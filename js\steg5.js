// steg5.js - <PERSON>tal glas (2-glas eller 3-glas)

(() => {
  // Konstanter för glastyper - definiera globalt så att alla funktioner kan använda dem
  window.GLASS_LABELS = {
    'double': '2-glas',
    'triple': '3-glas'
  };

  // --- Steg 5: Antal glas-knappar & live-render i "Ny design" ---
  // Definiera specBody globalt så att den är tillgänglig för renderGlassTypeSpec
  let specBody;

  document.addEventListener('DOMContentLoaded', () => {
    const glassBtns = document.querySelectorAll('.glass-option-btn');
    specBody = document.querySelector('#specTable tbody');
    window.selectedGlassType = null;

    // Gör funktionen tillgänglig globalt så att den kan anropas från loadConfiguration
    window.renderGlassTypeSpec = function() {
      // Kontrollera att specBody är tillgänglig
      if (!specBody) {
        specBody = document.querySelector('#specTable tbody');
        if (!specBody) return; // Om specBody fortfarande inte finns, avbryt
      }

      const old = specBody.querySelector('tr[data-key="glassType"]');
      if (old) old.remove();
      if (!window.selectedGlassType) return;

      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'glassType');
      tr.innerHTML = `<td>Antal glas</td><td>${window.GLASS_LABELS[window.selectedGlassType] || window.selectedGlassType}</td>`;
      specBody.appendChild(tr);

      // För felsökning
      console.log('renderGlassTypeSpec anropad med:', window.selectedGlassType);
    }

    glassBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        glassBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        window.selectedGlassType = btn.dataset.value;
        window.renderGlassTypeSpec();
      });
    });

    if (typeof window.updateSpecTable === 'function') {
      const orig = window.updateSpecTable;
      window.updateSpecTable = () => {
        orig();
        window.renderGlassTypeSpec();
      };
    }

    // Spara glastyp tillsammans med resten
    const saveBtn = document.querySelector('#section-7 .save-btn'); // Steg 8 (section-7)
    if (saveBtn) { // Kontrollera att knappen finns
      saveBtn.addEventListener('click', () => {
        setTimeout(() => {
          const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
          const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
          if (idx >= 0) {
            arr[idx].glassType = window.selectedGlassType;
            localStorage.setItem('myWindows', JSON.stringify(arr));
          }
        }, 0);
      });
    } else {
      console.warn("Kunde inte hitta save-knappen i section-7 (steg 8)");
    }
  });

  // --- Patch av showSavedDetail för "Mina sparade designs" ---
  function patchShowSavedDetail() {
    if (typeof window.showSavedDetail !== 'function') {
      // Om inte definierad än, testa igen om 50ms
      return setTimeout(patchShowSavedDetail, 50);
    }

    const orig = window.showSavedDetail;
    window.showSavedDetail = (cfg, idx) => {
      orig(cfg, idx);

      // Hämta <tbody> i sparad-vy
      const tbody = document.querySelector('#savedDetail .saved-spec tbody');
      if (!tbody) return;

      // Ta bort ev. tidigare glastyp-rad
      const existing = tbody.querySelector('tr[data-key="glassType"]');
      if (existing) existing.remove();

      // Skapa och lägg till glastyp-rad om det finns
      if (cfg.glassType) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'glassType');
        tr.innerHTML = `<td>Antal glas</td><td>${window.GLASS_LABELS[cfg.glassType] || cfg.glassType}</td>`;
        tbody.appendChild(tr);
      }
    };
  }

  // Kör patchen efter att hela sidan laddat
  document.addEventListener('DOMContentLoaded', patchShowSavedDetail);

})();
