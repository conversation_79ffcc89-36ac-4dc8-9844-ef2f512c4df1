/**
 * Admin JavaScript för Isofönster Konfigurator
 */

(function($) {
    'use strict';

    // Admin objekt
    window.IsofonsterAdmin = {
        
        // Initialisera admin
        init: function() {
            this.bindEvents();
            this.initTabs();
            this.initTooltips();
            console.log('Isofönster Admin initialiserad');
        },
        
        // Bind event handlers
        bindEvents: function() {
            // Media uploader
            this.initMediaUploader();
            
            // Färgförhandsvisning
            this.initColorPreview();
            
            // Formulärvalidering
            this.initFormValidation();
            
            // Bekräftelsedialoger
            this.initConfirmDialogs();
            
            // AJAX-formulär
            this.initAjaxForms();
        },
        
        // Initialisera media uploader
        initMediaUploader: function() {
            $(document).on('click', '.isofonster-media-upload', function(e) {
                e.preventDefault();
                
                var button = $(this);
                var targetInput = button.data('target');
                
                var mediaUploader = wp.media({
                    title: button.data('title') || 'Välj bild',
                    button: {
                        text: button.data('button-text') || 'Använd denna bild'
                    },
                    multiple: false
                });
                
                mediaUploader.on('select', function() {
                    var attachment = mediaUploader.state().get('selection').first().toJSON();
                    $(targetInput).val(attachment.url);
                    
                    // Visa förhandsvisning om det finns en preview-container
                    var previewContainer = button.siblings('.media-preview');
                    if (previewContainer.length) {
                        previewContainer.html('<img src="' + attachment.url + '" style="max-width: 200px; max-height: 100px;" alt="Preview" />');
                    }
                    
                    // Trigga change event
                    $(targetInput).trigger('change');
                });
                
                mediaUploader.open();
            });
        },
        
        // Initialisera färgförhandsvisning
        initColorPreview: function() {
            $(document).on('change', 'input[type="color"]', function() {
                var color = $(this).val();
                var previewClass = $(this).data('preview-class');
                
                if (previewClass) {
                    // Ta bort befintlig preview-stil
                    $('#color-preview-' + previewClass).remove();
                    
                    // Lägg till ny preview-stil
                    $('<style id="color-preview-' + previewClass + '">' + 
                      '.' + previewClass + ' { background-color: ' + color + ' !important; }' + 
                      '</style>').appendTo('head');
                }
            });
        },
        
        // Initialisera formulärvalidering
        initFormValidation: function() {
            $('form.isofonster-form').on('submit', function(e) {
                var form = $(this);
                var isValid = true;
                
                // Validera obligatoriska fält
                form.find('[required]').each(function() {
                    var field = $(this);
                    var value = field.val().trim();
                    
                    if (!value) {
                        isValid = false;
                        field.addClass('error');
                        IsofonsterAdmin.showFieldError(field, 'Detta fält är obligatoriskt');
                    } else {
                        field.removeClass('error');
                        IsofonsterAdmin.hideFieldError(field);
                    }
                });
                
                // Validera email-fält
                form.find('input[type="email"]').each(function() {
                    var field = $(this);
                    var value = field.val().trim();
                    
                    if (value && !IsofonsterAdmin.isValidEmail(value)) {
                        isValid = false;
                        field.addClass('error');
                        IsofonsterAdmin.showFieldError(field, 'Ange en giltig e-postadress');
                    }
                });
                
                // Validera URL-fält
                form.find('input[type="url"]').each(function() {
                    var field = $(this);
                    var value = field.val().trim();
                    
                    if (value && !IsofonsterAdmin.isValidUrl(value)) {
                        isValid = false;
                        field.addClass('error');
                        IsofonsterAdmin.showFieldError(field, 'Ange en giltig URL');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    IsofonsterAdmin.showNotice('Vänligen korrigera felen i formuläret', 'error');
                }
            });
        },
        
        // Initialisera bekräftelsedialoger
        initConfirmDialogs: function() {
            $(document).on('click', '[data-confirm]', function(e) {
                var message = $(this).data('confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        },
        
        // Initialisera AJAX-formulär
        initAjaxForms: function() {
            $(document).on('submit', '.isofonster-ajax-form', function(e) {
                e.preventDefault();
                
                var form = $(this);
                var submitButton = form.find('[type="submit"]');
                var originalText = submitButton.text();
                
                // Visa loading state
                submitButton.prop('disabled', true).text('Bearbetar...');
                form.addClass('isofonster-loading');
                
                $.ajax({
                    url: form.attr('action') || ajaxurl,
                    type: form.attr('method') || 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        if (response.success) {
                            IsofonsterAdmin.showNotice(response.data.message || 'Åtgärden slutfördes', 'success');
                            
                            // Trigga custom event
                            form.trigger('isofonster:ajax-success', [response]);
                        } else {
                            IsofonsterAdmin.showNotice(response.data || 'Ett fel uppstod', 'error');
                        }
                    },
                    error: function() {
                        IsofonsterAdmin.showNotice('Ett fel uppstod vid kommunikation med servern', 'error');
                    },
                    complete: function() {
                        // Återställ loading state
                        submitButton.prop('disabled', false).text(originalText);
                        form.removeClass('isofonster-loading');
                    }
                });
            });
        },
        
        // Initialisera tabs
        initTabs: function() {
            $('.isofonster-tabs').each(function() {
                var tabContainer = $(this);
                var tabNav = tabContainer.find('.isofonster-tab-nav');
                var tabContents = tabContainer.find('.isofonster-tab-content');
                
                // Visa första tab som standard
                tabNav.find('button:first').addClass('active');
                tabContents.first().addClass('active');
                
                // Tab navigation
                tabNav.on('click', 'button', function(e) {
                    e.preventDefault();
                    
                    var button = $(this);
                    var targetTab = button.data('tab');
                    
                    // Uppdatera navigation
                    tabNav.find('button').removeClass('active');
                    button.addClass('active');
                    
                    // Visa rätt innehåll
                    tabContents.removeClass('active');
                    tabContainer.find('[data-tab-content="' + targetTab + '"]').addClass('active');
                });
            });
        },
        
        // Initialisera tooltips
        initTooltips: function() {
            // Enkel tooltip-implementation
            $(document).on('mouseenter', '[data-tooltip]', function() {
                var element = $(this);
                var tooltipText = element.data('tooltip');
                
                if (!tooltipText) return;
                
                var tooltip = $('<div class="isofonster-tooltip-popup">' + tooltipText + '</div>');
                $('body').append(tooltip);
                
                var elementOffset = element.offset();
                var elementWidth = element.outerWidth();
                var elementHeight = element.outerHeight();
                var tooltipWidth = tooltip.outerWidth();
                var tooltipHeight = tooltip.outerHeight();
                
                tooltip.css({
                    position: 'absolute',
                    top: elementOffset.top - tooltipHeight - 5,
                    left: elementOffset.left + (elementWidth / 2) - (tooltipWidth / 2),
                    zIndex: 9999
                });
                
                element.data('tooltip-element', tooltip);
            });
            
            $(document).on('mouseleave', '[data-tooltip]', function() {
                var element = $(this);
                var tooltip = element.data('tooltip-element');
                
                if (tooltip) {
                    tooltip.remove();
                    element.removeData('tooltip-element');
                }
            });
        },
        
        // Hjälpfunktioner
        isValidEmail: function(email) {
            var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },
        
        isValidUrl: function(url) {
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        },
        
        showFieldError: function(field, message) {
            var errorElement = field.siblings('.field-error');
            
            if (errorElement.length === 0) {
                errorElement = $('<div class="field-error"></div>');
                field.after(errorElement);
            }
            
            errorElement.text(message).show();
        },
        
        hideFieldError: function(field) {
            field.siblings('.field-error').hide();
        },
        
        showNotice: function(message, type) {
            type = type || 'info';
            
            var notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            
            // Hitta rätt plats att visa meddelandet
            var target = $('.wrap h1').first();
            if (target.length) {
                target.after(notice);
            } else {
                $('.wrap').prepend(notice);
            }
            
            // Auto-hide efter 5 sekunder
            setTimeout(function() {
                notice.fadeOut(function() {
                    notice.remove();
                });
            }, 5000);
        },
        
        // Progress bar
        updateProgress: function(selector, percentage) {
            var progressBar = $(selector).find('.isofonster-progress-bar');
            progressBar.css('width', percentage + '%');
        },
        
        // Loading state
        setLoading: function(element, loading) {
            if (loading) {
                element.addClass('isofonster-loading');
            } else {
                element.removeClass('isofonster-loading');
            }
        }
    };

    // Initialisera när DOM är redo
    $(document).ready(function() {
        IsofonsterAdmin.init();
    });

})(jQuery);

// CSS för tooltips och andra admin-element
jQuery(document).ready(function($) {
    // Lägg till CSS för tooltips
    if (!$('#isofonster-admin-css').length) {
        $('<style id="isofonster-admin-css">' +
          '.isofonster-tooltip-popup {' +
          '  background: #333;' +
          '  color: #fff;' +
          '  padding: 5px 10px;' +
          '  border-radius: 3px;' +
          '  font-size: 12px;' +
          '  white-space: nowrap;' +
          '  box-shadow: 0 2px 5px rgba(0,0,0,0.2);' +
          '}' +
          '.field-error {' +
          '  color: #dc3232;' +
          '  font-size: 12px;' +
          '  margin-top: 5px;' +
          '}' +
          '.isofonster-loading {' +
          '  opacity: 0.6;' +
          '  pointer-events: none;' +
          '}' +
          'input.error, select.error, textarea.error {' +
          '  border-color: #dc3232 !important;' +
          '}' +
          '</style>').appendTo('head');
    }
});
