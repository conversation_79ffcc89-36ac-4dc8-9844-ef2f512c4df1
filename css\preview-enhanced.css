/**
 * Enhanced Preview Styles
 * Förbättrade stilar för fönsterförhandsvisning
 */

/* Preview container - Endast karmyttermått */
.preview-container {
  background: transparent; /* Transparent bakgrund */
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

/* Preview element - Karm */
.preview {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  background: rgba(60, 60, 60, 0.95); /* Mörk karmfärg */
  position: relative;
  box-shadow:
    inset 0 0 20px rgba(0, 0, 0, 0.2);

  /* Karm */
  border: 10px solid rgba(50, 50, 50, 0.95);
  box-sizing: border-box;
  padding: 0;

  /* Trästruktur i karmen */
  --wood-color: rgba(50, 50, 50, 0.95);
  --wood-grain:
    linear-gradient(90deg,
      var(--wood-color) 0%,
      rgba(70, 70, 70, 0.95) 20%,
      var(--wood-color) 40%,
      rgba(70, 70, 70, 0.95) 60%,
      var(--wood-color) 80%,
      rgba(70, 70, 70, 0.95) 100%);
  background-image: var(--wood-grain);
  background-size: 100% 100%;
}

/* Window frame - Fönsterbåge */
.window-frame {
  flex: 1;
  height: 100%;
  position: relative;
  background: rgba(65, 65, 65, 0.95);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
  padding: 0;
  margin: 0;

  /* Mittpost mellan lufterna */
  border-right: 8px solid var(--wood-color);

  /* Trästruktur i bågen */
  background-image: var(--wood-grain);
  background-size: 100% 100%;

  /* Fönsterbåge runt glaset */
  border-top: 8px solid var(--wood-color);
  border-bottom: 8px solid var(--wood-color);
  border-left: 8px solid var(--wood-color);
}

/* Spröjs borttagen enligt begäran */

/* Sista fönsterbågen ska ha höger-balk för konsekvent karm */
.window-frame:last-child {
  border-right: 8px solid var(--wood-color);
}

/* Första fönsterbågen ska inte ha vänster-balk */
.window-frame:first-child {
  border-left: none;
}

/* Window pane - Glasruta */
.pane {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  background: rgba(150, 170, 190, 0.1);
  box-shadow:
    inset 0 0 30px rgba(200, 200, 220, 0.1),
    0 0 5px rgba(0, 0, 0, 0.1);
  /* Glaseffekt */
  backdrop-filter: blur(0.3px);
  z-index: 2;
  border: 1px solid rgba(100, 100, 100, 0.3);
}

/* Sista fönsterbågens glasruta */
.window-frame:last-child .pane {
  right: 8px; /* Samma avstånd som övriga sidor för konsekvent karm */
}

/* Vi använder inte dessa pseudoelement för glasrutorna eftersom de används för reflektioner */

/* Glass reflection effect - Realistisk reflektion */
.pane::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0) 40%,
      rgba(255, 255, 255, 0.1) 60%,
      rgba(255, 255, 255, 0.2) 100%
    );
  pointer-events: none;
}

/* Ytterligare glaseffekt för djup */
.pane::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(
      45deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.05) 20%,
      rgba(173, 216, 230, 0.1) 40%,
      rgba(173, 216, 230, 0.05) 60%,
      rgba(255, 255, 255, 0) 100%
    );
  pointer-events: none;
  opacity: 0.7;
}

/* SVG lines - Öppningslinjer */
.frame-svg {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  bottom: 8px;
  width: calc(100% - 16px);
  height: calc(100% - 16px);
  pointer-events: none;
  z-index: 3;
  /* Säkerställ att SVG-koordinatsystemet är korrekt */
  transform: none;
  overflow: visible;
}

/* Sista fönsterbågens SVG */
.window-frame:last-child .frame-svg {
  width: calc(100% - 16px);
  right: 8px;
}

.frame-svg line {
  stroke: rgba(180, 180, 180, 0.7);
  stroke-width: 2;
  transition: stroke 0.2s ease;
  /* Skuggeffekt för linjerna */
  filter: drop-shadow(0px 0px 1px rgba(0, 0, 0, 0.3));
  /* Säkerställ att linjerna renderas korrekt */
  vector-effect: non-scaling-stroke;
}

.frame-svg line[stroke-dasharray] {
  stroke-dasharray: 4,4;
  stroke: rgba(160, 160, 160, 0.6);
}

/* Window handle - Fönsterhandtag */
.handle {
  position: absolute;
  width: 12px;
  height: 30px;
  background: linear-gradient(180deg, #909090 0%, #707070 50%, #909090 100%);
  border-radius: 4px;
  box-shadow:
    inset 0 2px 6px rgba(255, 255, 255, 0.4),
    inset 0 -2px 6px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: transform 0.2s ease, background 0.2s ease;
  z-index: 5;
  /* Metallisk effekt */
  background-image:
    linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 40%,
      rgba(255, 255, 255, 0.3) 60%,
      rgba(255, 255, 255, 0) 100%);
}

/* Handle knob - Handtagsknapp */
.handle::before {
  content: "";
  position: absolute;
  left: 50%;
  top: 4px;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: radial-gradient(circle at center, #888 0%, #666 60%, #444 100%);
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Handle base - Handtagsbas */
.handle::after {
  content: "";
  position: absolute;
  left: 50%;
  bottom: 3px;
  transform: translateX(-50%);
  width: 14px;
  height: 6px;
  background: radial-gradient(ellipse at center, #a0a0a0 0%, #808080 100%);
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Handle positions - Placerade på karmen utanför glaset */
.handle.side-left,
.handle.side-right {
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
}

.handle.side-left {
  left: -6px; /* Flyttad längre ut på karmen */
}

.handle.side-right {
  right: -6px; /* Flyttad längre ut på karmen, synkad med övriga handtag */
}

.handle.top-center {
  top: -6px; /* Flyttad längre ut på karmen */
  left: 50%;
  transform: translate(-50%, 0) rotate(90deg);
  z-index: 5;
}

.handle.bottom-center {
  bottom: -6px; /* Flyttad längre ut på karmen */
  left: 50%;
  transform: translate(-50%, 0) rotate(90deg);
  z-index: 5;
}

/* Hover effects - Interaktiva effekter */
.window-frame:hover {
  transform: scale(1.01);
  background-color: rgba(80, 80, 80, 0.95);
}

.window-frame:hover .pane {
  background: rgba(150, 170, 190, 0.15);
  box-shadow:
    inset 0 0 40px rgba(200, 200, 220, 0.15),
    0 0 8px rgba(0, 0, 0, 0.15);
}

.window-frame:hover .handle {
  background: linear-gradient(180deg, #a0a0a0 0%, #808080 50%, #a0a0a0 100%);
  box-shadow:
    inset 0 2px 6px rgba(255, 255, 255, 0.5),
    inset 0 -2px 6px rgba(0, 0, 0, 0.3),
    0 6px 10px rgba(0, 0, 0, 0.4);
  transform: translateY(-50%) scale(1.05);
}

.window-frame:hover .handle.top-center {
  transform: translate(-50%, 0) rotate(90deg) scale(1.05);
}

.window-frame:hover .handle.bottom-center {
  transform: translate(-50%, 0) rotate(90deg) scale(1.05);
}

.window-frame:hover .frame-svg line {
  stroke: rgba(200, 200, 200, 0.8);
  stroke-width: 1.8;
}

/* Hover på hela preview-containern */
.preview-container:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 3D-preview */
.preview3d {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  background-color: #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
}

.preview3d canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.preview3d.hidden {
  display: none;
}

/* Dölj 2D-preview när 3D-preview visas */
.preview.hidden {
  display: none !important;
}

/* 3D-preview knapp */
.preview-3d-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.preview-3d-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.preview-3d-btn svg {
  width: 16px;
  height: 16px;
}

/* 3D-preview kontroller */
.preview-3d-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 10;
}

.preview-3d-controls.hidden {
  display: none;
}

.control-btn {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.control-btn svg {
  width: 20px;
  height: 20px;
}

/* Vy-etikett */
.view-label {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 16px;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: opacity 0.3s ease;
  opacity: 1;
}

.view-label.hidden {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .preview-container {
    padding: 1rem;
  }

  .preview-container::before {
    display: none;
  }

  .window-frame {
    margin: 0 2px;
  }

  .handle {
    width: 6px;
    height: 30px;
  }
}
