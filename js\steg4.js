// js/sections/steg-4.js

(function(){
    // Etiketter för material
    const LABELS = {
      tra: 'Trä',
      pvc: 'PVC',
      aluminium: 'Aluminium',
      'tra-aluminium': 'Trä/Aluminium',
      'pvc-aluminium': 'PVC/Aluminium'
    };

    // --- Steg 4: Material-knappar & live-render i "Ny design" ---
    document.addEventListener('DOMContentLoaded', () => {
      const materialBtns = document.querySelectorAll('.material-option-btn');
      const specBody     = document.querySelector('#specTable tbody');
      window.selectedMaterial = null;

      function renderMaterialSpec() {
        const old = specBody.querySelector('tr[data-key="material"]');
        if (old) old.remove();
        if (!window.selectedMaterial) return;
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'material');
        tr.innerHTML = `<td>Material</td><td>${LABELS[window.selectedMaterial]}</td>`;
        specBody.appendChild(tr);
      }

      materialBtns.forEach(btn => {
        btn.addEventListener('click', () => {
          materialBtns.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');
          window.selectedMaterial = btn.dataset.value;
          renderMaterialSpec();

          // Trigga en händelse för att meddela att materialet har ändrats
          console.log("Material ändrat till:", window.selectedMaterial);
          document.dispatchEvent(new CustomEvent('materialChanged'));
        });
      });

      if (typeof window.updateSpecTable === 'function') {
        const orig = window.updateSpecTable;
        window.updateSpecTable = () => {
          orig();
          renderMaterialSpec();
        };
      }

      // Spara material tillsammans med resten
      const saveBtn = document.querySelector('#section-7 .save-btn'); // Steg 8 (section-7)
      if (saveBtn) { // Kontrollera att knappen finns
        saveBtn.addEventListener('click', () => {
          setTimeout(() => {
            const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
            const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
            if (idx >= 0) {
              arr[idx].material = window.selectedMaterial;
              localStorage.setItem('myWindows', JSON.stringify(arr));
            }
          }, 0);
        });
      } else {
        console.warn("Kunde inte hitta save-knappen i section-7 (steg 8)");
      }
    });

    // --- Patch av showSavedDetail för "Mina sparade designs" ---
    function patchShowSavedDetail() {
      if (typeof window.showSavedDetail !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchShowSavedDetail, 50);
      }

      const orig = window.showSavedDetail;
      window.showSavedDetail = (cfg, idx) => {
        orig(cfg, idx);

        // Hämta <tbody> i sparad-vy
        const tbody = document.querySelector('#savedDetail .saved-spec tbody');
        if (!tbody) return;

        // Ta bort ev. tidigare material-rad
        const existing = tbody.querySelector('tr[data-key="material"]');
        if (existing) existing.remove();

        // Skapa och lägg till material-rad om det finns
        if (cfg.material) {
          const tr = document.createElement('tr');
          tr.setAttribute('data-key', 'material');
          tr.innerHTML = `<td>Material</td><td>${LABELS[cfg.material] || cfg.material}</td>`;
          tbody.appendChild(tr);
        }
      };
    }

    // Kör patchen efter att hela sidan laddat
    document.addEventListener('DOMContentLoaded', patchShowSavedDetail);

  })();
