/**
 * Admin CSS för <PERSON><PERSON>er Konfigurator
 */

/* Allmänna admin-stilar */
.isofonster-admin-dashboard {
    max-width: 1200px;
}

.isofonster-welcome-panel {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 23px 10px 0;
    position: relative;
    overflow: hidden;
}

.isofonster-stats-overview,
.isofonster-quick-actions,
.isofonster-recent-activity,
.isofonster-system-info {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.isofonster-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.isofonster-stat-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.isofonster-stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #2271b1;
    line-height: 1;
}

.isofonster-stat-label {
    margin-top: 8px;
    color: #646970;
    font-size: 0.9em;
}

.isofonster-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.isofonster-action-buttons .button {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.isofonster-activity-list {
    margin-top: 15px;
    color: #646970;
}

.isofonster-system-info table {
    margin-top: 15px;
}

.isofonster-system-info td {
    padding: 8px 12px;
}

/* Inställningssidor */
.isofonster-settings-container {
    max-width: 1000px;
}

.isofonster-settings-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.isofonster-settings-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.logo-preview {
    margin-top: 10px;
}

.isofonster-reset-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
    border-left: 4px solid #dc3232;
}

.isofonster-reset-section h2 {
    color: #dc3232;
    margin-top: 0;
}

/* Designs-sida */
.isofonster-designs-table {
    margin-top: 20px;
}

.isofonster-designs-table .column-preview {
    width: 100px;
}

.isofonster-designs-table .column-name {
    width: 25%;
}

.isofonster-designs-table .column-type {
    width: 15%;
}

.isofonster-designs-table .column-dimensions {
    width: 20%;
}

.isofonster-designs-table .column-created {
    width: 20%;
}

.isofonster-designs-table .column-actions {
    width: 20%;
}

.isofonster-design-preview {
    width: 80px;
    height: 60px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
}

.isofonster-design-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.isofonster-design-actions .button {
    font-size: 12px;
    padding: 2px 8px;
    height: auto;
    line-height: 1.4;
}

/* Statistik-sida */
.isofonster-chart-container {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.isofonster-chart {
    height: 300px;
    width: 100%;
}

/* Responsiv design */
@media (max-width: 768px) {
    .isofonster-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .isofonster-action-buttons {
        flex-direction: column;
    }
    
    .isofonster-action-buttons .button {
        justify-content: center;
    }
    
    .isofonster-design-actions {
        flex-direction: column;
    }
}

/* Loading states */
.isofonster-loading {
    opacity: 0.6;
    pointer-events: none;
}

.isofonster-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: isofonster-spin 1s linear infinite;
}

@keyframes isofonster-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Meddelanden */
.isofonster-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    background: #fff;
}

.isofonster-notice.notice-success {
    border-left-color: #46b450;
}

.isofonster-notice.notice-error {
    border-left-color: #dc3232;
}

.isofonster-notice.notice-warning {
    border-left-color: #ffb900;
}

.isofonster-notice.notice-info {
    border-left-color: #00a0d2;
}

/* Tooltips */
.isofonster-tooltip {
    position: relative;
    cursor: help;
}

.isofonster-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.isofonster-tooltip:hover::after {
    opacity: 1;
}

/* Tabs */
.isofonster-tabs {
    margin: 20px 0;
}

.isofonster-tab-nav {
    display: flex;
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 20px;
}

.isofonster-tab-nav button {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.isofonster-tab-nav button.active {
    border-bottom-color: #2271b1;
    color: #2271b1;
}

.isofonster-tab-nav button:hover {
    background: #f0f0f0;
}

.isofonster-tab-content {
    display: none;
}

.isofonster-tab-content.active {
    display: block;
}

/* Progress bars */
.isofonster-progress {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.isofonster-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #2271b1, #72aee6);
    transition: width 0.3s ease;
    border-radius: 10px;
}

/* Badges */
.isofonster-badge {
    display: inline-block;
    padding: 2px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 3px;
    color: #fff;
}

.isofonster-badge.badge-success {
    background: #46b450;
}

.isofonster-badge.badge-error {
    background: #dc3232;
}

.isofonster-badge.badge-warning {
    background: #ffb900;
}

.isofonster-badge.badge-info {
    background: #00a0d2;
}

/* Cards */
.isofonster-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    border-radius: 4px;
    padding: 20px;
    margin: 15px 0;
}

.isofonster-card-header {
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.isofonster-card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.isofonster-card-body {
    /* Innehåll */
}

.isofonster-card-footer {
    border-top: 1px solid #e1e1e1;
    padding-top: 15px;
    margin-top: 15px;
    text-align: right;
}
