=== Isofönster Konfigurator ===
Contributors: isofonster
Tags: configurator, windows, doors, elementor, 3d-preview
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

En avancerad fönster- och dörrkonfigurator för WordPress med full Elementor-integration.

== Description ==

Isofönster Konfigurator är ett kraftfullt WordPress-plugin som låter dina kunder designa och konfigurera fönster och dörrar direkt på din webbplats. Pluginet är optimerat för Elementor och kan enkelt integreras som en widget.

= Huvudfunktioner =

* **Fönsterkonfigurator** - Komplett verktyg för fönsterdesign
* **Dörrkonfigurator** - Avancerad dörrkonfiguration  
* **Elementor-integration** - Fungerar perfekt med Elementor Page Builder
* **Responsiv design** - Optimerad för alla enheter
* **3D-förhandsvisning** - Realistisk 3D-rendering med Three.js
* **PDF-export** - Exportera designs som professionella PDF-dokument
* **Sparfunktion** - Användare kan spara och hantera sina designs

= Designfunktioner =

* **Karmmått** - Anpassningsbara dimensioner
* **Antal lufter** - Konfigurera 1-10 lufter
* **Öppningstyper** - Fast, sidohängt, toppvippt
* **Material** - Olika materialval
* **Glastyper** - Enkelt, dubbelt, trippelglas
* **Färgval** - Inre och yttre färger
* **Spröjs** - Olika spröjstyper och anpassningar
* **Handtag** - Olika handtagstyper
* **Myggnät** - Tillval för myggnät

= Tekniska funktioner =

* **AJAX-baserad** - Smidig användarupplevelse
* **Säker datahantering** - WordPress nonces och sanitering
* **Databasintegration** - Sparar designs i WordPress-databas
* **Caching** - Optimerad prestanda
* **Lokalisering** - Redo för översättning

== Installation ==

= Automatisk installation =

1. Gå till Plugins > Lägg till ny i WordPress admin
2. Sök efter "Isofönster Konfigurator"
3. Klicka "Installera nu" och sedan "Aktivera"
4. Gå till "Isofönster" i admin-menyn för konfiguration

= Manuell installation =

1. Ladda ner plugin-filerna
2. Ladda upp till `/wp-content/plugins/isofonster-configurator/`
3. Aktivera pluginet i WordPress admin
4. Konfigurera inställningar under "Isofönster"

== Användning ==

= Shortcode =

Använd shortcode för att visa konfiguratorn:

`[isofonster_configurator]`

Med parametrar:
`[isofonster_configurator type="both" theme="default" width="100%" height="auto"]`

= Elementor Widget =

1. Öppna Elementor-editorn
2. Sök efter "Isofönster Konfigurator" i widget-panelen
3. Dra widgeten till din sida
4. Konfigurera inställningar i widget-panelen

== Frequently Asked Questions ==

= Fungerar pluginet med alla teman? =

Ja, pluginet är designat för att fungera med alla WordPress-teman. Det använder sina egna stilar och påverkar inte temats utseende.

= Krävs Elementor för att använda pluginet? =

Nej, pluginet fungerar utan Elementor genom shortcode. Elementor krävs endast för widget-funktionaliteten.

= Kan användare spara sina designs? =

Ja, användare kan spara sina designs och komma åt dem senare. Designs sparas i WordPress-databasen.

= Stöds 3D-förhandsvisning på alla enheter? =

3D-förhandsvisning kräver WebGL-stöd i webbläsaren. De flesta moderna webbläsare stöder detta.

= Kan jag anpassa utseendet? =

Ja, pluginet erbjuder omfattande anpassningsmöjligheter via inställningssidan och CSS.

== Screenshots ==

1. Konfigurator-val mellan fönster och dörr
2. Fönsterkonfigurator med steg-för-steg guide
3. 3D-förhandsvisning av design
4. Sparade designs-översikt
5. Elementor widget-inställningar
6. Admin-dashboard med statistik

== Changelog ==

= 1.0.0 =
* Initial release
* Fönsterkonfigurator med alla grundfunktioner
* Elementor-integration
* 3D-förhandsvisning
* Sparfunktion för designs
* PDF-export
* Admin-dashboard med statistik
* Responsiv design
* Säker datahantering

== Upgrade Notice ==

= 1.0.0 =
Initial release av Isofönster Konfigurator.

== Support ==

För support och dokumentation, besök:
* [Dokumentation](https://isofonster.se/docs)
* [Support](https://isofonster.se/support)
* [Kontakt](https://isofonster.se/kontakt)

== Credits ==

Utvecklat av Isofönster.se team.

Använder följande bibliotek:
* Three.js för 3D-rendering
* jsPDF för PDF-generering
* html2canvas för skärmdumpar

== Privacy Policy ==

Detta plugin sparar användardata endast när användare aktivt väljer att spara sina designs. All data lagras säkert i WordPress-databasen och kan tas bort av användaren när som helst.

Pluginet använder inga externa tjänster för datalagring och skickar ingen användardata till tredje part.

== Technical Requirements ==

* WordPress 5.0 eller senare
* PHP 7.4 eller senare
* MySQL 5.6 eller senare
* Elementor 3.0 eller senare (för widget-funktionalitet)
* Modern webbläsare med WebGL-stöd (för 3D-förhandsvisning)

== Developer Information ==

= Hooks och Filters =

Pluginet erbjuder flera hooks för utvecklare:

`// Filter för att modifiera plugin-inställningar
add_filter('isofonster_configurator_options', 'my_custom_options');

// Action när en design sparas
add_action('isofonster_design_saved', 'my_design_saved_callback');

// Filter för att modifiera PDF-export
add_filter('isofonster_pdf_data', 'my_custom_pdf_data');`

= API =

Pluginet exponerar ett JavaScript API för avancerad anpassning:

`// Lyssna på konfigurator-events
document.addEventListener('isofonster:design-updated', function(event) {
  console.log('Design uppdaterad:', event.detail);
});

// Anpassa konfigurator-beteende
IsofonsterConfigurator.config.customOption = 'värde';`

== License ==

Detta plugin är licensierat under GPL v2 eller senare.

Copyright © Isofonster.se 2025. Alla rättigheter förbehållna.
