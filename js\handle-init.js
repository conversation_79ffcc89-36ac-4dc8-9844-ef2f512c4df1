// handle-init.js - <PERSON><PERSON>t to ensure window.openings is properly initialized

(function() {
  // Function to initialize window.openings if it doesn't exist
  function initializeOpenings() {
    if (!window.openings) {
      window.openings = ['fixed']; // Default value
    }
  }

  // Run the initialization when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOpenings);
  } else {
    // DOM is already loaded
    initializeOpenings();
  }

  // Also run the initialization when the handle section is shown
  document.addEventListener('click', function(event) {
    if (event.target.classList.contains('step') && event.target.dataset.step === '8') {
      initializeOpenings();
    }
  });
})();
