<!DOCTYPE html>
<html lang="sv">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Fönsterpreview</title>
    <style>
        body { 
            margin: 0; 
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        #canvas-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        #info {
            position: absolute;
            bottom: 20px;
            width: 100%;
            text-align: center;
            color: #333;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 0 5px white;
        }
        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }
        .control-btn {
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid #ccc;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.9);
        }
        .control-btn svg {
            width: 24px;
            height: 24px;
        }
    </style>
</head>
<body>
    <div id="canvas-container">
        <div id="info">Utsida</div>
        <div id="controls">
            <div class="control-btn" id="rotate-btn" title="Rotera fönster">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 12a9 9 0 0 1-9 9"></path>
                    <path d="M3 12a9 9 0 0 1 9-9"></path>
                    <path d="M21 12H3"></path>
                    <path d="M12 3v18"></path>
                </svg>
            </div>
            <div class="control-btn" id="flip-btn" title="Växla insida/utsida">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M7 16V4m0 0L3 8m4-4l4 4"></path>
                    <path d="M17 8v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Three.js bibliotek -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    
    <!-- Vår 3D-preview kod -->
    <script src="js/3d-preview.js"></script>
</body>
</html>
