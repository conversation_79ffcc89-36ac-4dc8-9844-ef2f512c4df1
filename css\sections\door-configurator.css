/**
 * Door Configurator CSS
 * <PERSON><PERSON><PERSON> för dörrkonfiguratorn
 */

/* Grundläggande stilar för dörrkonfiguratorn */
.door-configurator {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Steg-navigering */
.door-configurator .step-nav-wrapper {
  margin-bottom: 1rem;
}

/* Byggare-container */
.door-configurator .builder-container {
  display: flex;
  gap: 1.5rem;
  width: 100%;
}

/* Kontrollkolumn */
.door-configurator .controls-column {
  flex: 0 0 40%;
  max-width: 40%;
  width: 40%;
}

/* Resultatkolumn */
.door-configurator .result-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Sektioner */
.door-configurator .builder-section {
  background: var(--color-panel);
  border-radius: var(--radius);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.door-configurator .builder-section.expanded {
  max-height: 1000px;
}

.door-configurator .builder-section.collapsed {
  max-height: 60px;
}

.door-configurator .builder-section h2 {
  padding: 1rem;
  margin: 0;
  font-size: 1.2rem;
  background: rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.door-configurator .section-body {
  padding: 1rem;
}

/* Dimensioner */
.door-configurator .dimension-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.door-configurator .dim-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.door-configurator .dim-label {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

.door-configurator .dim-input {
  width: 100%;
  padding: 0.5rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1rem;
}

.door-configurator .dim-hint {
  font-size: 0.8rem;
  color: #888;
}

/* Antal luft */
.door-configurator .quantity-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.door-configurator .door-qty-option-btn {
  flex: 1;
  padding: 0.75rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  cursor: pointer;
  transition: all var(--transition);
}

.door-configurator .door-qty-option-btn:hover {
  background: rgba(245, 199, 0, 0.1);
}

.door-configurator .door-qty-option-btn.active {
  background: var(--color-primary);
  color: #121212;
}

.door-configurator .custom-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.door-configurator .custom-control.hidden {
  display: none;
}

.door-configurator .qty-btn {
  width: 30px;
  height: 30px;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1.2rem;
  cursor: pointer;
}

.door-configurator .custom-input {
  width: 60px;
  padding: 0.5rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1rem;
  text-align: center;
}

/* Panel */
.door-configurator .panel-control {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.door-configurator .panel-label {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-weight: 600;
}

.door-configurator .panel-slider {
  flex: 1;
  height: 8px;
  background: var(--color-bg);
  border-radius: 4px;
  outline: none;
  -webkit-appearance: none;
}

.door-configurator .panel-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
}

.door-configurator .panel-input {
  width: 60px;
  padding: 0.5rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1rem;
  text-align: center;
}

.door-configurator .unit {
  font-size: 0.9rem;
  color: var(--color-secondary);
}

.door-configurator .panel-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius);
  font-size: 0.9rem;
}

.door-configurator .panel-info p {
  margin: 0.5rem 0;
}

/* Glastyp */
.door-configurator .door-glass-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.door-configurator .door-glass-option {
  background: var(--color-bg);
  border: 2px solid var(--color-border);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  transition: all var(--transition);
}

.door-configurator .door-glass-option:hover {
  background: rgba(245, 199, 0, 0.1);
  transform: translateY(-2px);
}

.door-configurator .door-glass-option.active {
  border-color: var(--color-primary);
  background: rgba(245, 199, 0, 0.1);
}

.door-configurator .door-glass-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.door-configurator .door-glass-title {
  font-size: 1.1rem;
  margin: 0 0 0.5rem 0;
  color: var(--color-secondary);
}

.door-configurator .door-glass-description {
  font-size: 0.9rem;
  margin: 0;
  color: #aaa;
}

/* Material */
.door-configurator .door-material-option-btn {
  flex: 1 1 calc(33.33% - 1rem);
  padding: 1rem 0.5rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition);
}

.door-configurator .door-material-option-btn:hover {
  background: rgba(245, 199, 0, 0.1);
  border-color: var(--color-primary);
  transform: translateY(-2px);
}

.door-configurator .door-material-option-btn.active {
  background: var(--color-primary);
  color: #121212;
}

/* Färg */
.door-configurator .door-color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.door-configurator .door-color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.door-configurator .door-color-option .color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  transition: all var(--transition);
}

.door-configurator .door-color-option:hover .color-swatch {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px var(--color-primary);
}

.door-configurator .door-color-option.active .color-swatch {
  box-shadow: 0 0 0 3px var(--color-primary);
}

.door-configurator .door-color-option .color-label {
  font-size: 0.9rem;
}

/* Dörr Preview */
.door-configurator #doorPreview {
  display: flex;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  background: rgba(60, 60, 60, 0.95);
  position: relative;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
  border: 10px solid rgba(50, 50, 50, 0.95);
  box-sizing: border-box;
  padding: 0;
}

/* Justera preview-container */
.door-configurator .preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* Vy-etikett borttagen */

.door-configurator .door-frame {
  display: flex;
  flex-direction: column;
  position: relative;
  background: rgba(65, 65, 65, 0.95);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
  padding: 0;
  margin: 0;
  border-right: 8px solid rgba(50, 50, 50, 0.95);
  border-top: 8px solid rgba(50, 50, 50, 0.95);
  border-bottom: 8px solid rgba(50, 50, 50, 0.95);
  border-left: 8px solid rgba(50, 50, 50, 0.95);
  /* Förbättra utseendet på dörren */
  min-width: 60px;
}

.door-configurator .door-frame:last-child {
  border-right: 8px solid rgba(50, 50, 50, 0.95);
}

.door-configurator .door-glass-pane {
  flex: 1;
  background: rgba(150, 170, 190, 0.2);
  border: 2px solid rgba(50, 50, 50, 0.95);
  box-shadow:
    inset 0 0 20px rgba(200, 200, 220, 0.1),
    inset 0 0 40px rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  margin: 8px;
}

/* Glasreflektioner */
.door-configurator .door-glass-pane::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  pointer-events: none;
}

.door-configurator .door-panel {
  background: rgba(50, 50, 50, 0.95);
  border-top: 2px solid rgba(70, 70, 70, 0.95);
  box-shadow:
    inset 0 2px 5px rgba(0, 0, 0, 0.3),
    inset 0 -2px 5px rgba(255, 255, 255, 0.1);
  position: relative;
}

/* Panel detaljer */
.door-configurator .door-panel::before {
  content: "";
  position: absolute;
  top: 10%;
  left: 5%;
  right: 5%;
  bottom: 10%;
  border: 1px solid rgba(70, 70, 70, 0.8);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

.door-configurator .door-handle {
  position: absolute;
  width: 16px;
  height: 60px;
  background: linear-gradient(180deg, #a0a0a0 0%, #808080 50%, #a0a0a0 100%);
  border-radius: 4px;
  box-shadow:
    inset 0 2px 6px rgba(255, 255, 255, 0.4),
    inset 0 -2px 6px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.5);
  top: 50%;
  transform: translateY(-50%);
  z-index: 5;
  background-image:
    linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 40%,
      rgba(255, 255, 255, 0.3) 60%,
      rgba(255, 255, 255, 0) 100%);
}

/* Handtagets knopp */
.door-configurator .door-handle::before {
  content: "";
  position: absolute;
  width: 24px;
  height: 12px;
  background: linear-gradient(90deg, #a0a0a0 0%, #c0c0c0 50%, #a0a0a0 100%);
  border-radius: 6px;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.door-configurator .door-handle.handle-left {
  left: 15px;
}

.door-configurator .door-handle.handle-left::before {
  left: 12px;
}

.door-configurator .door-handle.handle-right {
  right: 15px;
}

.door-configurator .door-handle.handle-right::before {
  right: 12px;
}

/* Skjutparti */
.door-configurator .door-handle.handle-sliding {
  width: 20px;
  height: 40px;
  border-radius: 10px;
}

.door-configurator .door-handle.handle-sliding::before {
  width: 10px;
  height: 20px;
  border-radius: 5px;
}

/* Placering av handtag baserat på skjutriktning */
.door-configurator .door-handle.handle-sliding.sliding-right {
  right: 15px;
}

.door-configurator .door-handle.handle-sliding.sliding-right::before {
  right: 5px;
}

.door-configurator .door-handle.handle-sliding.sliding-left {
  left: 15px;
}

.door-configurator .door-handle.handle-sliding.sliding-left::before {
  left: 5px;
}

.door-configurator .sliding-door {
  position: relative;
}

/* Enkel markering för skjutpartier */
.door-configurator .sliding-door.sliding-right::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-right: 2px solid rgba(180, 180, 180, 0.3);
  pointer-events: none;
}

.door-configurator .sliding-door.sliding-left::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-left: 2px solid rgba(180, 180, 180, 0.3);
  pointer-events: none;
}

/* Riktningskontroller (gemensamma stilar) */
.door-configurator .sliding-direction-label,
.door-configurator .side-direction-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.door-configurator .sliding-direction-options,
.door-configurator .side-direction-options {
  display: flex;
  gap: 0.5rem;
}

.door-configurator .sliding-direction-btn,
.door-configurator .side-direction-btn {
  flex: 1;
  padding: 0.5rem;
  background: var(--color-bg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  cursor: pointer;
  transition: all var(--transition);
}

.door-configurator .sliding-direction-btn:hover,
.door-configurator .side-direction-btn:hover {
  background: rgba(245, 199, 0, 0.1);
}

.door-configurator .sliding-direction-btn.active,
.door-configurator .side-direction-btn.active {
  background: var(--color-primary);
  color: #121212;
}

/* Knappar */
.door-configurator .builder-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
}

.door-configurator .btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary);
  border: none;
  border-radius: var(--radius);
  color: #121212;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition);
}

.door-configurator .btn:hover {
  background: var(--color-primary-dark);
}

.door-configurator .prev-btn {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-secondary);
}

.door-configurator .prev-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* SVG-container för öppningslinjer */
.door-configurator .door-svg-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 4;
}

/* Specifikationstabell */
.door-configurator .spec-container {
  background: var(--color-panel);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.5);
  margin-top: 20px;
}

.door-configurator .spec-container h3 {
  margin-bottom: 1rem;
  color: var(--color-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

.door-configurator #doorSpecTable {
  width: 100%;
  border-collapse: collapse;
}

.door-configurator #doorSpecTable th,
.door-configurator #doorSpecTable td {
  border: 1px solid var(--color-border);
  padding: 0.75rem 1rem;
  text-align: left;
  font-size: 1rem;
  color: var(--color-secondary);
}

.door-configurator #doorSpecTable tr:hover td {
  background: rgba(245, 199, 0, 0.05);
}
