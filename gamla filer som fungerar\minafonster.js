// minafonster.js

// ─── Mina fönster-overlay & storage ────────────────────
document.addEventListener('DOMContentLoaded', () => {
  const myWindowsBtn = document.getElementById('myWindowsBtn');
  const overlay      = document.getElementById('overlay');
  const closeOverlay = document.getElementById('closeOverlay');
  const windowList   = document.getElementById('windowList');
  const saveCurrent  = document.getElementById('saveCurrent');
  const storageKey   = 'myWindows';

  function renderWindowList() {
    const arr = JSON.parse(localStorage.getItem(storageKey) || '[]');
    windowList.innerHTML = '';
    arr.forEach((cfg, idx) => {
      const li = document.createElement('li');
      li.innerHTML = `
        <span class="title">${cfg.name}</span>
        <button class="rename-btn" data-index="${idx}">✎</button>
        <button class="edit-btn"   data-index="${idx}">↻</button>
        <button class="delete-btn" data-index="${idx}">🗑</button>
      `;
      windowList.append(li);
    });

    // Radera
    windowList.querySelectorAll('.delete-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const i   = +btn.dataset.index;
        const arr = JSON.parse(localStorage.getItem(storageKey) || '[]');
        arr.splice(i, 1);
        localStorage.setItem(storageKey, JSON.stringify(arr));
        renderWindowList();
      });
    });

    // Byt namn
    windowList.querySelectorAll('.rename-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const i       = +btn.dataset.index;
        const arr     = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const newName = prompt('Byt namn på fönstret:', arr[i].name);
        if (newName) {
          arr[i].name = newName;
          localStorage.setItem(storageKey, JSON.stringify(arr));
          renderWindowList();
        }
      });
    });

    // Ladda/edita
    windowList.querySelectorAll('.edit-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const i   = +btn.dataset.index;
        const arr = JSON.parse(localStorage.getItem(storageKey) || '[]');
        const cfg = arr[i];
        window.loadConfiguration(cfg);
        hideOverlay();
      });
    });
  }

  function showOverlay() {
    renderWindowList();
    overlay.classList.remove('hidden');
  }
  function hideOverlay() {
    overlay.classList.add('hidden');
  }

  myWindowsBtn.addEventListener('click', showOverlay);
  closeOverlay.addEventListener('click', hideOverlay);
  overlay.addEventListener('click', e => {
    if (e.target === overlay) hideOverlay();
  });

  saveCurrent.addEventListener('click', () => {
    let name = prompt('Namnge fönstret:', 'Nytt fönster');
    if (!name) name = 'Odefinierat fönster';
    const arr = JSON.parse(localStorage.getItem(storageKey) || '[]');
    const cfg = {
      name,
      width:     window.windowWidthCm,
      height:    window.windowHeightCm,
      count:     window.count,
      openings:  window.openings,
      hinges:    window.hinges,
      openSizes: window.openSizes      // Spara alla storlekar per luft
    };
    arr.push(cfg);
    localStorage.setItem(storageKey, JSON.stringify(arr));
    renderWindowList();
  });

  // ─── Exponera API för att ladda in konfiguration ───────
  window.goToSection = step => {
    document.querySelector(`.step-nav .step[data-step="${step}"]`).click();
  };

  window.loadConfiguration = cfg => {
    // 1) Sätt karmmått
    const wIn        = document.getElementById('widthInput');
    const hIn        = document.getElementById('heightInput');
    const widthLabel = document.getElementById('widthLabel');
    const heightLabel= document.getElementById('heightLabel');
  
    wIn.value = cfg.width;
    window.windowWidthCm  = cfg.width;
    widthLabel.textContent = `${cfg.width} cm`;
    wIn.dispatchEvent(new Event('input'));
  
    hIn.value = cfg.height;
    window.windowHeightCm = cfg.height;
    heightLabel.textContent = `${cfg.height} cm`;
    hIn.dispatchEvent(new Event('input'));
  
    // 2) Tvinga fullständig uppdatering
    window.updateAll();
  
    // 3) Sätt antal luft
    const qtyBtn = Array.from(document.querySelectorAll('.qty-option-btn'))
                       .find(b => +b.dataset.value === cfg.count);
    if (qtyBtn) qtyBtn.click();
  
    // 4) Hoppa till Steg 3
    window.goToSection(2);
  
    // 5) Fyll i öppningar, gångjärn och storlekar
    setTimeout(() => {
      cfg.openings.forEach((op, i) => {
        const wrap = document.querySelectorAll('#openingsContainer .opening-wrap')[i];
        wrap.querySelector(`.type-btn[data-value="${op}"]`).click();
        if (op === 'side-hung' && cfg.hinges[i]) {
          wrap.querySelector(`.hinge-btn[data-value="${cfg.hinges[i]}"]`).click();
        }
        if (i < cfg.count - 1) {
          const inp = wrap.querySelector('.size-input');
          inp.value = cfg.openSizes[i];
          inp.dispatchEvent(new Event('input'));
        }
      });
    }, 50);
  };
});
