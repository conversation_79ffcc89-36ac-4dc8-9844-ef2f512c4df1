/**
 * steg7.js - <PERSON><PERSON>rg-steget i fönsterkonfiguratorn
 * Hanterar val av färg för fönstret, både insida och utsida
 */

(function() {
  // Vänta på att DOM är laddad
  document.addEventListener('DOMContentLoaded', function() {
    // Globala variabler för färgval
    window.selectedInsideColor = 'white'; // Standard: vit
    window.selectedOutsideColor = 'white'; // Standard: vit

    // Färgkoder för 3D-modellen
    const COLOR_HEX = {
      white: 0xffffff,
      gray: 0x808080,
      black: 0x222222,  // Mindre intensiv svart färg
      anthracite: 0x383c3f, // Antracitgrå
      darkgray: 0x505050, // Mörkgrå
      silver: 0xc0c0c0, // Silver
      beige: 0xf5f5dc // Beige
    };

    // Färgetiketter för specifikationstabellen
    window.COLOR_LABELS = {
      white: 'Vit',
      gray: '<PERSON><PERSON><PERSON>',
      black: '<PERSON>vart',
      anthracite: 'Antracitgrå',
      darkgray: 'Mörkgr<PERSON>',
      silver: 'Silver',
      beige: 'Beige'
    };

    // Referens till färgvalsalternativen
    const colorOptions = document.querySelectorAll('.color-option');
    const outsideColorSection = document.getElementById('outsideColorSection');

    // Lyssna på materialval för att visa/dölja utsida färgval
    function checkMaterialForOutsideColor() {
      // Visa utsida färgval endast om materialet är trä/alu eller pvc/alu
      const hasAluCladding = window.selectedMaterial === 'tra-aluminium' ||
                            window.selectedMaterial === 'pvc-aluminium';

      console.log("Kontrollerar material för utsidans färg:", window.selectedMaterial, "Har alu-beklädnad:", hasAluCladding);

      if (hasAluCladding) {
        outsideColorSection.classList.remove('hidden');
      } else {
        outsideColorSection.classList.add('hidden');
      }

      // Uppdatera 3D-modellen efter att utsidans färgval har visats/dolts
      updateModelColors();

      // Uppdatera specifikationstabellen
      updateColorSpec();
    }

    // Lyssna på materialändringar
    document.addEventListener('materialChanged', function() {
      console.log("Material ändrat event mottaget");
      checkMaterialForOutsideColor();
    });

    // Kontrollera även när steget visas
    const colorStep = document.querySelector('.step[data-step="6"]');
    if (colorStep) {
      colorStep.addEventListener('click', function() {
        console.log("Färgsteget aktiverat");
        setTimeout(checkMaterialForOutsideColor, 100);
      });
    } else {
      console.warn("Kunde inte hitta färgsteget (data-step=6)");
    }

    // Lägg till klickhändelser för färgalternativen
    colorOptions.forEach(option => {
      option.addEventListener('click', function() {
        const colorValue = this.dataset.value;
        const side = this.dataset.side;

        // Uppdatera aktiv klass för rätt sida
        document.querySelectorAll(`.color-option[data-side="${side}"]`).forEach(opt => {
          opt.classList.remove('active');
        });
        this.classList.add('active');

        // Spara vald färg
        if (side === 'inside') {
          window.selectedInsideColor = colorValue;
        } else {
          window.selectedOutsideColor = colorValue;
        }

        // Uppdatera 3D-modellen
        updateModelColors();

        // Uppdatera specifikationstabellen
        updateColorSpec();

        // Trigga en händelse för att meddela att färgen har ändrats
        document.dispatchEvent(new CustomEvent('colorChanged'));
      });
    });

    // Uppdatera 3D-modellen med valda färger
    function updateModelColors() {
      // Kontrollera om 3D-modellen är initialiserad
      if (!window.Preview3D) {
        console.log("3D-preview är inte tillgänglig (Preview3D)");
        return;
      }

      // Skapa konfigurationsobjekt för färguppdatering
      const colorConfig = {
        frameColor: COLOR_HEX[window.selectedInsideColor],
        insideColor: COLOR_HEX[window.selectedInsideColor]
      };

      // Lägg till utsidans färg om den är synlig
      if (!outsideColorSection.classList.contains('hidden')) {
        colorConfig.outsideColor = COLOR_HEX[window.selectedOutsideColor];
      } else {
        // Om utsidan inte är valbar, använd samma färg som insidan
        colorConfig.outsideColor = COLOR_HEX[window.selectedInsideColor];
      }



      // Uppdatera 3D-modellen - tvinga en fullständig återskapning
      if (window.Preview3D.updateConfig) {
        window.Preview3D.updateConfig(colorConfig);
      }

      // Tvinga en återskapning av modellen
      if (window.Preview3D.recreateModel) {
        setTimeout(() => window.Preview3D.recreateModel(), 50);
      } else if (window.Preview3D.createWindow) {
        setTimeout(() => window.Preview3D.createWindow(), 50);
      }

      // Trigga en händelse för att meddela att konfigurationen har ändrats
      document.dispatchEvent(new CustomEvent('configChanged'));
    }

    // Uppdatera specifikationstabellen med färginformation
    function updateColorSpec() {
      // Kontrollera att specBody är tillgänglig
      const specBody = document.querySelector('#specTable tbody');
      if (!specBody) return;

      // Ta bort befintliga färgrader
      const insideColorRow = specBody.querySelector('tr[data-key="insideColor"]');
      if (insideColorRow) insideColorRow.remove();

      const outsideColorRow = specBody.querySelector('tr[data-key="outsideColor"]');
      if (outsideColorRow) outsideColorRow.remove();

      // Lägg till insidans färg
      const insideRow = document.createElement('tr');
      insideRow.setAttribute('data-key', 'insideColor');
      insideRow.innerHTML = `<td>Färg insida</td><td>${window.COLOR_LABELS[window.selectedInsideColor]}</td>`;
      specBody.appendChild(insideRow);

      // Lägg till utsidans färg om den är synlig
      if (!outsideColorSection.classList.contains('hidden')) {
        const outsideRow = document.createElement('tr');
        outsideRow.setAttribute('data-key', 'outsideColor');
        outsideRow.innerHTML = `<td>Färg utsida</td><td>${window.COLOR_LABELS[window.selectedOutsideColor]}</td>`;
        specBody.appendChild(outsideRow);
      }
    }

    // Gör funktionen tillgänglig globalt så att den kan anropas från loadConfiguration
    window.renderColorSpec = updateColorSpec;

    // Patcha updateSpecTable för att inkludera färginformation
    if (typeof window.updateSpecTable === 'function') {
      const originalUpdateSpecTable = window.updateSpecTable;
      window.updateSpecTable = function() {
        originalUpdateSpecTable();
        updateColorSpec();
      };
    }

    // Spara färgval tillsammans med resten av designen
    const saveBtn = document.querySelector('#section-7 .save-btn'); // Steg 8 (section-7)
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        setTimeout(() => {
          const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
          const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
          if (idx >= 0) {
            arr[idx].insideColor = window.selectedInsideColor;
            arr[idx].outsideColor = window.selectedOutsideColor;
            localStorage.setItem('myWindows', JSON.stringify(arr));
          }
        }, 0);
      });
    }

    // Patcha loadConfiguration för att ladda färgval
    function patchLoadConfiguration() {
      if (typeof window.loadConfiguration !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchLoadConfiguration, 50);
      }

      const originalLoadConfiguration = window.loadConfiguration;
      window.loadConfiguration = function(cfg, idx) {
        // Anropa original-funktionen
        originalLoadConfiguration(cfg, idx);



        // Ladda färgval om de finns
        if (cfg.insideColor) {
          window.selectedInsideColor = cfg.insideColor;
          // Markera rätt färgalternativ
          document.querySelectorAll('.color-option[data-side="inside"]').forEach(opt => {
            opt.classList.toggle('active', opt.dataset.value === cfg.insideColor);
          });

          // Kontrollera om färgalternativet finns, om inte, använd standardfärg
          const colorExists = Array.from(document.querySelectorAll('.color-option[data-side="inside"]'))
            .some(opt => opt.dataset.value === cfg.insideColor);

          if (!colorExists) {
            window.selectedInsideColor = 'white';
            document.querySelectorAll('.color-option[data-side="inside"]').forEach(opt => {
              opt.classList.toggle('active', opt.dataset.value === 'white');
            });
          }
        } else {
          // Sätt standardfärg om ingen finns sparad
          window.selectedInsideColor = 'white';
          document.querySelectorAll('.color-option[data-side="inside"]').forEach(opt => {
            opt.classList.toggle('active', opt.dataset.value === 'white');
          });
        }

        if (cfg.outsideColor) {
          window.selectedOutsideColor = cfg.outsideColor;
          // Markera rätt färgalternativ
          document.querySelectorAll('.color-option[data-side="outside"]').forEach(opt => {
            opt.classList.toggle('active', opt.dataset.value === cfg.outsideColor);
          });

          // Kontrollera om färgalternativet finns, om inte, använd standardfärg
          const colorExists = Array.from(document.querySelectorAll('.color-option[data-side="outside"]'))
            .some(opt => opt.dataset.value === cfg.outsideColor);

          if (!colorExists) {
            window.selectedOutsideColor = 'white';
            document.querySelectorAll('.color-option[data-side="outside"]').forEach(opt => {
              opt.classList.toggle('active', opt.dataset.value === 'white');
            });
          }
        } else {
          // Sätt standardfärg om ingen finns sparad
          window.selectedOutsideColor = 'white';
          document.querySelectorAll('.color-option[data-side="outside"]').forEach(opt => {
            opt.classList.toggle('active', opt.dataset.value === 'white');
          });
        }

        // Vänta lite för att säkerställa att materialet har laddats korrekt
        setTimeout(() => {
          // Kontrollera om utsidans färgval ska visas
          checkMaterialForOutsideColor();

          // Uppdatera 3D-modellen
          updateModelColors();

          // Uppdatera specifikationstabellen
          updateColorSpec();

          // Trigga en händelse för att meddela att konfigurationen har ändrats
          document.dispatchEvent(new CustomEvent('configChanged'));
        }, 200);
      };
    }

    // Patcha showSavedDetail för att visa färginformation
    function patchShowSavedDetail() {
      if (typeof window.showSavedDetail !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchShowSavedDetail, 50);
      }

      const originalShowSavedDetail = window.showSavedDetail;
      window.showSavedDetail = function(cfg, idx) {
        // Anropa original-funktionen
        originalShowSavedDetail(cfg, idx);

        // Lägg till färginformation i specifikationstabellen
        const tb = document.querySelector('#savedDetail .saved-spec tbody');
        if (!tb) return;

        // Lägg till insidans färg om den finns
        if (cfg.insideColor) {
          const insideRow = document.createElement('tr');
          insideRow.setAttribute('data-key', 'insideColor');
          insideRow.innerHTML = `<td>Färg insida</td><td>${window.COLOR_LABELS[cfg.insideColor]}</td>`;
          tb.appendChild(insideRow);
        }

        // Lägg till utsidans färg om den finns och materialet har alu-beklädnad
        if (cfg.outsideColor && (cfg.material === 'tra-aluminium' || cfg.material === 'pvc-aluminium')) {
          const outsideRow = document.createElement('tr');
          outsideRow.setAttribute('data-key', 'outsideColor');
          outsideRow.innerHTML = `<td>Färg utsida</td><td>${window.COLOR_LABELS[cfg.outsideColor]}</td>`;
          tb.appendChild(outsideRow);
        }
      };
    }

    // Sätt standardfärg som aktiv
    const insideWhiteOption = document.querySelector('.color-option[data-side="inside"][data-value="white"]');
    if (insideWhiteOption) {
      insideWhiteOption.classList.add('active');
    }

    const outsideWhiteOption = document.querySelector('.color-option[data-side="outside"][data-value="white"]');
    if (outsideWhiteOption) {
      outsideWhiteOption.classList.add('active');
    }

    // Kör patcharna efter att hela sidan laddat
    patchLoadConfiguration();
    patchShowSavedDetail();

    // Kontrollera initialt om utsidans färgval ska visas
    setTimeout(checkMaterialForOutsideColor, 100);
  });
})();
