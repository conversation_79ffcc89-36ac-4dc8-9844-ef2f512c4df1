// steg5-improved.js - Förbättrad version av glastyp-funktionalitet

(() => {
  // Konstanter för glastyper - definiera globalt så att alla funktioner kan använda dem
  window.GLASS_LABELS = {
    'double': '2-glas',
    'triple': '3-glas'
  };
  
  // Fördelar med olika glastyper för användning i tooltips och beskrivningar
  window.GLASS_BENEFITS = {
    'double': {
      energy: 'God energieffektivitet',
      sound: 'Grundläggande ljudreduktion',
      insulation: 'Bra isolering'
    },
    'triple': {
      energy: 'Utmärkt energieffektivitet',
      sound: 'Förbättrad ljudreduktion',
      insulation: 'Överlägsen isolering'
    }
  };
  
  document.addEventListener('DOMContentLoaded', () => {
    const glassBtns = document.querySelectorAll('.glass-option-btn');
    const specBody = document.querySelector('#specTable tbody');
    window.selectedGlassType = null;

    function renderGlassTypeSpec() {
      const old = specBody.querySelector('tr[data-key="glassType"]');
      if (old) old.remove();
      if (!window.selectedGlassType) return;
      
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'glassType');
      tr.innerHTML = `<td>Glastyp</td><td>${GLASS_LABELS[window.selectedGlassType]}</td>`;
      specBody.appendChild(tr);
    }

    glassBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        glassBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        window.selectedGlassType = btn.dataset.value;
        renderGlassTypeSpec();
        
        // Uppdatera förhandsvisningen för att visa vald glastyp
        updatePreviewWithGlassType(window.selectedGlassType);
      });
    });

    // Funktion för att uppdatera förhandsvisningen med vald glastyp
    function updatePreviewWithGlassType(glassType) {
      // Här kan du lägga till kod för att visuellt uppdatera förhandsvisningen
      // baserat på vald glastyp, t.ex. ändra färg eller lägga till en ikon
      console.log(`Förhandsvisning uppdaterad med glastyp: ${glassType}`);
    }

    if (typeof window.updateSpecTable === 'function') {
      const orig = window.updateSpecTable;
      window.updateSpecTable = () => {
        orig();
        renderGlassTypeSpec();
      };
    }

    // Spara glastyp tillsammans med resten
    const saveBtn = document.querySelector('#section-6 .save-btn');
    saveBtn.addEventListener('click', () => {
      setTimeout(() => {
        const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
        const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
        if (idx >= 0) {
          arr[idx].glassType = window.selectedGlassType;
          localStorage.setItem('myWindows', JSON.stringify(arr));
        }
      }, 0);
    });
  });

  // Patch av showSavedDetail för "Mina sparade designs"
  function patchShowSavedDetail() {
    if (typeof window.showSavedDetail !== 'function') {
      // Om inte definierad än, testa igen om 50ms
      return setTimeout(patchShowSavedDetail, 50);
    }

    const orig = window.showSavedDetail;
    window.showSavedDetail = (cfg, idx) => {
      orig(cfg, idx);

      // Hämta <tbody> i sparad-vy
      const tbody = document.querySelector('#savedDetail .saved-spec tbody');
      if (!tbody) return;

      // Ta bort ev. tidigare glastyp-rad
      const existing = tbody.querySelector('tr[data-key="glassType"]');
      if (existing) existing.remove();

      // Skapa och lägg till glastyp-rad om det finns
      if (cfg.glassType) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'glassType');
        tr.innerHTML = `<td>Glastyp</td><td>${window.GLASS_LABELS[cfg.glassType] || cfg.glassType}</td>`;
        tbody.appendChild(tr);
      }
    };
  }

  // Kör patchen efter att hela sidan laddat
  document.addEventListener('DOMContentLoaded', patchShowSavedDetail);

})();
