// step-navigation.js - Hantera navigering mellan steg

(() => {
  document.addEventListener('DOMContentLoaded', () => {
    // Hämta alla sektioner och steg-knappar
    const sections = document.querySelectorAll('.builder-section');
    const stepButtons = document.querySelectorAll('.step-nav .step');

    // Håll reda på vilket steg som är aktivt
    let activeStepIndex = null;

    // Funktion för att öppna ett specifikt steg och stänga övriga
    function openStep(stepIndex) {
      // Om man klickar på det redan aktiva steget, stäng det och avaktivera alla
      if (stepIndex === activeStepIndex) {
        sections.forEach(section => {
          section.classList.add('collapsed');
        });

        // Avaktivera alla steg-knappar
        stepButtons.forEach(btn => {
          btn.classList.remove('active');
        });

        // Sätt aktivt steg till null
        activeStepIndex = null;
        return;
      }

      // Stäng alla sektioner först
      sections.forEach(section => {
        section.classList.add('collapsed');
      });

      // Öppna den valda sektionen
      if (sections[stepIndex]) {
        sections[stepIndex].classList.remove('collapsed');
      }

      // Uppdatera aktiv klass på steg-knapparna
      stepButtons.forEach((btn, index) => {
        btn.classList.toggle('active', index === stepIndex);
      });

      // Scrolla till den öppnade sektionen
      if (sections[stepIndex]) {
        sections[stepIndex].scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Uppdatera aktivt steg
      activeStepIndex = stepIndex;

      // Specialhantering för steg 3 (Öppning per luft)
      if (stepIndex === 2) {
        // Säkerställ att öppningskontrollerna byggs korrekt
        if (typeof window.buildOpeningControls === 'function') {
          window.buildOpeningControls();
        }
      }

      // Specialhantering för handtagssteget (steg 9, index 8)
      if (stepIndex === 8) {
        // Kort fördröjning för att låta DOM uppdateras
        setTimeout(() => {
          console.log('Handtagssteget aktiverat, säkerställer att window.openings är korrekt');

          // Säkerställ att window.openings är initierad
          if (!window.openings) {
            console.log('window.openings är inte definierad, initierar med standardvärde');
            window.openings = ['fixed']; // Standardvärde
          }

          // Kontrollera om alla fönster är fasta
          const allFixed = window.openings.length > 0 && window.openings.every(type => type === 'fixed');

          // Visa handtagssektionen om det finns minst ett öppningsbart fönster
          const handleSection = document.querySelector('.handle-section');
          if (handleSection) {
            if (allFixed) {
              handleSection.style.display = 'none';
              document.querySelector('#section-8 .section-body > p').textContent = 'Fasta fönster behöver inga handtag.';
            } else {
              handleSection.style.display = 'block';
              document.querySelector('#section-8 .section-body > p').textContent = 'Välj handtag för ditt fönster.';

              // Visa handtagsalternativ baserat på fönstertyper
              const hasSideHung = window.openings.some(type => type === 'side-hung');

              document.querySelectorAll('.handle-option').forEach(option => {
                const handleType = option.dataset.value;

                // Stormkrok-handtag (stormkrok och barnlas-stormkrok) endast för sidohängda fönster
                if (handleType === 'stormkrok' || handleType === 'barnlas-stormkrok') {
                  if (hasSideHung) {
                    option.style.display = 'flex';
                  } else {
                    option.style.display = 'none';
                  }
                }
                // Övriga handtag för alla öppningsbara fönster
                else {
                  option.style.display = 'flex';
                }
              });
            }
          }

          // Markera aktivt handtag
          if (window.selectedHandle) {
            document.querySelectorAll('.handle-option').forEach(opt => {
              opt.classList.toggle('active', opt.dataset.value === window.selectedHandle);
            });

            // Uppdatera specifikationstabellen direkt
            const specTable = document.querySelector('#specTable tbody');
            if (specTable && window.HANDLE_LABELS) {
              // Ta bort befintlig handtagsrad
              const handleRow = specTable.querySelector('tr[data-key="handle"]');
              if (handleRow) handleRow.remove();

              // Lägg till ny handtagsrad om det inte är ett fast fönster
              if (!allFixed) {
                const tr = document.createElement('tr');
                tr.setAttribute('data-key', 'handle');
                tr.innerHTML = `<td>Handtag</td><td>${window.HANDLE_LABELS[window.selectedHandle]}</td>`;
                specTable.appendChild(tr);
              }
            }
          }

          if (typeof window.setupHandleOptions === 'function') {
            window.setupHandleOptions();
          }
        }, 100);
      }
    }

    // Lägg till klick-händelser på steg-knapparna
    stepButtons.forEach((btn, index) => {
      btn.addEventListener('click', () => {
        openStep(index);
      });
    });

    // Lägg till klick-händelser på sektionsrubrikerna
    sections.forEach((section, index) => {
      const heading = section.querySelector('h2');
      if (heading) {
        heading.style.cursor = 'pointer';

        heading.addEventListener('click', () => {
          // Om sektionen redan är öppen, stäng den
          if (!section.classList.contains('collapsed')) {
            // Stäng alla sektioner
            sections.forEach(s => {
              s.classList.add('collapsed');
            });

            // Avaktivera alla steg-knappar
            stepButtons.forEach(btn => {
              btn.classList.remove('active');
            });

            // Sätt aktivt steg till null
            activeStepIndex = null;
          } else {
            // Öppna sektionen
            openStep(index);
          }
        });
      }
    });

    // Hantera nästa/föregående-knappar
    document.querySelectorAll('.next-btn').forEach((btn, index) => {
      btn.addEventListener('click', () => {
        if (index < sections.length - 1) {
          openStep(index + 1);
        }
      });
    });

    document.querySelectorAll('.prev-btn').forEach((btn, index) => {
      btn.addEventListener('click', () => {
        if (index > 0) {
          openStep(index - 1);
        }
      });
    });
  });
})();
