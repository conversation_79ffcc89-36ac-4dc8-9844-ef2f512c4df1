# Isofönster Konfigurator - WordPress Plugin

En avancerad fönster- och dörrkonfigurator för WordPress med full Elementor-integration.

## Översikt

Isofönster Konfigurator är ett kraftfullt WordPress-plugin som låter dina kunder designa och konfigurera fönster och dörrar direkt på din webbplats. Pluginet är optimerat för Elementor och kan enkelt integreras som en widget.

## Funktioner

### ✨ Huvudfunktioner
- **Fönsterkonfigurator** - Komplett verktyg för fönsterdesign
- **Dörrkonfigurator** - Avancerad dörrkonfiguration
- **Elementor-integration** - Fungerar perfekt med Elementor Page Builder
- **Responsiv design** - Optimerad för alla enheter
- **3D-förhandsvisning** - Realistisk 3D-rendering med Three.js
- **PDF-export** - Exportera designs som professionella PDF-dokument
- **Sparfunktion** - Användare kan spara och hantera sina designs

### 🎨 Designfunktioner
- **Karmmått** - Anpassningsbara dimensioner
- **Antal lufter** - Konfigurera 1-10 lufter
- **Öppningstyper** - Fast, sidohängt, toppvippt
- **Material** - Olika materialval
- **Glastyper** - Enkelt, dubbelt, trippelglas
- **Färgval** - Inre och yttre färger
- **Spröjs** - Olika spröjstyper och anpassningar
- **Handtag** - Olika handtagstyper
- **Myggnät** - Tillval för myggnät

### 🔧 Tekniska funktioner
- **AJAX-baserad** - Smidig användarupplevelse
- **Säker datahantering** - WordPress nonces och sanitering
- **Databasintegration** - Sparar designs i WordPress-databas
- **Caching** - Optimerad prestanda
- **Lokalisering** - Redo för översättning

## Installation

### Automatisk installation
1. Ladda upp plugin-mappen till `/wp-content/plugins/`
2. Aktivera pluginet via WordPress admin
3. Gå till "Isofönster" i admin-menyn för konfiguration

### Manuell installation
1. Ladda ner plugin-filerna
2. Extrahera till `/wp-content/plugins/isofonster-configurator/`
3. Aktivera pluginet i WordPress admin

## Konfiguration

### Grundinställningar
1. Gå till **Isofönster > Inställningar**
2. Konfigurera grundläggande inställningar:
   - Aktivera/avaktivera sparfunktion
   - Aktivera/avaktivera PDF-export
   - Sätt max antal designs per användare
   - Konfigurera sessionsrensning

### Utseende
- **Företagslogotyp** - Ladda upp din logotyp för PDF-export
- **Primärfärg** - Anpassa färgtemat
- **Standardtema** - Välj mellan mörkt och ljust tema

### Funktionalitet
- **3D-förhandsvisning** - Aktivera/avaktivera 3D-rendering
- **Dörrkonfigurator** - Aktivera/avaktivera dörrfunktioner
- **Automatisk sparning** - Spara progress automatiskt

## Användning

### Shortcode
Använd shortcode för att visa konfiguratorn:
```
[isofonster_configurator]
```

#### Shortcode-parametrar
```
[isofonster_configurator type="both" theme="default" width="100%" height="auto"]
```

- `type` - "window", "door", eller "both" (standard: "both")
- `theme` - "default", "light", eller "dark" (standard: "default")
- `width` - Bredd på konfiguratorn (standard: "100%")
- `height` - Höjd på konfiguratorn (standard: "auto")

### Elementor Widget
1. Öppna Elementor-editorn
2. Sök efter "Isofönster Konfigurator" i widget-panelen
3. Dra widgeten till din sida
4. Konfigurera inställningar i widget-panelen

#### Widget-inställningar
- **Konfigurator Typ** - Välj vilken typ av konfigurator som ska visas
- **Visa rubrik** - Aktivera/avaktivera rubrik
- **Rubriktext** - Anpassa rubriktexten
- **Visa navigation** - Aktivera/avaktivera navigering
- **Aktivera sparfunktion** - Tillåt användare att spara designs
- **Aktivera PDF export** - Tillåt PDF-export

#### Styling-alternativ
- **Container** - Bakgrund, ram, rundade hörn, skugga, padding, margin
- **Rubrik** - Typografi, färg, bakgrund, padding, margin
- **Knappar** - Typografi, färger (normal/hover), ram, rundade hörn, padding

## Databasstruktur

Pluginet skapar följande databastabeller:

### wp_isofonster_designs
- `id` - Unikt ID
- `user_id` - WordPress användar-ID (0 för gäster)
- `session_id` - Sessions-ID för gäster
- `name` - Designnamn
- `type` - Typ (window/door)
- `quantity` - Antal
- `width` - Bredd
- `height` - Höjd
- `design_data` - JSON-data med alla designdetaljer
- `created_at` - Skapad datum
- `updated_at` - Uppdaterad datum

## API och Hooks

### AJAX Actions
- `save_design` - Spara design
- `load_design` - Ladda design
- `load_user_designs` - Ladda användarens designs
- `delete_design` - Ta bort design
- `export_pdf` - Exportera PDF

### WordPress Hooks
```php
// Filter för att modifiera plugin-inställningar
add_filter('isofonster_configurator_options', 'my_custom_options');

// Action när en design sparas
add_action('isofonster_design_saved', 'my_design_saved_callback');

// Filter för att modifiera PDF-export
add_filter('isofonster_pdf_data', 'my_custom_pdf_data');
```

## Anpassning

### CSS-anpassning
Lägg till anpassad CSS via inställningssidan eller i ditt tema:

```css
/* Anpassa primärfärg */
:root {
  --isofonster-color-primary: #your-color;
}

/* Anpassa konfigurator-container */
.isofonster-configurator-container {
  /* Dina stilar */
}
```

### JavaScript-anpassning
```javascript
// Lyssna på konfigurator-events
document.addEventListener('isofonster:design-updated', function(event) {
  console.log('Design uppdaterad:', event.detail);
});

// Anpassa konfigurator-beteende
IsofonsterConfigurator.config.customOption = 'värde';
```

## Prestanda

### Optimeringar
- **Minifierade assets** - CSS och JS är minifierade för produktion
- **Lazy loading** - 3D-komponenter laddas endast vid behov
- **Caching** - Designdata cachas för bättre prestanda
- **AJAX** - Smidig användarupplevelse utan sidladdningar

### Systemkrav
- **WordPress** 5.0 eller senare
- **PHP** 7.4 eller senare
- **MySQL** 5.6 eller senare
- **Elementor** 3.0 eller senare (för widget-funktionalitet)

## Säkerhet

### Säkerhetsåtgärder
- **Nonces** - Alla AJAX-anrop skyddas med WordPress nonces
- **Sanitering** - All input saniteras och valideras
- **Capability checks** - Behörighetskontroller för admin-funktioner
- **SQL injection protection** - Prepared statements används

### Datahantering
- **GDPR-kompatibel** - Användare kan ta bort sina designs
- **Sessionshantering** - Automatisk rensning av gamla sessioner
- **Datavalidering** - Strikt validering av all indata

## Felsökning

### Debug-läge
Aktivera debug-läge i inställningarna för detaljerad loggning:
```php
// Lägg till i wp-config.php
define('ISOFONSTER_DEBUG', true);
```

### Vanliga problem

#### Konfiguratorn visas inte
1. Kontrollera att pluginet är aktiverat
2. Verifiera att shortcode/widget är korrekt konfigurerad
3. Kontrollera konsolen för JavaScript-fel

#### 3D-förhandsvisning fungerar inte
1. Kontrollera att WebGL är aktiverat i webbläsaren
2. Verifiera att Three.js-biblioteket laddas korrekt
3. Kontrollera nätverksanslutning för CDN-resurser

#### Sparfunktion fungerar inte
1. Kontrollera att sparfunktion är aktiverad i inställningarna
2. Verifiera AJAX-URL och nonce
3. Kontrollera databasanslutning

## Support

### Dokumentation
- [Fullständig dokumentation](https://isofonster.se/docs)
- [Video-tutorials](https://isofonster.se/tutorials)
- [FAQ](https://isofonster.se/faq)

### Kontakt
- **Support:** <EMAIL>
- **Webbplats:** https://isofonster.se
- **Telefon:** +46 XXX XXX XXX

## Changelog

### Version 1.0.0
- Initial release
- Fönsterkonfigurator
- Elementor-integration
- Grundläggande sparfunktion
- PDF-export
- 3D-förhandsvisning

## Licens

Copyright © Isofonster.se 2025. Alla rättigheter förbehållna.

Detta plugin är licensierat under GPL v2 eller senare.

## Utveckling

### Bidrag
Vi välkomnar bidrag till pluginet. Kontakta oss för mer information om utvecklingsriktlinjer.

### Roadmap
- [ ] Avancerad dörrkonfigurator
- [ ] Fler materialval
- [ ] Integrationer med CRM-system
- [ ] Mobil app
- [ ] AR-förhandsvisning
