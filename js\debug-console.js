// debug-console.js - Script to log window.openings to the console

(function() {
  console.log('Debug Console Script Loaded');
  
  // Function to log window.openings
  function logOpenings() {
    console.log('Current window.openings:', window.openings);
    console.log('Has side-hung:', window.openings && window.openings.some(type => type === 'side-hung'));
    
    // Check if any handle options are visible
    const handleOptions = document.querySelectorAll('.handle-option');
    console.log(`Handle options visible: ${Array.from(handleOptions).filter(opt => opt.style.display !== 'none').length}`);
    
    // Log onclick attributes
    handleOptions.forEach(opt => {
      console.log(`Handle option ${opt.dataset.value} onclick:`, opt.getAttribute('onclick'));
    });
  }
  
  // Run the check when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(logOpenings, 1000);
    });
  } else {
    // DOM is already loaded
    setTimeout(logOpenings, 1000);
  }
  
  // Also log when openingTypeChanged event is fired
  document.addEventListener('openingTypeChanged', function(event) {
    console.log('openingTypeChanged event fired with detail:', event.detail);
    setTimeout(logOpenings, 500);
  });
})();
