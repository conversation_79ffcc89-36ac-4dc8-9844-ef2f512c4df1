/**
 * Integration mellan huvudapplikationen och 3D-preview
 * Denna fil hanterar kommunikationen mellan konfigurationen och 3D-modellen
 */

document.addEventListener('DOMContentLoaded', () => {
    // Kontrollera om 3D-preview är tillgänglig
    if (!window.Preview3D) {
        console.warn('3D-preview är inte tillgänglig (Preview3D)');
        return;
    }

    // Lyssna på knappen för att öppna 3D-preview
    const previewBtn = document.getElementById('open3dPreview');
    if (previewBtn) {
        previewBtn.addEventListener('click', open3DPreview);
    }

    // Lyssna på ändringar i konfigurationen
    document.addEventListener('configChanged', function() {
        console.log("Config changed event mottaget");
        if (window.Preview3D) {
            const config = getCurrentConfig();
            console.log("Uppdaterar 3D-modell med ny konfiguration:", config);
            window.Preview3D.updateConfig(config);
        }
    });

    // Lyssna på färgändringar
    document.addEventListener('colorChanged', function() {
        console.log("Color changed event mottaget");
        if (window.Preview3D) {
            const config = getCurrentConfig();
            console.log("Uppdaterar 3D-modell med nya färger:", config);
            window.Preview3D.updateConfig(config);
        }
    });
});

/**
 * Öppnar 3D-preview i ett nytt fönster/flik
 */
function open3DPreview() {
    // Hämta aktuell konfiguration
    const config = getCurrentConfig();

    // Öppna 3D-preview i ett nytt fönster
    const previewWindow = window.open('3d-preview.html', '_blank', 'width=800,height=600');

    // Vänta på att fönstret ska laddas klart
    previewWindow.addEventListener('load', () => {
        // Skicka konfigurationen till 3D-preview
        previewWindow.WindowPreview3D.updateConfig(config);
    });
}

/**
 * Hämtar aktuell konfiguration från huvudapplikationen
 */
function getCurrentConfig() {
    // Hämta värden från DOM
    const width = parseInt(document.getElementById('widthInput').value) || 100;
    const height = parseInt(document.getElementById('heightInput').value) || 120;

    // Hämta antal luft och öppningstyper
    let count = 1;
    let openings = ['fixed'];

    // Om globala variabler finns tillgängliga, använd dem
    if (window.count !== undefined) count = window.count;
    if (window.openings !== undefined) openings = window.openings;

    // Hämta färginformation om tillgänglig
    let insideColor = 0xffffff; // Vit som standard
    let outsideColor = 0xffffff; // Vit som standard

    // Om färgval finns tillgängliga, använd dem
    if (window.selectedInsideColor) {
        // Konvertera färgnamn till hexkod
        switch(window.selectedInsideColor) {
            case 'white': insideColor = 0xffffff; break;
            case 'gray': insideColor = 0x808080; break;
            case 'black': insideColor = 0x222222; break; // Mindre intensiv svart färg
            case 'anthracite': insideColor = 0x383c3f; break; // Antracitgrå
            case 'darkgray': insideColor = 0x505050; break; // Mörkgrå
            case 'silver': insideColor = 0xc0c0c0; break; // Silver
            case 'beige': insideColor = 0xf5f5dc; break; // Beige
            default: insideColor = 0xffffff;
        }
    }

    if (window.selectedOutsideColor) {
        // Konvertera färgnamn till hexkod
        switch(window.selectedOutsideColor) {
            case 'white': outsideColor = 0xffffff; break;
            case 'gray': outsideColor = 0x808080; break;
            case 'black': outsideColor = 0x222222; break; // Mindre intensiv svart färg
            case 'anthracite': outsideColor = 0x383c3f; break; // Antracitgrå
            case 'darkgray': outsideColor = 0x505050; break; // Mörkgrå
            case 'silver': outsideColor = 0xc0c0c0; break; // Silver
            case 'beige': outsideColor = 0xf5f5dc; break; // Beige
            default: outsideColor = 0xffffff;
        }
    }

    // Skapa konfigurationsobjekt
    return {
        width: width,
        height: height,
        depth: 10,
        frameWidth: 8,
        count: count,
        openings: openings,
        glassColor: 0xadd8e6, // Ljusblå
        frameColor: insideColor, // Använd vald insidafärg
        insideColor: insideColor, // Använd vald insidafärg
        outsideColor: outsideColor // Använd vald utsidafärg
    };
}
