/* ===== SAVED DESIGNS (sektion Mina sparade designs) ===== */

/* Hela sektionen */
#sectionSavedDesigns {
    display: flex;
    gap: 1rem;
    padding: 1rem;
  }

  /* Toolbar (sök & sort) */
  .saved-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  .search-input {
    flex: 1;
    padding: 0.6rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-bg);
    color: var(--color-secondary);
    transition: border-color var(--transition);
  }
  .search-input:focus {
    border-color: var(--color-primary);
    outline: none;
  }
  .sort-btn {
    padding: 0.6rem 1rem;
    border: none;
    border-radius: var(--radius);
    background: var(--color-primary);
    color: #121212;
    cursor: pointer;
    transition: background var(--transition);
  }
  .sort-btn:hover {
    background: var(--color-primary-dark);
  }

  /* Layout sidebar & detail */
  #sectionSavedDesigns .saved-section {
    display: flex;
    align-items: flex-start;
    width: 100%;
    height: calc(100vh - var(--header-height));
    gap: 1.5rem;
    margin: 0;
    padding: 1rem 0;
  }
  #sectionSavedDesigns .saved-sidebar {
    flex: 0 0 450px;
    max-width: 450px;
    position: sticky;
    top: var(--header-height);
    background: var(--color-panel);
    border-right: 1px solid var(--color-border);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  #sectionSavedDesigns .saved-list {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-self: stretch;
    gap: 0.5rem;
    overflow-y: auto;
    background: var(--color-bg);
    padding: 0.5rem;
    border-radius: var(--radius);
  }

  /* Kort i listan */
  .saved-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: background var(--transition), transform var(--transition);
  }
  .saved-card:hover {
    background-color: rgba(245,199,0,0.08);
  }
  .saved-card.active {
    background: rgba(245,199,0,0.1);
    border: 2px solid var(--color-primary);
  }
  .saved-card::after {
    content: "";
    position: absolute;
    left: 0; bottom: 0;
    height: 2px; width: 0;
    background: var(--color-primary);
    transition: width 0.3s ease-out;
  }
  .saved-card:hover::after {
    width: 100%;
  }
  .card-title {
    font-weight: 600;
    color: var(--color-secondary);
  }
  .card-date {
    font-size: 0.85rem;
    color: #888;
  }

  /* Detaljpanelet */
  #savedDetail,
  #sectionSavedDesigns .saved-detail {
    flex: 1;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1rem;
    display: none;
    overflow-y: auto;
  }
  #savedDetail:not(.hidden),
  #sectionSavedDesigns .saved-detail:not(.hidden) {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .detail-title {
    color: var(--color-primary);
    font-size: 1.25rem;
    text-align: center;
    margin: 0;
  }
  .placeholder-text {
    color: #888;
    font-style: italic;
  }
  .detail-actions {
    display: flex;
    gap: 0.5rem;
  }
  .detail-actions button {
    padding: 0.5rem 1rem;
    background: var(--color-primary);
    color: #121212;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition);
  }
  .detail-actions button:hover {
    background: var(--color-primary-dark);
  }

  /* Specifikationskort i detalj */
  .saved-detail .spec-container.saved-spec {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
    margin-top: 2rem;
  }
  .saved-detail .spec-container.saved-spec h4 {
    color: var(--color-primary);
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }
  .saved-detail .spec-container.saved-spec table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  .saved-detail .spec-container.saved-spec th,
  .saved-detail .spec-container.saved-spec td {
    border: 1px solid var(--color-border);
    padding: 0.5rem 0.75rem;
    text-align: left;
    font-size: 0.95rem;
    color: var(--color-secondary);
  }
  .saved-detail .spec-container.saved-spec th {
    background: rgba(245,199,0,0.1);
    color: var(--color-primary);
    font-weight: 600;
  }
  .saved-detail .preview-container {
    margin-bottom: 2rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    max-width: 100%;
    overflow: hidden;
  }

  /* Container för dörrförhandsvisning */
  .saved-detail .preview-container .door-preview {
    height: auto; /* Låt höjden anpassas efter bredden */
    width: 100%; /* Använd hela tillgängliga bredden */
    max-width: 800px; /* Maxbredd för att undvika att det blir för stort */
    max-height: 600px; /* Maxhöjd för att undvika att det blir för stort */
    margin: 0 auto; /* Centrera horisontellt */
  }

  /* Vy-etikett borttagen */

  /* SVG-container för dörrlinjer */
  .door-svg-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
  }

  .saved-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    background: rgba(60, 60, 60, 0.95);
    position: relative;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
    border: 10px solid rgba(50, 50, 50, 0.95);
    box-sizing: border-box;
    padding: 0;
    min-width: 200px;
    min-height: 300px;

    /* Trästruktur i karmen - samma som i preview-enhanced.css */
    --wood-color: rgba(50, 50, 50, 0.95);
    --wood-grain:
      linear-gradient(90deg,
        var(--wood-color) 0%,
        rgba(70, 70, 70, 0.95) 20%,
        var(--wood-color) 40%,
        rgba(70, 70, 70, 0.95) 60%,
        var(--wood-color) 80%,
        rgba(70, 70, 70, 0.95) 100%);
    background-image: var(--wood-grain);
    background-size: 100% 100%;
  }

  /* Window frame styling för sparade designer - matchar preview-enhanced.css */
  .saved-preview .window-frame {
    flex: 1;
    height: 100%;
    position: relative;
    background: rgba(65, 65, 65, 0.95);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease;
    padding: 0;
    margin: 0;

    /* Mittpost mellan lufterna */
    border-right: 8px solid var(--wood-color);

    /* Trästruktur i bågen */
    background-image: var(--wood-grain);
    background-size: 100% 100%;

    /* Fönsterbåge runt glaset */
    border-top: 8px solid var(--wood-color);
    border-bottom: 8px solid var(--wood-color);
    border-left: 8px solid var(--wood-color);
  }

  /* Sista fönsterbågen ska ha höger-balk för konsekvent karm */
  .saved-preview .window-frame:last-child {
    border-right: 8px solid var(--wood-color);
  }

  /* Första fönsterbågen ska inte ha vänster-balk */
  .saved-preview .window-frame:first-child {
    border-left: none;
  }

  /* Window pane - Glasruta */
  .saved-preview .pane {
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    background: rgba(150, 170, 190, 0.1);
    box-shadow:
      inset 0 0 30px rgba(200, 200, 220, 0.1),
      0 0 5px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(0.3px);
    z-index: 2;
    border: 1px solid rgba(100, 100, 100, 0.3);
  }

  /* Specifik styling för dörrförhandsvisning i sparade designer */
  .saved-preview.door-preview {
    display: flex;
    align-items: stretch;
    justify-content: center;
    height: 100%;
    width: 100%;
    max-width: 100%;
    /* Aspect ratio hanteras dynamiskt i JavaScript baserat på dörrens faktiska dimensioner */
  }

  /* Dörrförhandsvisning i sparade designer */
  .saved-preview .door-frame {
    display: flex;
    flex-direction: column;
    position: relative;
    background: rgba(65, 65, 65, 0.95);
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease;
    padding: 0;
    margin: 0;
    border-right: 8px solid rgba(50, 50, 50, 0.95);
    border-top: 8px solid rgba(50, 50, 50, 0.95);
    border-bottom: 8px solid rgba(50, 50, 50, 0.95);
    border-left: 8px solid rgba(50, 50, 50, 0.95);
    min-width: 60px;
    overflow: visible; /* Tillåt innehåll att sticka ut utanför ramen */
    flex: 1; /* Fyll tillgängligt utrymme */
    height: 100%; /* Fyll hela höjden */
  }

  /* Dörrförhandsvisning i sparade designer - specifikt för door-preview */
  .door-preview .door-frame {
    width: 100%; /* Fyll hela bredden av containern */
    height: 100%; /* Fyll hela höjden av containern */
    flex: 1 1 auto; /* Låt flex-grow och flex-shrink anpassa sig efter innehållet */
  }

  /* Förbättrad SVG-container för dörrlinjer */
  .saved-preview .door-frame .door-svg-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
  }

  .saved-preview .door-glass-pane {
    flex: 1;
    background: rgba(150, 170, 190, 0.2);
    border: 2px solid rgba(50, 50, 50, 0.95);
    box-shadow:
      inset 0 0 20px rgba(200, 200, 220, 0.1),
      inset 0 0 40px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    margin: 8px;
  }

  .saved-preview .door-panel {
    background: rgba(50, 50, 50, 0.95);
    border-top: 2px solid rgba(70, 70, 70, 0.95);
    box-shadow:
      inset 0 2px 5px rgba(0, 0, 0, 0.3),
      inset 0 -2px 5px rgba(255, 255, 255, 0.1);
    position: relative;
  }

  .saved-preview .door-handle {
    position: absolute;
    width: 16px;
    height: 60px;
    background: linear-gradient(180deg, #a0a0a0 0%, #808080 50%, #a0a0a0 100%);
    border-radius: 4px;
    box-shadow:
      inset 0 2px 6px rgba(255, 255, 255, 0.4),
      inset 0 -2px 6px rgba(0, 0, 0, 0.4),
      0 4px 8px rgba(0, 0, 0, 0.5);
    top: 50%;
    transform: translateY(-50%);
    z-index: 5;
  }

  .saved-preview .door-handle.handle-left {
    left: 15px;
  }

  .saved-preview .door-handle.handle-right {
    right: 15px;
  }

  .saved-preview .door-handle.handle-sliding {
    width: 20px;
    height: 40px;
    border-radius: 10px;
  }

  .saved-preview .door-sproj {
    width: 8px;
    background: rgba(50, 50, 50, 0.95);
    z-index: 2;
  }
  /* Kolumnbredder */
  .saved-detail .spec-container.saved-spec th:first-child,
  .saved-detail .spec-container.saved-spec td:first-child {
    width: 30%;
  }
  .saved-detail .spec-container.saved-spec th:nth-child(2),
  .saved-detail .spec-container.saved-spec td:nth-child(2) {
    width: 70%;
  }
  .saved-detail .spec-container.saved-spec td {
    word-wrap: break-word;
  }

  /* Overlay & Sidebar (om du använder modal) */
  .overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background: rgba(0,0,0,0.6);
    display: flex; justify-content: flex-start; align-items: stretch;
    z-index: 1000;
  }
  .hidden {
    display: none !important;
  }
  .sidebar {
    width: 300px;
    background: var(--color-panel);
    color: var(--color-secondary);
    display: flex; flex-direction: column;
    padding: 1rem;
    box-shadow: 2px 0 8px rgba(0,0,0,0.5);
  }
  .sidebar-header {
    display: flex; justify-content: space-between; align-items: center;
  }
  .sidebar-header h2 {
    font-size: 1.25rem;
    margin: 0;
  }
  .close-btn {
    background: none; border: none;
    font-size: 1.5rem; color: var(--color-secondary);
    cursor: pointer;
  }
  .window-list {
    flex: 1; margin: 1rem 0; padding: 0;
    list-style: none; overflow-y: auto;
  }
  .window-list li {
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 0.75rem; margin-bottom: 0.5rem;
    display: flex; justify-content: space-between; align-items: center;
  }
  .window-list li .title {
    flex: 1; font-size: 0.9rem;
  }
  .window-list li button {
    background: none; border: none;
    font-size: 1rem; color: var(--color-secondary);
    cursor: pointer; margin-left: 0.5rem;
  }
  .save-btn {
    align-self: stretch;
    margin-top: auto;
  }

  /* Responsivt för sidebar/detalj (≤768px) */
  @media (max-width: 768px) {
    #sectionSavedDesigns .saved-section {
      flex-direction: column;
      height: auto;
    }
    .saved-sidebar {
      position: static;
      width: 100%;
      margin-bottom: 1rem;
    }
    .saved-list { height: auto; }
  }
