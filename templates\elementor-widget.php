<?php
/**
 * Elementor Widget Template för Isofönster Konfigurator
 */

// Förhindra direkt åtkomst
if (!defined('ABSPATH')) {
    exit;
}

$configurator_type = $settings['configurator_type'];
$show_header = $settings['show_header'] === 'yes';
$header_text = $settings['header_text'];
$show_navigation = $settings['show_navigation'] === 'yes';
$enable_save = $settings['enable_save'] === 'yes';
$enable_export = $settings['enable_export'] === 'yes';
?>

<div class="isofonster-configurator-container" 
     data-type="<?php echo esc_attr($configurator_type); ?>"
     data-save="<?php echo esc_attr($enable_save ? 'true' : 'false'); ?>"
     data-export="<?php echo esc_attr($enable_export ? 'true' : 'false'); ?>">
     
    <?php if ($show_header): ?>
    <header class="isofonster-configurator-header">
        <h1><?php echo esc_html($header_text); ?></h1>
    </header>
    <?php endif; ?>
    
    <?php if ($show_navigation): ?>
    <nav class="isofonster-top-nav">
        <button id="isofonster-navNewDesign" data-target="sectionNewDesign" class="active">
            <?php _e('Ny Design', 'isofonster-configurator'); ?>
        </button>
        <?php if ($enable_save): ?>
        <button id="isofonster-navSavedDesigns" data-target="sectionSavedDesigns">
            <?php _e('Mina sparade designs', 'isofonster-configurator'); ?>
        </button>
        <?php endif; ?>
        <?php if ($enable_export): ?>
        <button id="isofonster-navExport" data-target="sectionExport">
            <?php _e('Exportera', 'isofonster-configurator'); ?>
        </button>
        <?php endif; ?>
    </nav>
    <?php endif; ?>
    
    <!-- SEKTION: Ny design (configurator) -->
    <section id="isofonster-sectionNewDesign" class="isofonster-section">
        
        <?php if ($configurator_type === 'both'): ?>
        <!-- Konfigurator-val -->
        <div id="isofonster-configuratorSelection" class="isofonster-configurator-selection">
            <h2><?php _e('Välj konfigurator', 'isofonster-configurator'); ?></h2>
            <div class="isofonster-configurator-options">
                <div id="isofonster-windowConfiguratorOption" class="isofonster-configurator-option">
                    <div class="isofonster-icon">🪟</div>
                    <h3><?php _e('Fönster Konfigurator', 'isofonster-configurator'); ?></h3>
                    <p><?php _e('Designa ditt eget fönster med vårt enkla verktyg', 'isofonster-configurator'); ?></p>
                    <button id="isofonster-startWindowConfigurator" class="btn">
                        <?php _e('Designa ett fönster', 'isofonster-configurator'); ?>
                    </button>
                </div>
                <div id="isofonster-doorConfiguratorOption" class="isofonster-configurator-option">
                    <div class="isofonster-icon">🚪</div>
                    <h3><?php _e('Dörr Konfigurator', 'isofonster-configurator'); ?></h3>
                    <p><?php _e('Designa din egen dörr med vårt enkla verktyg', 'isofonster-configurator'); ?></p>
                    <button id="isofonster-startDoorConfigurator" class="btn">
                        <?php _e('Designa en dörr', 'isofonster-configurator'); ?>
                    </button>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($configurator_type === 'window' || $configurator_type === 'both'): ?>
        <!-- Fönsterkonfigurator -->
        <div id="isofonster-windowConfigurator" class="isofonster-window-configurator <?php echo $configurator_type === 'window' ? '' : 'hidden'; ?>">
            
            <!-- Steg-navigering -->
            <div class="isofonster-step-nav-wrapper">
                <div class="isofonster-step-nav">
                    <div class="isofonster-step active" data-step="0">
                        <span class="isofonster-step-number">1</span>
                        <span class="isofonster-step-title"><?php _e('Karmmått', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="1">
                        <span class="isofonster-step-number">2</span>
                        <span class="isofonster-step-title"><?php _e('Antal lufter', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="2">
                        <span class="isofonster-step-number">3</span>
                        <span class="isofonster-step-title"><?php _e('Öppning per luft', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="3">
                        <span class="isofonster-step-number">4</span>
                        <span class="isofonster-step-title"><?php _e('Material', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="4">
                        <span class="isofonster-step-number">5</span>
                        <span class="isofonster-step-title"><?php _e('Glas', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="5">
                        <span class="isofonster-step-number">6</span>
                        <span class="isofonster-step-title"><?php _e('Färg', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="6">
                        <span class="isofonster-step-number">7</span>
                        <span class="isofonster-step-title"><?php _e('Spara', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="7">
                        <span class="isofonster-step-number">8</span>
                        <span class="isofonster-step-title"><?php _e('Spröjs', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="8">
                        <span class="isofonster-step-number">9</span>
                        <span class="isofonster-step-title"><?php _e('Handtag', 'isofonster-configurator'); ?></span>
                    </div>
                    <div class="isofonster-step" data-step="9">
                        <span class="isofonster-step-number">10</span>
                        <span class="isofonster-step-title"><?php _e('Myggnät', 'isofonster-configurator'); ?></span>
                    </div>
                </div>
            </div>
            
            <main class="isofonster-builder-container">
                <aside class="isofonster-controls-column">
                    <!-- Steg 1: Karmmått -->
                    <section id="isofonster-section-0" class="isofonster-builder-section expanded">
                        <h2><?php _e('1. Karmmått', 'isofonster-configurator'); ?></h2>
                        <div class="isofonster-section-body isofonster-dimension-section">
                            <div class="isofonster-dim-row">
                                <div class="isofonster-dim-label">
                                    <span><?php _e('Bredd', 'isofonster-configurator'); ?></span>
                                    <span class="isofonster-dim-value" id="isofonster-widthLabel">180 cm</span>
                                </div>
                                <input type="number" id="isofonster-widthInput" class="isofonster-dim-input" min="50" max="180" value="180">
                                <div class="isofonster-dim-hint"><?php _e('Min: 50 cm / Max: 180 cm', 'isofonster-configurator'); ?></div>
                            </div>
                            <div class="isofonster-dim-row">
                                <div class="isofonster-dim-label">
                                    <span><?php _e('Höjd', 'isofonster-configurator'); ?></span>
                                    <span class="isofonster-dim-value" id="isofonster-heightLabel">150 cm</span>
                                </div>
                                <input type="number" id="isofonster-heightInput" class="isofonster-dim-input" min="60" max="180" value="150">
                                <div class="isofonster-dim-hint"><?php _e('Min: 60 cm / Max: 180 cm', 'isofonster-configurator'); ?></div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Steg 2: Antal lufter -->
                    <section id="isofonster-section-1" class="isofonster-builder-section collapsed">
                        <h2><?php _e('2. Antal lufter', 'isofonster-configurator'); ?></h2>
                        <div class="isofonster-section-body">
                            <div class="isofonster-count-controls">
                                <button id="isofonster-decBtn" class="isofonster-count-btn">−</button>
                                <input type="number" id="isofonster-customIn" class="isofonster-count-input" min="1" max="10" value="1">
                                <button id="isofonster-incBtn" class="isofonster-count-btn">+</button>
                            </div>
                            <div class="isofonster-count-hint"><?php _e('Välj antal lufter (1-10)', 'isofonster-configurator'); ?></div>
                        </div>
                    </section>
                    
                    <!-- Steg 3: Öppning per luft -->
                    <section id="isofonster-section-2" class="isofonster-builder-section collapsed">
                        <h2><?php _e('3. Öppning per luft', 'isofonster-configurator'); ?></h2>
                        <div class="isofonster-section-body">
                            <div id="isofonster-openingControls" class="isofonster-opening-controls">
                                <!-- Dynamiskt innehåll genereras av JavaScript -->
                            </div>
                        </div>
                    </section>
                    
                    <!-- Ytterligare steg kommer att laddas dynamiskt -->
                    
                </aside>
                
                <div class="isofonster-result-column">
                    <!-- Förhandsvisning -->
                    <div class="isofonster-preview-container">
                        <div id="isofonster-preview" class="isofonster-preview">
                            <!-- Förhandsvisning renderas här -->
                        </div>
                    </div>
                    
                    <!-- 3D Preview -->
                    <div class="isofonster-3d-preview-container">
                        <div id="isofonster-3dPreview" class="isofonster-3d-preview">
                            <!-- 3D förhandsvisning -->
                        </div>
                    </div>
                </div>
                
                <!-- Specifikationstabell -->
                <div class="isofonster-spec-container">
                    <h3><?php _e('Specifikation', 'isofonster-configurator'); ?></h3>
                    <table id="isofonster-specTable" class="isofonster-spec-table">
                        <tbody>
                            <!-- Specifikationer fylls i dynamiskt -->
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
        <?php endif; ?>
        
        <?php if ($configurator_type === 'door' || $configurator_type === 'both'): ?>
        <!-- Dörrkonfigurator -->
        <div id="isofonster-doorConfigurator" class="isofonster-door-configurator <?php echo $configurator_type === 'door' ? '' : 'hidden'; ?>">
            <!-- Dörrkonfigurator innehåll kommer här -->
            <p><?php _e('Dörrkonfigurator kommer snart...', 'isofonster-configurator'); ?></p>
        </div>
        <?php endif; ?>
        
    </section>
    
    <?php if ($enable_save): ?>
    <!-- SEKTION: Mina sparade designs -->
    <section id="isofonster-sectionSavedDesigns" class="isofonster-section hidden">
        <div class="isofonster-saved-section">
            <aside class="isofonster-saved-sidebar">
                <div class="isofonster-saved-toolbar">
                    <input type="search" id="isofonster-searchSaved" class="isofonster-search-input" 
                           placeholder="<?php _e('Sök dina sparade designs…', 'isofonster-configurator'); ?>">
                    <button id="isofonster-sortDate" class="isofonster-sort-btn" data-order="desc">
                        <?php _e('Senaste först', 'isofonster-configurator'); ?>
                    </button>
                </div>
                <div id="isofonster-savedList" class="isofonster-saved-list">
                    <!-- Sparade designs renderas här -->
                </div>
            </aside>
            <div id="isofonster-savedDetail" class="isofonster-saved-detail">
                <p class="isofonster-placeholder-text">
                    <?php _e('Välj en design för att se detaljer', 'isofonster-configurator'); ?>
                </p>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <?php if ($enable_export): ?>
    <!-- SEKTION: Export -->
    <section id="isofonster-sectionExport" class="isofonster-section hidden">
        <div class="isofonster-export-section">
            <h2><?php _e('Exportera dina designs', 'isofonster-configurator'); ?></h2>
            <p><?php _e('Exportera alla dina sparade designs som PDF.', 'isofonster-configurator'); ?></p>
            
            <div class="isofonster-export-controls">
                <button id="isofonster-exportBtn" class="btn isofonster-export-btn">
                    <?php _e('Exportera som PDF', 'isofonster-configurator'); ?>
                </button>
            </div>
            
            <div id="isofonster-pdfProgressContainer" class="isofonster-progress-container hidden">
                <div class="isofonster-progress-bar">
                    <div id="isofonster-pdfProgressBar" class="isofonster-progress-fill"></div>
                </div>
                <p id="isofonster-pdfProgressStatus" class="isofonster-progress-status">
                    <?php _e('Förbereder export...', 'isofonster-configurator'); ?>
                </p>
            </div>
        </div>
    </section>
    <?php endif; ?>
    
    <!-- Hidden elements för PDF export -->
    <div id="isofonster-pdfExportContainer" class="hidden">
        <!-- PDF export innehåll -->
    </div>
</div>

<script>
// Initiera konfigurator när DOM är laddad
document.addEventListener('DOMContentLoaded', function() {
    if (typeof IsofonsterConfigurator !== 'undefined') {
        IsofonsterConfigurator.init({
            container: '.isofonster-configurator-container',
            type: '<?php echo esc_js($configurator_type); ?>',
            enableSave: <?php echo $enable_save ? 'true' : 'false'; ?>,
            enableExport: <?php echo $enable_export ? 'true' : 'false'; ?>,
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('isofonster_nonce'); ?>'
        });
    }
});
</script>
