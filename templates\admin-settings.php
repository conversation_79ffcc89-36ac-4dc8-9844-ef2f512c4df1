<?php
/**
 * Admin inställningssida för Isofönster Konfigurator
 */

// Förhindra direkt åtkomst
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('Isofönster Konfigurator - Inställningar', 'isofonster-configurator'); ?></h1>
    
    <?php settings_errors(); ?>
    
    <form method="post" action="options.php">
        <?php
        settings_fields('isofonster_settings');
        do_settings_sections('isofonster_settings');
        ?>
        
        <div class="isofonster-settings-container">
            
            <!-- Allmänna inställningar -->
            <div class="isofonster-settings-section">
                <h2><?php _e('Allmänna inställningar', 'isofonster-configurator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="enable_save"><?php _e('Aktivera sparfunktion', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $options = get_option('isofonster_options', array());
                                $enable_save = isset($options['enable_save']) ? $options['enable_save'] : true;
                                ?>
                                <input type="checkbox" id="enable_save" name="isofonster_options[enable_save]" value="1" <?php checked(1, $enable_save); ?> />
                                <p class="description"><?php _e('Tillåt användare att spara sina designs', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable_export"><?php _e('Aktivera PDF export', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $enable_export = isset($options['enable_export']) ? $options['enable_export'] : true;
                                ?>
                                <input type="checkbox" id="enable_export" name="isofonster_options[enable_export]" value="1" <?php checked(1, $enable_export); ?> />
                                <p class="description"><?php _e('Tillåt användare att exportera designs som PDF', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="max_designs_per_user"><?php _e('Max designs per användare', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $max_designs = isset($options['max_designs_per_user']) ? $options['max_designs_per_user'] : 50;
                                ?>
                                <input type="number" id="max_designs_per_user" name="isofonster_options[max_designs_per_user]" value="<?php echo esc_attr($max_designs); ?>" min="1" max="1000" class="small-text" />
                                <p class="description"><?php _e('Maximalt antal designs en användare kan spara', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="session_cleanup_days"><?php _e('Rensa sessionsdata efter (dagar)', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $cleanup_days = isset($options['session_cleanup_days']) ? $options['session_cleanup_days'] : 30;
                                ?>
                                <input type="number" id="session_cleanup_days" name="isofonster_options[session_cleanup_days]" value="<?php echo esc_attr($cleanup_days); ?>" min="1" max="365" class="small-text" />
                                <p class="description"><?php _e('Antal dagar innan gamla sessionsdata rensas automatiskt', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Utseende -->
            <div class="isofonster-settings-section">
                <h2><?php _e('Utseende', 'isofonster-configurator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="company_logo"><?php _e('Företagslogotyp URL', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $company_logo = isset($options['company_logo']) ? $options['company_logo'] : '';
                                ?>
                                <input type="url" id="company_logo" name="isofonster_options[company_logo]" value="<?php echo esc_attr($company_logo); ?>" class="regular-text" />
                                <button type="button" class="button" id="upload-logo-btn"><?php _e('Välj bild', 'isofonster-configurator'); ?></button>
                                <p class="description"><?php _e('URL till företagslogotyp som visas i PDF-export', 'isofonster-configurator'); ?></p>
                                <?php if ($company_logo): ?>
                                    <div class="logo-preview">
                                        <img src="<?php echo esc_url($company_logo); ?>" style="max-width: 200px; max-height: 100px; margin-top: 10px;" alt="Logo preview" />
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="primary_color"><?php _e('Primärfärg', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $primary_color = isset($options['primary_color']) ? $options['primary_color'] : '#f5c700';
                                ?>
                                <input type="color" id="primary_color" name="isofonster_options[primary_color]" value="<?php echo esc_attr($primary_color); ?>" />
                                <p class="description"><?php _e('Huvudfärg för konfiguratorn', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="default_theme"><?php _e('Standardtema', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $default_theme = isset($options['default_theme']) ? $options['default_theme'] : 'dark';
                                ?>
                                <select id="default_theme" name="isofonster_options[default_theme]">
                                    <option value="dark" <?php selected('dark', $default_theme); ?>><?php _e('Mörkt tema', 'isofonster-configurator'); ?></option>
                                    <option value="light" <?php selected('light', $default_theme); ?>><?php _e('Ljust tema', 'isofonster-configurator'); ?></option>
                                </select>
                                <p class="description"><?php _e('Standardtema för konfiguratorn', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Funktionalitet -->
            <div class="isofonster-settings-section">
                <h2><?php _e('Funktionalitet', 'isofonster-configurator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="enable_3d_preview"><?php _e('Aktivera 3D förhandsvisning', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $enable_3d = isset($options['enable_3d_preview']) ? $options['enable_3d_preview'] : true;
                                ?>
                                <input type="checkbox" id="enable_3d_preview" name="isofonster_options[enable_3d_preview]" value="1" <?php checked(1, $enable_3d); ?> />
                                <p class="description"><?php _e('Visa 3D förhandsvisning av designs (kräver WebGL)', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable_door_configurator"><?php _e('Aktivera dörrkonfigurator', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $enable_door = isset($options['enable_door_configurator']) ? $options['enable_door_configurator'] : true;
                                ?>
                                <input type="checkbox" id="enable_door_configurator" name="isofonster_options[enable_door_configurator]" value="1" <?php checked(1, $enable_door); ?> />
                                <p class="description"><?php _e('Tillåt konfiguration av dörrar förutom fönster', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="auto_save"><?php _e('Automatisk sparning', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $auto_save = isset($options['auto_save']) ? $options['auto_save'] : false;
                                ?>
                                <input type="checkbox" id="auto_save" name="isofonster_options[auto_save]" value="1" <?php checked(1, $auto_save); ?> />
                                <p class="description"><?php _e('Spara automatiskt användarens progress i sessionen', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Avancerade inställningar -->
            <div class="isofonster-settings-section">
                <h2><?php _e('Avancerade inställningar', 'isofonster-configurator'); ?></h2>
                
                <table class="form-table" role="presentation">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <label for="debug_mode"><?php _e('Debug-läge', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $debug_mode = isset($options['debug_mode']) ? $options['debug_mode'] : false;
                                ?>
                                <input type="checkbox" id="debug_mode" name="isofonster_options[debug_mode]" value="1" <?php checked(1, $debug_mode); ?> />
                                <p class="description"><?php _e('Aktivera debug-läge för felsökning (endast för utveckling)', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="cache_duration"><?php _e('Cache-varaktighet (sekunder)', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $cache_duration = isset($options['cache_duration']) ? $options['cache_duration'] : 3600;
                                ?>
                                <input type="number" id="cache_duration" name="isofonster_options[cache_duration]" value="<?php echo esc_attr($cache_duration); ?>" min="0" max="86400" class="small-text" />
                                <p class="description"><?php _e('Hur länge data ska cachas (0 = ingen cache)', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="custom_css"><?php _e('Anpassad CSS', 'isofonster-configurator'); ?></label>
                            </th>
                            <td>
                                <?php
                                $custom_css = isset($options['custom_css']) ? $options['custom_css'] : '';
                                ?>
                                <textarea id="custom_css" name="isofonster_options[custom_css]" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($custom_css); ?></textarea>
                                <p class="description"><?php _e('Anpassad CSS som läggs till konfiguratorn', 'isofonster-configurator'); ?></p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <?php submit_button(); ?>
    </form>
    
    <!-- Återställ inställningar -->
    <div class="isofonster-reset-section">
        <h2><?php _e('Återställ inställningar', 'isofonster-configurator'); ?></h2>
        <p><?php _e('Återställ alla inställningar till standardvärden.', 'isofonster-configurator'); ?></p>
        <button type="button" class="button button-secondary" id="reset-settings-btn">
            <?php _e('Återställ inställningar', 'isofonster-configurator'); ?>
        </button>
    </div>
</div>

<style>
.isofonster-settings-container {
    max-width: 1000px;
}

.isofonster-settings-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.isofonster-settings-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.logo-preview {
    margin-top: 10px;
}

.isofonster-reset-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
    border-left: 4px solid #dc3232;
}

.isofonster-reset-section h2 {
    color: #dc3232;
    margin-top: 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Media uploader för logotyp
    $('#upload-logo-btn').on('click', function(e) {
        e.preventDefault();
        
        var mediaUploader = wp.media({
            title: '<?php _e('Välj logotyp', 'isofonster-configurator'); ?>',
            button: {
                text: '<?php _e('Använd denna bild', 'isofonster-configurator'); ?>'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#company_logo').val(attachment.url);
            
            // Visa förhandsvisning
            $('.logo-preview').remove();
            $('<div class="logo-preview"><img src="' + attachment.url + '" style="max-width: 200px; max-height: 100px; margin-top: 10px;" alt="Logo preview" /></div>').insertAfter('#upload-logo-btn');
        });
        
        mediaUploader.open();
    });
    
    // Återställ inställningar
    $('#reset-settings-btn').on('click', function() {
        if (!confirm('<?php _e('Är du säker på att du vill återställa alla inställningar? Detta kan inte ångras.', 'isofonster-configurator'); ?>')) {
            return;
        }
        
        var $btn = $(this);
        $btn.prop('disabled', true).text('<?php _e('Återställer...', 'isofonster-configurator'); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'isofonster_reset_settings',
                nonce: '<?php echo wp_create_nonce('isofonster_admin_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php _e('Inställningar återställda!', 'isofonster-configurator'); ?>');
                    location.reload();
                } else {
                    alert('<?php _e('Fel vid återställning', 'isofonster-configurator'); ?>');
                }
            },
            error: function() {
                alert('<?php _e('Fel vid återställning', 'isofonster-configurator'); ?>');
            },
            complete: function() {
                $btn.prop('disabled', false).text('<?php _e('Återställ inställningar', 'isofonster-configurator'); ?>');
            }
        });
    });
    
    // Färgförhandsvisning
    $('#primary_color').on('change', function() {
        var color = $(this).val();
        $('<style id="color-preview">.isofonster-color-preview { background-color: ' + color + ' !important; }</style>').appendTo('head');
        $('#color-preview').remove();
    });
});
</script>
