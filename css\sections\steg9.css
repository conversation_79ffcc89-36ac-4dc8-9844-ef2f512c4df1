/* CSS för <PERSON> */

.handle-section {
  margin-bottom: 2rem;
}

.handle-section h3 {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: var(--color-text);
}

.handle-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.handle-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 1rem;
  border-radius: var(--radius);
  border: 2px solid transparent;
  transition: all 0.2s ease;
  background-color: var(--color-panel);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.handle-option:hover {
  background-color: rgba(245, 199, 0, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.handle-option:active {
  transform: scale(0.98);
}

.handle-option.active {
  border-color: var(--color-primary);
  background-color: rgba(245, 199, 0, 0.15);
  box-shadow: 0 0 0 2px rgba(245, 199, 0, 0.3);
}

/* Add a checkmark to the active option */
.handle-option.active::before {
  content: '✓';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: var(--color-primary);
  color: #121212;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  z-index: 2;
}

/* Add a subtle indicator to show it's clickable */
.handle-option::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: var(--color-primary);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.handle-option:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.handle-option.active::after {
  transform: scaleX(1);
}

.handle-image {
  width: 100%;
  height: 150px;
  object-fit: contain;
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius);
  padding: 0.5rem;
}

.handle-label {
  text-align: center;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.handle-description {
  font-size: 0.85rem;
  text-align: center;
  color: var(--color-secondary);
  opacity: 0.8;
}
