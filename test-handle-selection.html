<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Handle Selection Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .test-instructions {
      background-color: #f0f0f0;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .test-button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 5px;
    }
    .result {
      margin-top: 20px;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
  </style>
</head>
<body>
  <h1>Handle Selection Test</h1>
  
  <div class="test-instructions">
    <h2>Instructions</h2>
    <p>This page will help test if the handle selection functionality is working properly. Click the button below to run the test.</p>
  </div>
  
  <button id="testButton" class="test-button">Run Handle Selection Test</button>
  
  <div id="testResult" class="result"></div>
  
  <script>
    document.getElementById('testButton').addEventListener('click', function() {
      const resultDiv = document.getElementById('testResult');
      resultDiv.innerHTML = '<h3>Test Results:</h3>';
      
      // Check if handleHandleClick function exists
      if (typeof window.handleHandleClick === 'function') {
        resultDiv.innerHTML += '<p>✅ window.handleHandleClick function exists</p>';
      } else {
        resultDiv.innerHTML += '<p>❌ window.handleHandleClick function is missing</p>';
      }
      
      // Check if setupHandleOptions function exists
      if (typeof window.setupHandleOptions === 'function') {
        resultDiv.innerHTML += '<p>✅ window.setupHandleOptions function exists</p>';
      } else {
        resultDiv.innerHTML += '<p>❌ window.setupHandleOptions function is missing</p>';
      }
      
      // Check if HANDLE_LABELS exists
      if (window.HANDLE_LABELS) {
        resultDiv.innerHTML += '<p>✅ window.HANDLE_LABELS exists</p>';
      } else {
        resultDiv.innerHTML += '<p>❌ window.HANDLE_LABELS is missing</p>';
      }
      
      // Create a test handle option element
      const testHandleOption = document.createElement('div');
      testHandleOption.className = 'handle-option';
      testHandleOption.dataset.value = 'test-handle';
      
      // Try to call handleHandleClick
      try {
        window.handleHandleClick(testHandleOption, 'test-handle');
        resultDiv.innerHTML += '<p>✅ Successfully called window.handleHandleClick</p>';
      } catch (error) {
        resultDiv.innerHTML += `<p>❌ Error calling window.handleHandleClick: ${error.message}</p>`;
      }
      
      resultDiv.innerHTML += '<p>Test completed. Please check the main application to see if handle selection is now working.</p>';
    });
  </script>
</body>
</html>
