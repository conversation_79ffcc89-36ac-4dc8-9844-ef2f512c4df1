/* css/pdf.css */

/* Skriv ut utan marginaler i b<PERSON><PERSON><PERSON><PERSON><PERSON>, men jsPDF sköter sina egna */
@page { margin: 0 }

/* Wrapper för varje sida */
.pdf-page {
  position: relative;
  width: 210mm;     /* A4-bredd */
  min-height: 297mm;/* A4-höjd */
  padding: 15mm;    /* Inre marginal */
  box-sizing: border-box;
  background: #fff;
}

/* Titel */
.pdf-page .detail-title {
  font-family: "Segoe UI", sans-serif;
  font-size: 18pt;
  font-weight: bold;
  text-align: center;
  color: #222;
  margin-bottom: 8pt;
}

/* Preview */
.pdf-page .saved-container {
  margin: 0 auto 8pt;
  padding: 4pt;
  background: #f4f4f4;
  border: 1px solid #ccc;
  width: 60%;
  box-sizing: border-box;
}

/* Specifikation */
.pdf-page .saved-spec {
  margin: 0 auto 10pt;
  padding: 4pt;
  background: #fafafa;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 90%;
  box-sizing: border-box;
}

.pdf-page .saved-spec h4 {
  font-size: 12pt;
  margin-bottom: 4pt;
  border-bottom: 1px solid #ddd;
  padding-bottom: 2pt;
  color: #222;
}

.pdf-page .saved-spec table {
  width: 100%;
  border-collapse: collapse;
  font-size: 10pt;
}

.pdf-page .saved-spec th,
.pdf-page .saved-spec td {
  border: 1px solid #ddd;
  padding: 4pt 6pt;
  text-align: left;
}

.pdf-page .saved-spec th {
  background: #e0e0e0;
  color: #111;
}

/* Dölj alla knappar/actions i PDF */
.pdf-page .detail-actions {
  display: none !important;
}

/* Page-break (för säkerhets skull, jsPDF brukar ta hand om det själv) */
.pdf-page {
  page-break-after: always;
}
.pdf-page:last-of-type {
  page-break-after: auto;
}

.pdf-export-container.hidden {
    position: absolute;
    left: -9999px;
    top: -9999px;
    /* men INTE display:none */
  }

  .pdf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8mm;    /* luft under headern */
    padding: 0 5mm;
  }

  .pdf-logo {
    flex-shrink: 0;
    width: 30mm;           /* sätt önskad bredd på loggan */
  }

  .pdf-logo img {
    display: block;
    width: 100%;
    height: auto;
  }

  .pdf-export-date {
    font-size: 10pt;
    color: #444;
  }

/* Progress bar styles */
.pdf-progress-container {
  margin-top: 20px;
  width: 100%;
  max-width: 500px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pdf-progress-container.hidden {
  display: none;
}

.pdf-progress-text {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
  font-weight: 500;
}

.pdf-progress-bar-outer {
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.pdf-progress-bar-inner {
  height: 100%;
  background-color: var(--color-primary, #f5c700);
  width: 0%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.pdf-progress-status {
  text-align: center;
  margin-top: 5px;
  font-size: 14px;
  color: #555;
}