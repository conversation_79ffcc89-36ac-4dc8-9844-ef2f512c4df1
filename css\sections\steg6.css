/* Steg 6 - Glastyp */

.glass-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.glass-type-option {
  background: var(--color-panel);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 1.5rem;
  cursor: pointer;
  transition: all var(--transition);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  height: 100%;
}

.glass-type-option:hover {
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.glass-type-option.active {
  border: 2px solid var(--color-primary);
  box-shadow: 0 0 0 1px rgba(245, 199, 0, 0.5);
}

.glass-type-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1.5rem;
}

.glass-type-img {
  width: 140px;
  height: 140px;
  object-fit: contain;
  flex-shrink: 0;
  background: transparent;
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.glass-type-option:hover .glass-type-img {
  transform: scale(1.05);
}

.glass-type-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.glass-type-title {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--color-secondary);
  margin: 0 0 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.glass-type-description {
  font-size: 0.9rem;
  color: #999;
  line-height: 1.5;
  margin-bottom: 0.75rem;
}

/* Priserna är borttagna */

/* Anpassa för mobil */
@media (max-width: 768px) {
  .glass-type-grid {
    grid-template-columns: 1fr;
  }

  .glass-type-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .glass-type-img {
    margin-bottom: 1rem;
  }

  .glass-type-title {
    text-align: center;
  }

  .glass-type-description {
    text-align: center;
  }
}

/* Specialfall för härdat glas */
.glass-type-option[data-value="tempered"] {
  grid-column: 1 / -1;
  max-width: 50%;
  margin: 0 auto;
}
