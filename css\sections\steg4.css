/* Grupp för materialval */
.material-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  /* Materialknappar */
  .material-option-btn {
    flex: 1 1 calc(20% - 1rem);
    padding: 1rem 0.5rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-secondary);
    font-size: 1rem;
    text-align: center;
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition), transform var(--transition);
  }
  
  .material-option-btn:hover {
    background: rgba(245,199,0,0.1);
    border-color: var(--color-primary);
    transform: translateY(-2px);
  }
  
  /* Aktivt val */
  .material-option-btn.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
    transform: none;
  }