<?php
namespace IsofonsterConfigurator;

/**
 * Admin-klass för Isofönster Konfigurator
 */
class Admin {
    
    /**
     * Konstruktor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
        
        // AJAX för admin
        add_action('wp_ajax_isofonster_cleanup_sessions', array($this, 'ajax_cleanup_sessions'));
        add_action('wp_ajax_isofonster_export_designs', array($this, 'ajax_export_designs'));
    }
    
    /**
     * <PERSON>ägg till admin-meny
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Isofönster Konfigurator', 'isofonster-configurator'),
            __('Isofönster', 'isofonster-configurator'),
            'manage_options',
            'isofonster-configurator',
            array($this, 'admin_page'),
            'dashicons-admin-home',
            30
        );
        
        add_submenu_page(
            'isofonster-configurator',
            __('Inställningar', 'isofonster-configurator'),
            __('Inställningar', 'isofonster-configurator'),
            'manage_options',
            'isofonster-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'isofonster-configurator',
            __('Designs', 'isofonster-configurator'),
            __('Alla Designs', 'isofonster-configurator'),
            'manage_options',
            'isofonster-designs',
            array($this, 'designs_page')
        );
        
        add_submenu_page(
            'isofonster-configurator',
            __('Statistik', 'isofonster-configurator'),
            __('Statistik', 'isofonster-configurator'),
            'manage_options',
            'isofonster-statistics',
            array($this, 'statistics_page')
        );
    }
    
    /**
     * Registrera inställningar
     */
    public function register_settings() {
        register_setting('isofonster_settings', 'isofonster_options');
        
        add_settings_section(
            'isofonster_general',
            __('Allmänna inställningar', 'isofonster-configurator'),
            array($this, 'general_section_callback'),
            'isofonster_settings'
        );
        
        add_settings_field(
            'enable_save',
            __('Aktivera sparfunktion', 'isofonster-configurator'),
            array($this, 'checkbox_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'enable_save', 'default' => true)
        );
        
        add_settings_field(
            'enable_export',
            __('Aktivera PDF export', 'isofonster-configurator'),
            array($this, 'checkbox_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'enable_export', 'default' => true)
        );
        
        add_settings_field(
            'max_designs_per_user',
            __('Max designs per användare', 'isofonster-configurator'),
            array($this, 'number_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'max_designs_per_user', 'default' => 50, 'min' => 1, 'max' => 1000)
        );
        
        add_settings_field(
            'session_cleanup_days',
            __('Rensa sessionsdata efter (dagar)', 'isofonster-configurator'),
            array($this, 'number_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'session_cleanup_days', 'default' => 30, 'min' => 1, 'max' => 365)
        );
        
        add_settings_field(
            'company_logo',
            __('Företagslogotyp URL', 'isofonster-configurator'),
            array($this, 'text_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'company_logo', 'default' => '')
        );
        
        add_settings_field(
            'primary_color',
            __('Primärfärg', 'isofonster-configurator'),
            array($this, 'color_field_callback'),
            'isofonster_settings',
            'isofonster_general',
            array('field' => 'primary_color', 'default' => '#f5c700')
        );
    }
    
    /**
     * Huvudadmin-sida
     */
    public function admin_page() {
        $database = new Database();
        $stats = $database->get_statistics();
        
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/admin-main.php';
    }
    
    /**
     * Inställningssida
     */
    public function settings_page() {
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/admin-settings.php';
    }
    
    /**
     * Designs-sida
     */
    public function designs_page() {
        $database = new Database();
        
        // Hantera actions
        if (isset($_GET['action']) && isset($_GET['design_id'])) {
            $design_id = intval($_GET['design_id']);
            
            switch ($_GET['action']) {
                case 'delete':
                    if (wp_verify_nonce($_GET['_wpnonce'], 'delete_design_' . $design_id)) {
                        $database->delete_design($design_id);
                        add_settings_error('isofonster_messages', 'design_deleted', __('Design borttagen', 'isofonster-configurator'), 'updated');
                    }
                    break;
            }
        }
        
        // Hämta designs
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $offset = ($page - 1) * $per_page;
        $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
        
        $designs = $database->get_user_designs($per_page, $offset, $search);
        $total_designs = $database->count_user_designs($search);
        $total_pages = ceil($total_designs / $per_page);
        
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/admin-designs.php';
    }
    
    /**
     * Statistiksida
     */
    public function statistics_page() {
        $database = new Database();
        $stats = $database->get_statistics();
        
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/admin-statistics.php';
    }
    
    /**
     * Admin-meddelanden
     */
    public function admin_notices() {
        settings_errors('isofonster_messages');
    }
    
    /**
     * Callback för allmän sektion
     */
    public function general_section_callback() {
        echo '<p>' . __('Konfigurera grundläggande inställningar för Isofönster Konfigurator.', 'isofonster-configurator') . '</p>';
    }
    
    /**
     * Checkbox-fält callback
     */
    public function checkbox_field_callback($args) {
        $options = get_option('isofonster_options', array());
        $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];
        
        echo '<input type="checkbox" id="' . esc_attr($args['field']) . '" name="isofonster_options[' . esc_attr($args['field']) . ']" value="1" ' . checked(1, $value, false) . ' />';
    }
    
    /**
     * Number-fält callback
     */
    public function number_field_callback($args) {
        $options = get_option('isofonster_options', array());
        $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];
        
        echo '<input type="number" id="' . esc_attr($args['field']) . '" name="isofonster_options[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" min="' . esc_attr($args['min']) . '" max="' . esc_attr($args['max']) . '" />';
    }
    
    /**
     * Text-fält callback
     */
    public function text_field_callback($args) {
        $options = get_option('isofonster_options', array());
        $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];
        
        echo '<input type="text" id="' . esc_attr($args['field']) . '" name="isofonster_options[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" class="regular-text" />';
    }
    
    /**
     * Color-fält callback
     */
    public function color_field_callback($args) {
        $options = get_option('isofonster_options', array());
        $value = isset($options[$args['field']]) ? $options[$args['field']] : $args['default'];
        
        echo '<input type="color" id="' . esc_attr($args['field']) . '" name="isofonster_options[' . esc_attr($args['field']) . ']" value="' . esc_attr($value) . '" />';
    }
    
    /**
     * AJAX: Rensa sessionsdata
     */
    public function ajax_cleanup_sessions() {
        check_ajax_referer('isofonster_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Otillräckliga behörigheter', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $options = get_option('isofonster_options', array());
        $days = isset($options['session_cleanup_days']) ? $options['session_cleanup_days'] : 30;
        
        $deleted = $database->cleanup_old_sessions($days);
        
        wp_send_json_success(array(
            'message' => sprintf(__('%d gamla sessioner rensades', 'isofonster-configurator'), $deleted),
            'deleted' => $deleted
        ));
    }
    
    /**
     * AJAX: Exportera designs
     */
    public function ajax_export_designs() {
        check_ajax_referer('isofonster_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Otillräckliga behörigheter', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $designs = $database->get_user_designs(1000); // Hämta upp till 1000 designs
        
        // Skapa CSV-export
        $csv_data = array();
        $csv_data[] = array(
            'ID',
            'Namn',
            'Typ',
            'Antal',
            'Bredd',
            'Höjd',
            'Skapad',
            'Uppdaterad'
        );
        
        foreach ($designs as $design) {
            $csv_data[] = array(
                $design['id'],
                $design['name'],
                $design['type'],
                $design['quantity'],
                $design['width'],
                $design['height'],
                $design['created_at'],
                $design['updated_at']
            );
        }
        
        // Skapa temporär fil
        $upload_dir = wp_upload_dir();
        $filename = 'isofonster_designs_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = $upload_dir['basedir'] . '/' . $filename;
        
        $file = fopen($filepath, 'w');
        foreach ($csv_data as $row) {
            fputcsv($file, $row);
        }
        fclose($file);
        
        wp_send_json_success(array(
            'download_url' => $upload_dir['baseurl'] . '/' . $filename,
            'filename' => $filename
        ));
    }
    
    /**
     * Hämta plugin-inställningar
     */
    public static function get_option($key, $default = null) {
        $options = get_option('isofonster_options', array());
        return isset($options[$key]) ? $options[$key] : $default;
    }
}
