/**
 * configurator-selection.js
 * Hanterar val mellan fönster- och dörrkonfigurator
 */

document.addEventListener('DOMContentLoaded', () => {
  // Hämta DOM-element
  const configuratorSelection = document.getElementById('configuratorSelection');
  const windowConfigurator = document.getElementById('windowConfigurator');
  const doorConfigurator = document.getElementById('doorConfigurator');
  const startWindowConfiguratorBtn = document.getElementById('startWindowConfigurator');
  const startDoorConfiguratorBtn = document.getElementById('startDoorConfigurator');
  const navNewDesignBtn = document.getElementById('navNewDesign');

  // Funktion för att visa fönsterkonfiguratorn
  function showWindowConfigurator() {
    configuratorSelection.classList.add('hidden');
    windowConfigurator.classList.remove('hidden');

    // Uppdatera förhandsvisningen
    if (typeof updateAll === 'function') {
      setTimeout(updateAll, 100);
    }

    // Öppna första steget
    if (typeof goTo === 'function') {
      goTo(0);
    }
  }

  // Funktion för att visa dörrkonfiguratorn
  function showDoorConfigurator() {
    configuratorSelection.classList.add('hidden');
    doorConfigurator.classList.remove('hidden');

    // Uppdatera förhandsvisningen
    if (typeof updateDoorAll === 'function') {
      setTimeout(updateDoorAll, 100);
    }

    // Öppna första steget
    if (typeof doorGoTo === 'function') {
      doorGoTo(0);
    }
  }

  // Funktion för att visa valsidan
  function showConfiguratorSelection() {
    windowConfigurator.classList.add('hidden');
    doorConfigurator.classList.add('hidden');
    configuratorSelection.classList.remove('hidden');
  }

  // Lägg till händelselyssnare för knapparna och hela boxarna
  if (startWindowConfiguratorBtn) {
    startWindowConfiguratorBtn.addEventListener('click', showWindowConfigurator);
  }

  if (startDoorConfiguratorBtn) {
    startDoorConfiguratorBtn.addEventListener('click', showDoorConfigurator);
  }

  // Gör hela boxarna klickbara
  const windowConfiguratorOption = document.getElementById('windowConfiguratorOption');
  const doorConfiguratorOption = document.getElementById('doorConfiguratorOption');

  if (windowConfiguratorOption) {
    windowConfiguratorOption.addEventListener('click', (event) => {
      // Kontrollera att klicket inte var på knappen (för att undvika dubbla klick-händelser)
      if (event.target !== startWindowConfiguratorBtn && !startWindowConfiguratorBtn.contains(event.target)) {
        showWindowConfigurator();
      }
    });
  }

  if (doorConfiguratorOption) {
    doorConfiguratorOption.addEventListener('click', (event) => {
      // Kontrollera att klicket inte var på knappen (för att undvika dubbla klick-händelser)
      if (event.target !== startDoorConfiguratorBtn && !startDoorConfiguratorBtn.contains(event.target)) {
        showDoorConfigurator();
      }
    });
  }

  // Överskugga standardbeteendet för "Ny Design"-knappen
  if (navNewDesignBtn) {
    // Spara originalbeteendet
    const originalClickHandler = navNewDesignBtn.onclick;

    // Ersätt med nytt beteende
    navNewDesignBtn.addEventListener('click', (event) => {
      // Visa valsidan
      showConfiguratorSelection();
    });
  }

  // Hantera tillbaka-knapp från fönsterkonfiguratorn
  document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape' && !configuratorSelection.classList.contains('hidden')) {
      showConfiguratorSelection();
    }
  });

  // Visa valsidan när sidan laddas
  showConfiguratorSelection();
});
