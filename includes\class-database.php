<?php
namespace IsofonsterConfigurator;

/**
 * Databasklass för Isofönster Konfigurator
 */
class Database {
    
    /**
     * Tabellnamn
     */
    private $table_name;
    
    /**
     * Konstruktor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'isofonster_designs';
    }
    
    /**
     * Skapa databastabeller
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL DEFAULT 0,
            session_id varchar(255) NOT NULL DEFAULT '',
            name varchar(255) NOT NULL,
            type varchar(50) NOT NULL DEFAULT 'window',
            quantity int(11) NOT NULL DEFAULT 1,
            width decimal(10,2) NOT NULL,
            height decimal(10,2) NOT NULL,
            design_data longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY type (type),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Spara design
     */
    public function save_design($design_data) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $data = array(
            'user_id' => $user_id,
            'session_id' => $session_id,
            'name' => $design_data['name'],
            'type' => $design_data['type'],
            'quantity' => $design_data['quantity'],
            'width' => $design_data['width'],
            'height' => $design_data['height'],
            'design_data' => wp_json_encode($design_data),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert($this->table_name, $data);
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Uppdatera design
     */
    public function update_design($design_id, $design_data) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $data = array(
            'name' => $design_data['name'],
            'type' => $design_data['type'],
            'quantity' => $design_data['quantity'],
            'width' => $design_data['width'],
            'height' => $design_data['height'],
            'design_data' => wp_json_encode($design_data),
            'updated_at' => current_time('mysql')
        );
        
        $where = array('id' => $design_id);
        
        // Säkerställ att användaren äger designen eller att det är samma session
        if ($user_id > 0) {
            $where['user_id'] = $user_id;
        } else {
            $where['session_id'] = $session_id;
        }
        
        $result = $wpdb->update($this->table_name, $data, $where);
        
        return $result !== false;
    }
    
    /**
     * Hämta design
     */
    public function get_design($design_id) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $where_clause = "id = %d";
        $where_values = array($design_id);
        
        // Säkerställ att användaren äger designen eller att det är samma session
        if ($user_id > 0) {
            $where_clause .= " AND user_id = %d";
            $where_values[] = $user_id;
        } else {
            $where_clause .= " AND session_id = %s";
            $where_values[] = $session_id;
        }
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE {$where_clause}",
            $where_values
        );
        
        $result = $wpdb->get_row($sql, ARRAY_A);
        
        if ($result) {
            $result['design_data'] = json_decode($result['design_data'], true);
            return $result;
        }
        
        return false;
    }
    
    /**
     * Hämta alla designs för användare
     */
    public function get_user_designs($limit = 50, $offset = 0, $search = '', $order_by = 'created_at', $order = 'DESC') {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $where_clause = "";
        $where_values = array();
        
        // Säkerställ att användaren bara ser sina egna designs
        if ($user_id > 0) {
            $where_clause = "user_id = %d";
            $where_values[] = $user_id;
        } else {
            $where_clause = "session_id = %s";
            $where_values[] = $session_id;
        }
        
        // Lägg till sökfilter
        if (!empty($search)) {
            $where_clause .= " AND name LIKE %s";
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }
        
        // Säkerställ giltiga sorteringsparametrar
        $allowed_order_by = array('id', 'name', 'type', 'created_at', 'updated_at');
        $allowed_order = array('ASC', 'DESC');
        
        if (!in_array($order_by, $allowed_order_by)) {
            $order_by = 'created_at';
        }
        
        if (!in_array(strtoupper($order), $allowed_order)) {
            $order = 'DESC';
        }
        
        $sql = $wpdb->prepare(
            "SELECT id, name, type, quantity, width, height, created_at, updated_at 
             FROM {$this->table_name} 
             WHERE {$where_clause} 
             ORDER BY {$order_by} {$order} 
             LIMIT %d OFFSET %d",
            array_merge($where_values, array($limit, $offset))
        );
        
        $results = $wpdb->get_results($sql, ARRAY_A);
        
        return $results ? $results : array();
    }
    
    /**
     * Ta bort design
     */
    public function delete_design($design_id) {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $where = array('id' => $design_id);
        
        // Säkerställ att användaren äger designen eller att det är samma session
        if ($user_id > 0) {
            $where['user_id'] = $user_id;
        } else {
            $where['session_id'] = $session_id;
        }
        
        $result = $wpdb->delete($this->table_name, $where);
        
        return $result !== false;
    }
    
    /**
     * Räkna designs för användare
     */
    public function count_user_designs($search = '') {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $session_id = $this->get_session_id();
        
        $where_clause = "";
        $where_values = array();
        
        // Säkerställ att användaren bara ser sina egna designs
        if ($user_id > 0) {
            $where_clause = "user_id = %d";
            $where_values[] = $user_id;
        } else {
            $where_clause = "session_id = %s";
            $where_values[] = $session_id;
        }
        
        // Lägg till sökfilter
        if (!empty($search)) {
            $where_clause .= " AND name LIKE %s";
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }
        
        $sql = $wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE {$where_clause}",
            $where_values
        );
        
        return (int) $wpdb->get_var($sql);
    }
    
    /**
     * Hämta session ID
     */
    private function get_session_id() {
        if (!session_id()) {
            session_start();
        }
        
        if (!isset($_SESSION['isofonster_session_id'])) {
            $_SESSION['isofonster_session_id'] = wp_generate_uuid4();
        }
        
        return $_SESSION['isofonster_session_id'];
    }
    
    /**
     * Rensa gamla sessionsdata
     */
    public function cleanup_old_sessions($days = 30) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $sql = $wpdb->prepare(
            "DELETE FROM {$this->table_name} 
             WHERE user_id = 0 
             AND session_id != '' 
             AND created_at < %s",
            $cutoff_date
        );
        
        return $wpdb->query($sql);
    }
    
    /**
     * Hämta statistik
     */
    public function get_statistics() {
        global $wpdb;
        
        $stats = array();
        
        // Totalt antal designs
        $stats['total_designs'] = (int) $wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name}"
        );
        
        // Antal fönsterdesigns
        $stats['window_designs'] = (int) $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE type = %s",
                'window'
            )
        );
        
        // Antal dörrdesigns
        $stats['door_designs'] = (int) $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE type = %s",
                'door'
            )
        );
        
        // Antal registrerade användare med designs
        $stats['users_with_designs'] = (int) $wpdb->get_var(
            "SELECT COUNT(DISTINCT user_id) FROM {$this->table_name} WHERE user_id > 0"
        );
        
        // Designs skapade senaste 30 dagarna
        $stats['recent_designs'] = (int) $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE created_at >= %s",
                date('Y-m-d H:i:s', strtotime('-30 days'))
            )
        );
        
        return $stats;
    }
}
