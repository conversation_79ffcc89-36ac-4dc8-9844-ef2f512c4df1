<?php
namespace IsofonsterConfigurator;

/**
 * AJAX Handler för Isofönster Konfigurator
 */
class AjaxHandler {
    
    /**
     * Spara design
     */
    public function save_design() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        // Sanitera och validera input
        $design_data = $this->sanitize_design_data($_POST);
        
        if (!$design_data) {
            wp_send_json_error(__('Ogiltiga designdata', 'isofonster-configurator'));
        }
        
        // Spara till databas
        $database = new Database();
        $design_id = $database->save_design($design_data);
        
        if ($design_id) {
            wp_send_json_success(array(
                'message' => __('Design sparad!', 'isofonster-configurator'),
                'design_id' => $design_id
            ));
        } else {
            wp_send_json_error(__('Fel vid sparande', 'isofonster-configurator'));
        }
    }
    
    /**
     * Ladda design
     */
    public function load_design() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $design_id = intval($_POST['design_id']);
        
        if (!$design_id) {
            wp_send_json_error(__('Ogiltigt design-ID', 'isofonster-configurator'));
        }
        
        // Ladda från databas
        $database = new Database();
        $design = $database->get_design($design_id);
        
        if ($design) {
            wp_send_json_success($design);
        } else {
            wp_send_json_error(__('Design hittades inte', 'isofonster-configurator'));
        }
    }
    
    /**
     * Ladda alla designs för användare
     */
    public function load_user_designs() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $designs = $database->get_user_designs();
        
        wp_send_json_success($designs);
    }
    
    /**
     * Ta bort design
     */
    public function delete_design() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $design_id = intval($_POST['design_id']);
        
        if (!$design_id) {
            wp_send_json_error(__('Ogiltigt design-ID', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $success = $database->delete_design($design_id);
        
        if ($success) {
            wp_send_json_success(__('Design borttagen', 'isofonster-configurator'));
        } else {
            wp_send_json_error(__('Fel vid borttagning', 'isofonster-configurator'));
        }
    }
    
    /**
     * Exportera PDF
     */
    public function export_pdf() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $design_ids = array_map('intval', $_POST['design_ids']);
        
        if (empty($design_ids)) {
            wp_send_json_error(__('Inga designs valda', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $designs = array();
        
        foreach ($design_ids as $design_id) {
            $design = $database->get_design($design_id);
            if ($design) {
                $designs[] = $design;
            }
        }
        
        if (empty($designs)) {
            wp_send_json_error(__('Inga giltiga designs hittades', 'isofonster-configurator'));
        }
        
        // Generera PDF (detta skulle kunna göras med en separat PDF-klass)
        $pdf_url = $this->generate_pdf($designs);
        
        if ($pdf_url) {
            wp_send_json_success(array(
                'pdf_url' => $pdf_url,
                'message' => __('PDF genererad!', 'isofonster-configurator')
            ));
        } else {
            wp_send_json_error(__('Fel vid PDF-generering', 'isofonster-configurator'));
        }
    }
    
    /**
     * Sanitera designdata
     */
    private function sanitize_design_data($data) {
        $sanitized = array();
        
        // Grundläggande fält
        $sanitized['name'] = sanitize_text_field($data['name'] ?? '');
        $sanitized['type'] = sanitize_text_field($data['type'] ?? 'window');
        $sanitized['quantity'] = intval($data['quantity'] ?? 1);
        
        // Dimensioner
        $sanitized['width'] = floatval($data['width'] ?? 0);
        $sanitized['height'] = floatval($data['height'] ?? 0);
        
        // Fönsterspecifika fält
        if ($sanitized['type'] === 'window') {
            $sanitized['count'] = intval($data['count'] ?? 1);
            $sanitized['openings'] = array_map('sanitize_text_field', $data['openings'] ?? array());
            $sanitized['hinges'] = array_map('sanitize_text_field', $data['hinges'] ?? array());
            $sanitized['openSizes'] = array_map('floatval', $data['openSizes'] ?? array());
            $sanitized['material'] = sanitize_text_field($data['material'] ?? '');
            $sanitized['glassType'] = sanitize_text_field($data['glassType'] ?? '');
            $sanitized['glassTypeValue'] = sanitize_text_field($data['glassTypeValue'] ?? '');
            $sanitized['insideColor'] = sanitize_text_field($data['insideColor'] ?? 'white');
            $sanitized['outsideColor'] = sanitize_text_field($data['outsideColor'] ?? 'white');
            $sanitized['sprojsType'] = sanitize_text_field($data['sprojsType'] ?? 'none');
            $sanitized['handle'] = sanitize_text_field($data['handle'] ?? 'stormkrok');
            $sanitized['mosquitoNet'] = (bool) ($data['mosquitoNet'] ?? false);
            
            // Anpassade spröjs
            if (isset($data['customSprojsPerLuft']) && is_array($data['customSprojsPerLuft'])) {
                $sanitized['customSprojsPerLuft'] = array();
                foreach ($data['customSprojsPerLuft'] as $sprojs) {
                    $sanitized['customSprojsPerLuft'][] = array(
                        'horizontal' => intval($sprojs['horizontal'] ?? 2),
                        'vertical' => intval($sprojs['vertical'] ?? 2)
                    );
                }
            }
        }
        
        // Dörrspecifika fält
        if ($sanitized['type'] === 'door') {
            $sanitized['opening'] = sanitize_text_field($data['opening'] ?? 'fixed');
            $sanitized['hinge'] = sanitize_text_field($data['hinge'] ?? '');
            $sanitized['material'] = sanitize_text_field($data['material'] ?? '');
            $sanitized['glassType'] = sanitize_text_field($data['glassType'] ?? '');
            $sanitized['insideColor'] = sanitize_text_field($data['insideColor'] ?? 'white');
            $sanitized['outsideColor'] = sanitize_text_field($data['outsideColor'] ?? 'white');
            $sanitized['handle'] = sanitize_text_field($data['handle'] ?? 'standard');
            $sanitized['panelHeight'] = floatval($data['panelHeight'] ?? 0);
        }
        
        // Validera att obligatoriska fält finns
        if (empty($sanitized['name']) || $sanitized['width'] <= 0 || $sanitized['height'] <= 0) {
            return false;
        }
        
        return $sanitized;
    }
    
    /**
     * Generera PDF (placeholder - skulle kunna implementeras med TCPDF eller liknande)
     */
    private function generate_pdf($designs) {
        // Detta är en placeholder-implementation
        // I en riktig implementation skulle du använda ett PDF-bibliotek som TCPDF
        
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/isofonster-pdfs/';
        
        // Skapa mapp om den inte finns
        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }
        
        $filename = 'designs_' . date('Y-m-d_H-i-s') . '.pdf';
        $filepath = $pdf_dir . $filename;
        
        // Här skulle PDF-genereringen ske
        // För nu returnerar vi bara en placeholder-URL
        
        return $upload_dir['baseurl'] . '/isofonster-pdfs/' . $filename;
    }
    
    /**
     * Uppdatera design
     */
    public function update_design() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $design_id = intval($_POST['design_id']);
        
        if (!$design_id) {
            wp_send_json_error(__('Ogiltigt design-ID', 'isofonster-configurator'));
        }
        
        // Sanitera och validera input
        $design_data = $this->sanitize_design_data($_POST);
        
        if (!$design_data) {
            wp_send_json_error(__('Ogiltiga designdata', 'isofonster-configurator'));
        }
        
        // Uppdatera i databas
        $database = new Database();
        $success = $database->update_design($design_id, $design_data);
        
        if ($success) {
            wp_send_json_success(__('Design uppdaterad!', 'isofonster-configurator'));
        } else {
            wp_send_json_error(__('Fel vid uppdatering', 'isofonster-configurator'));
        }
    }
    
    /**
     * Duplicera design
     */
    public function duplicate_design() {
        // Verifiera nonce
        if (!wp_verify_nonce($_POST['nonce'], 'isofonster_nonce')) {
            wp_die(__('Säkerhetsfel', 'isofonster-configurator'));
        }
        
        $design_id = intval($_POST['design_id']);
        
        if (!$design_id) {
            wp_send_json_error(__('Ogiltigt design-ID', 'isofonster-configurator'));
        }
        
        $database = new Database();
        $original_design = $database->get_design($design_id);
        
        if (!$original_design) {
            wp_send_json_error(__('Design hittades inte', 'isofonster-configurator'));
        }
        
        // Skapa kopia
        $copy_data = $original_design;
        $copy_data['name'] = $copy_data['name'] . ' (Kopia)';
        unset($copy_data['id']); // Ta bort ID så att en ny skapas
        
        $new_design_id = $database->save_design($copy_data);
        
        if ($new_design_id) {
            wp_send_json_success(array(
                'message' => __('Design duplicerad!', 'isofonster-configurator'),
                'design_id' => $new_design_id
            ));
        } else {
            wp_send_json_error(__('Fel vid duplicering', 'isofonster-configurator'));
        }
    }
}
