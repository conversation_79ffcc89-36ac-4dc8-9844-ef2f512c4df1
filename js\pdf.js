// js/pdf.js

/**
 * PDF-export för offert, version 2.2 (korrigerad)
 * Uppdateringar:
 * 1) Logo på första sidan ocks<PERSON>
 * 2) Mellanrum mellan logo och sektionstitlar (Innehållsförteckning, Villkor, etc.)
 * 3) Syntaxfel åtgärdat (PAGE_W/2 + MARGIN)
 */
document.addEventListener('DOMContentLoaded', () => {
  const exportBtn = document.getElementById('exportBtn');
  const progressContainer = document.getElementById('pdfProgressContainer');
  const progressBar = document.getElementById('pdfProgressBar');
  const progressStatus = document.getElementById('pdfProgressStatus');

  if (!exportBtn) return;

  // Funktion för att uppdatera progress bar
  function updateProgress(percent) {
    if (progressBar && progressStatus) {
      progressBar.style.width = `${percent}%`;
      progressStatus.textContent = `${Math.round(percent)}%`;
    }
  }

  // Funktion för att visa progress bar
  function showProgressBar() {
    if (progressContainer) {
      progressContainer.classList.remove('hidden');
      updateProgress(0);
    }
  }

  // Funktion för att dölja progress bar
  function hideProgressBar() {
    if (progressContainer) {
      // Vänta lite innan vi döljer progress bar så användaren hinner se 100%
      setTimeout(() => {
        progressContainer.classList.add('hidden');
      }, 1000);
    }
  }

  exportBtn.addEventListener('click', async () => {
    try {
      // --- 1) Hämta och validera sparade designs ---
      const designs = JSON.parse(localStorage.getItem('myDesigns') || '[]');
      if (!designs.length) {
        return alert('Du har inga sparade designs att exportera.');
      }

      // Visa progress bar
      showProgressBar();

      // --- 2) Initiera jsPDF ---
      const { jsPDF } = window.jspdf;
      const pdf = new jsPDF({
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
        compress: true
      });

      // --- 3) Globala mått och layoutegenskaper ---
      const PAGE_W   = pdf.internal.pageSize.getWidth();
      const PAGE_H   = pdf.internal.pageSize.getHeight();
      const MARGIN   = 15;
      const COL_GAP  = 10;
      const IMG_W    = (PAGE_W - 2 * MARGIN - COL_GAP) * 0.4;
      const LINE_H   = 7;
      const LOGO_MAX = { width: 100, height: 80 };

      // --- 4) Konfigurationsobjekt ---
      const CONFIG = {
        labels: {
          tra: 'Trä',
          pvc: 'PVC',
          aluminium: 'Aluminium',
          'tra-aluminium': 'Trä/Aluminium',
          'pvc-aluminium': 'PVC/Aluminium'
        },
        glassTypes: {
          'double': '2-glas',
          'triple': '3-glas'
        },
        glassTypeLabels: {
          'clear': 'Klart glas',
          'sound': 'Ljudreducerande glas',
          'sun': 'Solskyddsglas',
          'granite': 'Granit',
          'cotswold': 'Cotswold',
          'frosted': 'Frostat glas',
          'tempered': 'Härdat glas'
        },
        sprojsLabels: {
          'none': 'Inga spröjs',
          'one-horizontal': 'En horisontell',
          'one-vertical': 'En vertikal',
          'cross': 'Kors',
          'grid': 'Rutnät',
          'custom': 'Anpassad'
        },
        colorLabels: {
          'white': 'Vit',
          'gray': 'Grå',
          'black': 'Svart',
          'anthracite': 'Antracitgrå',
          'darkgray': 'Mörkgrå',
          'silver': 'Silver',
          'beige': 'Beige'
        },
        mosquitoNetLabels: {
          'true': 'Med myggnät',
          'false': 'Utan myggnät'
        },
        company: {
          name:    'Isofönster AB',
          address: 'Bällstavägen 57, 168 72 Stockholm',
          phone:   '070-743 28 32',
          email:   '<EMAIL>',
          vat:     'SE5568717317',
          website: 'www.isofonster.se'
        },
        sprojsLabels: {
          'none': 'Inga spröjs',
          'one-horizontal': 'En horisontell',
          'one-vertical': 'En vertikal',
          'cross': 'Kors',
          'grid': 'Rutnät',
          'custom': 'Anpassad'
        },
        handleLabels: {
          'none': 'Inget handtag',
          'stormkrok': 'Handtag med stormkrok',
          'barnlas-stormkrok': 'Handtag med barnlås och stormkrok',
          'krom': 'Kromhandtag',
          'vitt': 'Vitt handtag',
          'vitt-las': 'Vitt handtag med lås',
          'krom-las-nyckel': 'Kromhandtag med lås + nyckel (barnsäker)'
        },
        doorOpeningLabels: {
          'fixed': 'Fast',
          'side-left': 'Sidohängd vänster',
          'side-right': 'Sidohängd höger',
          'sliding-left': 'Skjutdörr vänster',
          'sliding-right': 'Skjutdörr höger'
        },
        dates: {
          today:        new Date().toLocaleDateString('sv-SE'),
          validityDays: 30
        },
        page: {
          coverTitle:  'OFFERT',
          coverFont:   { name: 'helvetica', style: 'bold', size: 28 },
          normalFont:  { name: 'helvetica', style: 'normal', size: 12 },
          smallFont:   { name: 'helvetica', style: 'normal', size: 10 },
          footerFont:  { name: 'helvetica', style: 'normal', size: 9 }
        },
        price: {
          showPerDesign: true,
          currency:      'SEK'
        },
        terms: [
          'Betalningsvillkor: 30 dagar netto.',
          'Leveransvillkor: Incoterms 2020 FOB.',
          'Garanti: 5 år mot fabrikationsfel.',
          'Offerten är giltig i 30 dagar från offertdatum.'
        ]
      };

      // --- 5) Ladda logotyp ---
      const logoUrl = 'logo.png';
      const logoImg = new Image();
      logoImg.src = logoUrl;
      await new Promise(resolve => logoImg.onload = resolve);

      // --- 6) Skala logotyp inom maxmått ---
      const logoRatio = logoImg.width / logoImg.height;
      let logoW = LOGO_MAX.width;
      let logoH = LOGO_MAX.width / logoRatio;
      if (logoH > LOGO_MAX.height) {
        logoH = LOGO_MAX.height;
        logoW = LOGO_MAX.height * logoRatio;
      }

      // --- 7) Beräkna header- och mellanrumshöjder ---
      const LOGO_SCALE    = 0.8;
      const scaledLogoW   = logoW * LOGO_SCALE;
      const scaledLogoH   = logoH * LOGO_SCALE;
      const HEADER_BOTTOM = MARGIN + scaledLogoH + 5;
      const HEADER_GAP_Y  = 10;

      // --- 8) Förbered innehållsförteckning och sidräknare ---
      const tocEntries  = [];
      let   currentPage = 1;

      // Istället för att försöka beräkna antalet sidor i förväg,
      // kommer vi att räkna dem dynamiskt under genereringen

      // Skapa en variabel för att hålla reda på det faktiska antalet sidor
      let actualTotalPages = 0;

      // Vi uppdaterar sidnumreringen i slutet av PDF-genereringen
      // när vi vet det faktiska antalet sidor

      // Vi använder actualTotalPages för att hålla reda på det faktiska antalet sidor
      // Ingen temporär totalPages-variabel behövs längre

      // --- 9) Ritfunktioner ---

      /**
       * Rita omslagssida med logo
       */
      function drawCover() {
        // Logo
        pdf.addImage(logoImg, 'PNG', MARGIN, MARGIN, scaledLogoW, scaledLogoH);

        // Titel
        pdf.setFont(CONFIG.page.coverFont.name, CONFIG.page.coverFont.style);
        pdf.setFontSize(CONFIG.page.coverFont.size);
        pdf.text(CONFIG.page.coverTitle, PAGE_W / 2, PAGE_H / 3, { align: 'center' });

        // Datum
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(CONFIG.page.normalFont.size);
        pdf.text(`Datum: ${CONFIG.dates.today}`, PAGE_W / 2, PAGE_H / 3 + 12, { align: 'center' });
        pdf.text(
          `Giltig till: ${new Date(Date.now() + CONFIG.dates.validityDays * 86400000).toLocaleDateString('sv-SE')}`,
          PAGE_W / 2,
          PAGE_H / 3 + 20,
          { align: 'center' }
        );

        // Företagsinfo längst ner
        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        [
          CONFIG.company.name,
          CONFIG.company.address,
          `Tel: ${CONFIG.company.phone} • ${CONFIG.company.email}`,
          `Org.nr: ${CONFIG.company.vat} • ${CONFIG.company.website}`
        ].forEach((ln, i) => {
          pdf.text(ln, PAGE_W / 2, PAGE_H - 40 + i * 5, { align: 'center' });
        });

        currentPage++;
      }

      /**
       * Rita sidhuvud (logo + datum) och sidfot
       * @param {Number} pageNum - Sidnummer
       */
      function drawHeaderFooter(pageNum) {
        // Logo
        pdf.addImage(logoImg, 'PNG', MARGIN, MARGIN, scaledLogoW, scaledLogoH);

        // Datum under logo
        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        pdf.text(
          `Offertdatum: ${CONFIG.dates.today}`,
          PAGE_W - MARGIN,
          MARGIN + scaledLogoH + 5,
          { align: 'right' }
        );

        // Sidfot - rita bara linjen under genereringen, sidnummer läggs till senare
        pdf.setDrawColor(200);
        pdf.setLineWidth(0.3);
        pdf.line(MARGIN, PAGE_H - 15, PAGE_W - MARGIN, PAGE_H - 15);

        // Räkna upp sidnumret
        currentPage++;

        // Uppdatera det faktiska antalet sidor
        actualTotalPages = Math.max(actualTotalPages, currentPage - 1);

        // Logga sidnumrering för felsökning
        console.log(`Renderar sida ${pageNum}, aktuellt antal sidor: ${actualTotalPages}`);
      }

      /* Temporärt kommenterad - kommer att användas senare
      function drawToc() {
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yStart = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(16);
        pdf.text('Innehållsförteckning', MARGIN, yStart);

        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        let y = yStart + LINE_H * 1.5;
        tocEntries.forEach(entry => {
          pdf.text(entry.title, MARGIN + 5, y);
          pdf.text(String(entry.page), PAGE_W - MARGIN, y, { align: 'right' });
          y += LINE_H;
        });
      }
      */

      /**
       * Rendera en design
       * @param {Object} cfg - Designkonfiguration
       * @param {Number} index - Index för designen
       * @param {Boolean} isDoorSection - Om designen är i dörrsektionen (för att undvika att lägga till i TOC)
       */
      async function renderDesign(cfg, index, isDoorSection = false) {
        // Lägg bara till i innehållsförteckningen om det inte är en dörr i dörrsektionen
        // (eftersom dörrsektionen redan har en egen rubrik i innehållsförteckningen)
        if (!isDoorSection) {
          tocEntries.push({ title: cfg.name, page: currentPage });
        }

        // Beräkna ungefärlig höjd som behövs för denna design
        // Baserat på bildstorlek och specifikationer
        const isDesignDoor = cfg.type === 'door';
        const estimatedImgHeight = isDesignDoor ? 70 : 80; // mm, uppskattad höjd för bild
        const estimatedSpecsHeight = 15 + (10 * (isDesignDoor ? 8 : 10)); // mm, uppskattad höjd för specifikationer
        const estimatedTotalHeight = estimatedImgHeight + estimatedSpecsHeight + 20; // mm, med lite extra marginal

        // Beräkna återstående utrymme på nuvarande sida
        const currentY = pdf.internal.getCurrentPageInfo().pageContext.y || HEADER_BOTTOM + HEADER_GAP_Y;
        const remainingSpace = PAGE_H - currentY - MARGIN;

        // Kontrollera om vi behöver en ny sida
        // Vi behöver en ny sida om det inte finns tillräckligt med plats på nuvarande sida
        // Vi skapar INTE en ny sida om vi redan är på en sida med en kategori-rubrik (currentY är nära toppen)
        const isOnCategoryPage = currentY < (HEADER_BOTTOM + HEADER_GAP_Y + 30); // Om vi är inom 30mm från toppen av innehållsområdet

        if (remainingSpace < estimatedTotalHeight && !isOnCategoryPage) {
          pdf.addPage();
          drawHeaderFooter(currentPage - 1);
          // Återställ Y-positionen till toppen av innehållsområdet på den nya sidan
          pdf.internal.getCurrentPageInfo().pageContext.y = HEADER_BOTTOM + HEADER_GAP_Y;
        }

        // Använd showSavedDetail för att skapa förhandsvisningen
        showSavedDetail(cfg, index);
        await new Promise(r => setTimeout(r, 100));

        // Hämta den skapade förhandsvisningen
        const detailEl = document.getElementById('savedDetail');
        const preview = detailEl.querySelector('.preview-container.saved-container');

        // Skapa en klon av förhandsvisningen
        const tempContainer = preview.cloneNode(true);
        tempContainer.style.position = 'absolute';
        tempContainer.style.top = '-9999px';
        tempContainer.style.left = '-9999px';
        tempContainer.style.backgroundColor = '#fff';

        // Sätt fast höjd för både dörrar och fönster för konsekvent utseende
        // Beräkna bredd baserat på proportioner
        const aspectRatio = cfg.width / cfg.height;
        const fixedHeight = 350; // Fast höjd i pixlar
        const calculatedWidth = fixedHeight * aspectRatio;

        // Sätt höjd och bredd med extra marginal för att säkerställa att hela elementet syns
        tempContainer.style.height = `${fixedHeight}px`;
        tempContainer.style.width = `${calculatedWidth + 50}px`; // Lägg till extra bredd

        if (cfg.type === 'door') {
          // Säkerställ att dörr-preview har rätt stil
          const doorPreview = tempContainer.querySelector('.door-preview');
          if (doorPreview) {
            doorPreview.style.width = '100%';
            doorPreview.style.height = '100%';

            // Säkerställ att alla dörr-frames är synliga
            const doorFrames = doorPreview.querySelectorAll('.door-frame');
            doorFrames.forEach(frame => {
              frame.style.flex = '1';
              frame.style.minWidth = 'auto';
              frame.style.display = 'flex';
              frame.style.flexDirection = 'column';
            });
          }

          console.log(`Door dimensions: ${cfg.width}x${cfg.height} cm, Preview: ${calculatedWidth + 50}x${fixedHeight} px`);
        } else {
          // Säkerställ att fönster-preview har rätt stil
          const windowPreview = tempContainer.querySelector('.preview');
          if (windowPreview) {
            windowPreview.style.width = '100%';
            windowPreview.style.height = '100%';

            // Säkerställ att alla fönsterramar är synliga
            const windowFrames = windowPreview.querySelectorAll('.window-frame');
            windowFrames.forEach(frame => {
              frame.style.height = '100%';
            });
          }

          console.log(`Window dimensions: ${cfg.width}x${cfg.height} cm, Preview: ${calculatedWidth + 50}x${fixedHeight} px`);
        }

        document.body.appendChild(tempContainer);

        // Lägg till PDF-export CSS direkt i DOM
        const pdfStyle = document.createElement('style');
        pdfStyle.textContent = `
          /* Säkerställ att preview-container har rätt stil */
          .preview-container.saved-container {
            background: transparent;
            border: none;
            padding: 0;
            margin: 0;
            box-shadow: none;
          }

          /* Säkerställ att preview har rätt stil */
          .preview.saved-preview {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 100%;
            background: rgba(60, 60, 60, 0.95);
            position: relative;
            border: 10px solid rgba(50, 50, 50, 0.95);
            box-sizing: border-box;
            padding: 0;
            margin: 0;
          }

          /* Säkerställ att window-frame har rätt stil */
          .window-frame {
            flex: 1;
            height: 100%;
            position: relative !important;
            background: rgba(65, 65, 65, 0.95);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
            padding: 0 !important;
            margin: 0 !important;
            border-right: 8px solid rgba(50, 50, 50, 0.95);
            border-top: 8px solid rgba(50, 50, 50, 0.95);
            border-bottom: 8px solid rgba(50, 50, 50, 0.95);
            border-left: 8px solid rgba(50, 50, 50, 0.95);
            box-sizing: border-box !important;
          }

          /* Sista fönsterbågen ska ha höger-balk för konsekvent karm */
          .window-frame:last-child {
            border-right: 8px solid rgba(50, 50, 50, 0.95);
          }

          /* Första fönsterbågen ska inte ha vänster-balk */
          .window-frame:first-child {
            border-left: none;
          }

          /* Säkerställ att pane har rätt stil */
          .pane {
            position: absolute !important;
            top: 8px !important;
            left: 8px !important;
            right: 8px !important;
            bottom: 8px !important;
            width: calc(100% - 16px) !important;
            height: calc(100% - 16px) !important;
            background: rgba(150, 170, 190, 0.1);
            border: 1px solid rgba(100, 100, 100, 0.3);
            box-sizing: border-box !important;
          }

          /* Säkerställ att SVG har rätt stil */
          .frame-svg {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100% !important;
            height: 100% !important;
            pointer-events: none;
            z-index: 3;
          }

          /* Säkerställ att linjer har rätt stil */
          .frame-svg line {
            stroke: rgba(180, 180, 180, 0.7);
            stroke-width: 2;
            vector-effect: non-scaling-stroke;
          }

          /* Säkerställ att spröjs-linjer har rätt stil */
          .frame-svg line.sprojs-line {
            stroke: #333 !important;
            stroke-width: 1 !important;
            vector-effect: non-scaling-stroke !important;
          }

          /* Säkerställ att handtag har rätt stil */
          .handle {
            position: absolute;
            width: 12px;
            height: 30px;
            background: linear-gradient(180deg, #909090 0%, #707070 50%, #909090 100%);
            border-radius: 4px;
            z-index: 5;
          }

          .handle.side-left {
            left: -6px;
            top: 50%;
            transform: translateY(-50%);
          }

          .handle.side-right {
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
          }

          .handle.top-center {
            top: -6px;
            left: 50%;
            transform: translate(-50%, 0) rotate(90deg);
          }

          .handle.bottom-center {
            bottom: -6px;
            left: 50%;
            transform: translate(-50%, 0) rotate(90deg);
          }

          /* Dörr-specifika stilar */
          .door-preview {
            display: flex;
            align-items: stretch;
            justify-content: flex-start; /* Ändrat från center till flex-start för att visa hela dörren */
            height: 100%;
            width: 100%;
            background: rgba(60, 60, 60, 0.95);
            border: 10px solid rgba(50, 50, 50, 0.95);
            box-sizing: border-box;
            margin: 0 auto;
            overflow: visible; /* Säkerställ att innehåll inte klipps */
          }

          .door-frame {
            display: flex;
            flex-direction: column;
            position: relative;
            background: rgba(65, 65, 65, 0.95);
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s ease;
            padding: 0;
            margin: 0;
            border-right: 8px solid rgba(50, 50, 50, 0.95);
            border-top: 8px solid rgba(50, 50, 50, 0.95);
            border-bottom: 8px solid rgba(50, 50, 50, 0.95);
            border-left: 8px solid rgba(50, 50, 50, 0.95);
            min-width: 60px;
            overflow: visible;
            flex: 1 1 auto;
            height: 100%;
            box-sizing: border-box;
            width: auto;
          }

          .door-glass-pane {
            flex: 1;
            background: rgba(150, 170, 190, 0.2);
            border: 2px solid rgba(50, 50, 50, 0.95);
            box-shadow:
              inset 0 0 20px rgba(200, 200, 220, 0.1),
              inset 0 0 40px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            margin: 8px;
          }

          .door-svg-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
          }

          .door-sproj {
            width: 8px;
            background: rgba(50, 50, 50, 0.95);
            z-index: 2;
            flex: 0 0 8px; /* Säkerställ att spröjsen har rätt bredd */
            align-self: stretch; /* Sträck spröjsen över hela höjden */
          }

          /* Förbättrad hantering av handtag för dörrar */
          .door-frame .handle {
            position: absolute;
            width: 15px;
            height: 40px;
            background: linear-gradient(180deg, #a0a0a0 0%, #808080 50%, #a0a0a0 100%);
            border-radius: 3px;
            z-index: 5;
            right: -7px;
            top: 50%;
            transform: translateY(-50%);
          }

          /* Specifik hantering för sidohängda dörrar */
          .door-frame .handle.side-left {
            left: auto;
            right: -7px;
            top: 50%;
            transform: translateY(-50%);
          }

          .door-frame .handle.side-right {
            right: auto;
            left: -7px;
            top: 50%;
            transform: translateY(-50%);
          }
        `;
        document.head.appendChild(pdfStyle);

        let canvas;
        try {
          // Förbättra SVG-element i den klonade förhandsvisningen
          const svgElements = tempContainer.querySelectorAll('svg');
          svgElements.forEach(svg => {
            // Säkerställ att SVG har rätt attribut
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.setAttribute('preserveAspectRatio', 'none');
            svg.setAttribute('vector-effect', 'non-scaling-stroke');

            // Säkerställ att SVG är korrekt positionerad
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.right = '0';
            svg.style.bottom = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';

            // Förbättra linjer i SVG
            const lines = svg.querySelectorAll('line');
            lines.forEach(line => {
              // Säkerställ att alla linjer har vector-effect
              line.setAttribute('vector-effect', 'non-scaling-stroke');

              // Säkerställ att spröjs-linjer har rätt attribut
              if (line.classList.contains('sprojs-line')) {
                line.setAttribute('stroke', '#333');
                line.setAttribute('stroke-width', '1');
              } else {
                // Öppningslinjer ska vara tjockare och ljusare
                line.setAttribute('stroke', 'rgba(180, 180, 180, 0.7)');
                line.setAttribute('stroke-width', '2');
              }
            });
          });

          // Hantera dörr-specifika SVG-element
          const doorSvgContainers = tempContainer.querySelectorAll('.door-svg-container');
          doorSvgContainers.forEach(container => {
            // Säkerställ att container har rätt stil
            container.style.position = 'absolute';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.pointerEvents = 'none';
            container.style.zIndex = '10';

            // Förbättra SVG i dörr-container
            const svg = container.querySelector('svg');
            if (svg) {
              svg.setAttribute('width', '100%');
              svg.setAttribute('height', '100%');
              svg.setAttribute('preserveAspectRatio', 'none');
              svg.setAttribute('viewBox', '0 0 100 100');

              // Förbättra linjer i dörr-SVG
              const lines = svg.querySelectorAll('line');
              lines.forEach(line => {
                line.setAttribute('vector-effect', 'non-scaling-stroke');
                line.setAttribute('stroke', 'rgba(180, 180, 180, 0.7)');
                line.setAttribute('stroke-width', '2');
              });
            }
          });

          // Vänta lite längre för att säkerställa att alla DOM-ändringar har applicerats
          await new Promise(resolve => setTimeout(resolve, 500));

          // Skapa canvas med förbättrade inställningar
          canvas = await html2canvas(tempContainer, {
            scale: 5, // Högre upplösning för skarpare linjer
            useCORS: true,
            backgroundColor: '#fff',
            logging: false,
            allowTaint: true,
            imageTimeout: 0,
            removeContainer: true,
            letterRendering: true,
            onclone: (clonedDoc) => {
              // Förbättra SVG-rendering i den klonade DOM:en
              const svgs = clonedDoc.querySelectorAll('svg');
              svgs.forEach(svg => {
                // Förbättra SVG-rendering
                svg.style.shapeRendering = 'geometricPrecision';

                // Förbättra linjer
                const lines = svg.querySelectorAll('line');
                lines.forEach(line => {
                  // Säkerställ att alla linjer har rätt attribut
                  line.setAttribute('vector-effect', 'non-scaling-stroke');

                  if (line.classList.contains('sprojs-line')) {
                    // Spröjs-linjer ska vara tunnare och mörkare
                    line.setAttribute('stroke', '#333');
                    line.setAttribute('stroke-width', '1');
                    line.style.shapeRendering = 'crispEdges';
                  } else {
                    // Öppningslinjer ska vara tjockare och ljusare
                    line.setAttribute('stroke', 'rgba(180, 180, 180, 0.7)');
                    line.setAttribute('stroke-width', '2');
                  }
                });
              });

              // Hantera dörr-specifika SVG-element i den klonade DOM:en
              const doorSvgContainers = clonedDoc.querySelectorAll('.door-svg-container');
              doorSvgContainers.forEach(container => {
                // Säkerställ att container har rätt stil
                container.style.position = 'absolute';
                container.style.top = '0';
                container.style.left = '0';
                container.style.width = '100%';
                container.style.height = '100%';
                container.style.pointerEvents = 'none';
                container.style.zIndex = '10';

                // Förbättra SVG i dörr-container
                const svg = container.querySelector('svg');
                if (svg) {
                  svg.style.shapeRendering = 'geometricPrecision';
                  svg.setAttribute('width', '100%');
                  svg.setAttribute('height', '100%');
                  svg.setAttribute('preserveAspectRatio', 'none');
                  svg.setAttribute('viewBox', '0 0 100 100');

                  // Förbättra linjer i dörr-SVG
                  const lines = svg.querySelectorAll('line');
                  lines.forEach(line => {
                    line.setAttribute('vector-effect', 'non-scaling-stroke');
                    line.setAttribute('stroke', 'rgba(180, 180, 180, 0.7)');
                    line.setAttribute('stroke-width', '2');
                    line.style.shapeRendering = 'geometricPrecision';
                  });
                }
              });
            }
          });
        } finally {
          // Ta bort temporär container och CSS
          document.body.removeChild(tempContainer);
          document.head.removeChild(pdfStyle);
        }

        const imgData = canvas.toDataURL('image/png');

        // Beräkna dimensioner baserat på proportioner för både dörrar och fönster
        let imgW, imgH;

        // Använd en fast höjd på 70mm i PDF för konsekvent utseende för både dörrar och fönster
        imgH = 70;

        // Använd samma aspectRatio som tidigare beräknats
        imgW = (imgH * aspectRatio) + 5; // Lägg till 5mm extra bredd

        // Begränsa bredden till max IMG_W (standard bildbredd)
        if (imgW > IMG_W) {
          const scale = IMG_W / imgW;
          imgW = IMG_W;
          imgH = imgH * scale; // Justera höjden proportionellt om bredden begränsas
        }

        if (cfg.type === 'door') {
          console.log(`Door PDF dimensions: ${imgW}x${imgH} mm, Aspect ratio: ${aspectRatio}`);
        } else {
          console.log(`Window PDF dimensions: ${imgW}x${imgH} mm, Aspect ratio: ${aspectRatio}`);
        }

        // Använd aktuell Y-position istället för fast position
        const xImg = MARGIN;
        const yImg = pdf.internal.getCurrentPageInfo().pageContext.y || (HEADER_BOTTOM + HEADER_GAP_Y);
        pdf.addImage(imgData, 'PNG', xImg, yImg, imgW, imgH);

        const xSpec = xImg + imgW + COL_GAP;
        let   ySpec = yImg;
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(14);
        pdf.text(cfg.name, xSpec, ySpec);
        ySpec += LINE_H * 1.5;

        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        const specs = [];

        // Kontrollera om det är en dörr eller ett fönster
        const isDoor = cfg.type === 'door';

        // Lägg till rätt antal-etikett baserat på typ
        if (cfg.quantity != null) {
          if (isDoor) {
            specs.push(['Antal dörrar', cfg.quantity]);
          } else {
            specs.push(['Antal fönster', cfg.quantity]);
          }
        }

        // Gemensamma specifikationer för både dörrar och fönster
        specs.push(['Bredd', `${cfg.width} cm`]);
        specs.push(['Höjd', `${cfg.height} cm`]);
        specs.push(['Antal luft', cfg.count]);
        if (cfg.material) specs.push(['Material', CONFIG.labels[cfg.material] || cfg.material]);

        // Olika hantering av glastyp för dörrar och fönster
        if (isDoor) {
          // För dörrar visar vi bara glastyp
          if (cfg.glassType) specs.push(['Glastyp', CONFIG.glassTypeLabels[cfg.glassType] || cfg.glassType]);
        } else {
          // För fönster visar vi både antal glas och glastyp
          if (cfg.glassType) specs.push(['Antal glas', CONFIG.glassTypes[cfg.glassType] || cfg.glassType]);
          if (cfg.glassTypeValue) specs.push(['Glastyp', CONFIG.glassTypeLabels[cfg.glassTypeValue] || cfg.glassTypeValue]);
        }

        if (cfg.insideColor) specs.push(['Färg insida', CONFIG.colorLabels[cfg.insideColor] || cfg.insideColor]);

        // Dörr-specifika specifikationer
        if (isDoor && cfg.panelHeight) {
          specs.push(['Panelhöjd', `${cfg.panelHeight} cm`]);
        }

        // Lägg till spröjs i PDF om det finns (endast för fönster)
        if (!isDoor && cfg.sprojsType && cfg.sprojsType !== 'none') {
          if (cfg.sprojsType === 'custom' && cfg.customSprojsPerLuft && cfg.customSprojsPerLuft.length > 0) {
            const settings = cfg.customSprojsPerLuft[0];
            specs.push(['Spröjs', `Anpassad (${settings.horizontal} horisontella, ${settings.vertical} vertikala)`]);
          } else {
            specs.push(['Spröjs', CONFIG.sprojsLabels[cfg.sprojsType] || cfg.sprojsType]);
          }
        }

        if (cfg.outsideColor && cfg.outsideColor !== cfg.insideColor) specs.push(['Färg utsida', CONFIG.colorLabels[cfg.outsideColor] || cfg.outsideColor]);

        // Lägg till handtag i PDF om det finns (endast för fönster)
        if (!isDoor) {
          if (cfg.handle && cfg.handle !== 'none') {
            const handleLabel = CONFIG.handleLabels[cfg.handle] || cfg.handle;
            specs.push(['Handtag', handleLabel]);
            console.log(`Lade till handtag i PDF: ${handleLabel}`);
          } else {
            // Om inget handtag är valt, använd standardvärdet
            const defaultHandle = 'stormkrok';
            specs.push(['Handtag', CONFIG.handleLabels[defaultHandle] || defaultHandle]);
            console.log(`Inget handtag valt, använder standard: ${CONFIG.handleLabels[defaultHandle]}`);
          }
        }

        // Lägg till myggnät i PDF om det finns (endast för fönster)
        if (!isDoor && cfg.mosquitoNet !== undefined) {
          // Kontrollera om alla fönster är fasta
          const allFixed = cfg.openings && cfg.openings.every(type => type === 'fixed');

          // Visa myggnät-information endast om det inte är ett fast fönster eller om myggnät är valt
          if (!allFixed || cfg.mosquitoNet) {
            const mosquitoNetLabel = CONFIG.mosquitoNetLabels[cfg.mosquitoNet.toString()] || (cfg.mosquitoNet ? 'Med myggnät' : 'Utan myggnät');
            specs.push(['Myggnät', mosquitoNetLabel]);
            console.log(`Lade till myggnät i PDF: ${mosquitoNetLabel}`);
          }
        }

        // Luftinformation hanteras separat nedan
        cfg.openSizes.forEach((sz, idx) => {
          const op = cfg.openings[idx];

          // Hantera olika format för dörrar och fönster
          if (isDoor) {
            // För dörrar, använd doorOpeningLabels
            const openingLabel = CONFIG.doorOpeningLabels[op] || op;
            specs.push([`Luft ${idx+1}`, `${openingLabel} ${sz} cm`]);
          } else {
            // För fönster, använd befintlig logik
            const hing = (op==='side-hung' && cfg.hinges[idx]) ? ` (${cfg.hinges[idx]})` : '';
            let luftInfo = `${op} ${sz} cm${hing}`;

            // Lägg till spröjsinformation för denna luft om det finns
            if (cfg.sprojsType && cfg.sprojsType !== 'none') {
              if (cfg.sprojsType === 'custom' && cfg.customSprojsPerLuft && cfg.customSprojsPerLuft.length > 0) {
                const customSprojs = cfg.customSprojsPerLuft[0];
                luftInfo += `, Spröjs: Anpassad (${customSprojs.horizontal || 0}h, ${customSprojs.vertical || 0}v)`;
              } else {
                luftInfo += `, Spröjs: ${CONFIG.sprojsLabels[cfg.sprojsType] || cfg.sprojsType}`;
              }
            }

            specs.push([`Luft ${idx+1}`, luftInfo]);
          }
        });

        specs.forEach(([lbl, val]) => {
          pdf.text(`${lbl}: ${val}`, xSpec, ySpec);
          ySpec += LINE_H;
        });

        let designPrice = 0;
        if (CONFIG.price.showPerDesign && cfg.price != null) {
          ySpec += LINE_H;
          const priceTxt = `Pris: ${cfg.price} ${CONFIG.price.currency}`;
          pdf.text(priceTxt, xSpec, ySpec);
          designPrice = parseFloat(cfg.price);
        }

        // Beräkna position för separatorlinje baserat på höjden av bild och specifikationer
        const sepY = Math.max(yImg + imgH, ySpec) + 10;
        pdf.setDrawColor(180);
        pdf.setLineWidth(0.3);
        pdf.line(MARGIN, sepY, PAGE_W - MARGIN, sepY);

        // Uppdatera Y-positionen för nästa element i en variabel istället för att använda setY
        // (jsPDF har ingen setY-metod)
        pdf.internal.getCurrentPageInfo().pageContext.y = sepY + 10;

        // Kontrollera om vi närmar oss slutet av sidan och behöver byta sida
        if (pdf.internal.getCurrentPageInfo().pageContext.y > (PAGE_H - 50)) {
          pdf.addPage();
          drawHeaderFooter(currentPage - 1);
          // Återställ Y-positionen till toppen av innehållsområdet på den nya sidan
          pdf.internal.getCurrentPageInfo().pageContext.y = HEADER_BOTTOM + HEADER_GAP_Y;
        }

        return designPrice;
      }

      /* Temporärt kommenterad - kommer att användas senare
      function drawSummary(total) {
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yStart = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(16);
        pdf.text('Prissammanfattning', MARGIN, yStart);

        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        pdf.text(
          `Totalt pris: ${total.toFixed(2)} ${CONFIG.price.currency}`,
          MARGIN,
          yStart + LINE_H * 2
        );
      }

      function drawTerms() {
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yStart = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(12);
        pdf.text('Allmänna villkor', MARGIN, yStart);

        pdf.setFont(CONFIG.page.smallFont.name, CONFIG.page.smallFont.style);
        pdf.setFontSize(CONFIG.page.smallFont.size);
        let y = yStart + LINE_H;
        CONFIG.terms.forEach(term => {
          pdf.text(`• ${term}`, MARGIN + 5, y);
          y += LINE_H * 1.2;
        });
      }

      function drawSignature() {
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yStart = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(12);
        pdf.text('Undertecknande', MARGIN, yStart);

        pdf.setLineWidth(0.3);
        // Kund underskrift
        pdf.line(MARGIN, yStart + 20, PAGE_W / 2 - MARGIN, yStart + 20);
        pdf.text('Kund', MARGIN, yStart + 25);

        // Företags underskrift
        pdf.line(PAGE_W / 2 + MARGIN, yStart + 20, PAGE_W - MARGIN, yStart + 20);
        pdf.text('Ägare/Projektledare', PAGE_W / 2 + MARGIN, yStart + 25);
      }
      */

      // --- 10) Generera PDF i ordning ---
      // Uppdatera progress bar - 10% för att visa att vi har börjat
      updateProgress(10);

      drawCover();

      // Uppdatera progress bar - 20% efter framsidan
      updateProgress(20);

      // Sortera designs så att fönster kommer först och dörrar sist
      const windowDesigns = designs.filter(design => !design.type || design.type === 'window');
      const doorDesigns = designs.filter(design => design.type === 'door');

      console.log(`Sorterade designs: ${windowDesigns.length} fönster och ${doorDesigns.length} dörrar`);

      // Beräkna hur mycket progress varje design ska få
      const designProgressStep = 50 / designs.length; // 50% av progress bar för designs

      let totalSum = 0;
      let designIndex = 0;

      // Om det finns både fönster och dörrar, lägg till en rubrik "Fönster:"
      if (windowDesigns.length > 0 && doorDesigns.length > 0) {
        // Lägg till en sida med rubriken "Fönster"
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yTitle = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, 'bold');
        pdf.setFontSize(18);
        pdf.text('FÖNSTER', MARGIN, yTitle);

        // Lägg till en beskrivande text
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(12);
        pdf.text('Följande fönster ingår i offerten:', MARGIN, yTitle + 10);

        // Lägg till en linje under rubriken
        pdf.setDrawColor(180);
        pdf.setLineWidth(0.3);
        pdf.line(MARGIN, yTitle + 15, PAGE_W - MARGIN, yTitle + 15);

        // Spara Y-positionen för nästa element i en variabel istället för att använda setY
        // (jsPDF har ingen setY-metod)
        pdf.internal.getCurrentPageInfo().pageContext.y = yTitle + 25;

        // Lägg till denna sida i innehållsförteckningen
        tocEntries.push({ title: 'Fönster', page: currentPage - 1 });
      }

      // Rendera alla fönster
      for (let i = 0; i < windowDesigns.length; i++) {
        // Om det finns både fönster och dörrar, markera att detta är i fönster-sektionen
        const isInWindowSection = (doorDesigns.length > 0);
        totalSum += await renderDesign(windowDesigns[i], designIndex, isInWindowSection);
        designIndex++;

        // Uppdatera progress bar för varje design
        updateProgress(20 + (designIndex) * designProgressStep);
      }

      // Om det finns dörrar, lägg till en rubrik "Dörrar:"
      if (doorDesigns.length > 0) {
        // Lägg till en sida med rubriken "Dörrar"
        pdf.addPage();
        drawHeaderFooter(currentPage - 1);

        const yTitle = HEADER_BOTTOM + HEADER_GAP_Y;
        pdf.setFont(CONFIG.page.normalFont.name, 'bold');
        pdf.setFontSize(18);
        pdf.text('DÖRRAR', MARGIN, yTitle);

        // Lägg till en beskrivande text
        pdf.setFont(CONFIG.page.normalFont.name, CONFIG.page.normalFont.style);
        pdf.setFontSize(12);
        pdf.text('Följande dörrar ingår i offerten:', MARGIN, yTitle + 10);

        // Lägg till en linje under rubriken
        pdf.setDrawColor(180);
        pdf.setLineWidth(0.3);
        pdf.line(MARGIN, yTitle + 15, PAGE_W - MARGIN, yTitle + 15);

        // Spara Y-positionen för nästa element i en variabel istället för att använda setY
        // (jsPDF har ingen setY-metod)
        pdf.internal.getCurrentPageInfo().pageContext.y = yTitle + 25;

        // Lägg till denna sida i innehållsförteckningen
        tocEntries.push({ title: 'Dörrar', page: currentPage - 1 });
      }

      // Rendera sedan alla dörrar
      for (let i = 0; i < doorDesigns.length; i++) {
        totalSum += await renderDesign(doorDesigns[i], designIndex, true);
        designIndex++;

        // Uppdatera progress bar för varje design
        updateProgress(20 + (designIndex) * designProgressStep);
      }

      // Uppdatera progress bar - 70% efter alla designs
      updateProgress(70);

      // Kommenterat ut innehållsförteckning tills vidare
      // drawToc();
      //
      // // Uppdatera progress bar - 80% efter innehållsförteckning
      // updateProgress(80);

      // Hoppa direkt till 80% eftersom vi hoppat över innehållsförteckningen
      updateProgress(80);

      // Kommenterat ut tills vidare - kommer att utvecklas senare
      // drawSummary(totalSum);
      //
      // // Uppdatera progress bar - 85% efter sammanfattning
      // updateProgress(85);
      //
      // drawTerms();
      //
      // // Uppdatera progress bar - 90% efter villkor
      // updateProgress(90);
      //
      // drawSignature();
      //
      // // Uppdatera progress bar - 90% efter signatur
      // updateProgress(90);

      // Hoppa direkt till 90% eftersom vi hoppat över flera steg
      updateProgress(90);

      // --- 11) Uppdatera sidnumreringen med det faktiska antalet sidor ---
      console.log(`Uppdaterar sidnumrering för ${actualTotalPages} sidor`);

      // Gå igenom alla sidor och uppdatera sidnumreringen
      for (let i = 1; i <= actualTotalPages; i++) {
        pdf.setPage(i);

        // Rita sidfoten med korrekt sidnumrering
        pdf.setFont(CONFIG.page.footerFont.name, CONFIG.page.footerFont.style);
        pdf.setFontSize(CONFIG.page.footerFont.size);
        pdf.text(`Sida ${i} av ${actualTotalPages}`, PAGE_W / 2, PAGE_H - 10, { align: 'center' });
      }

      // Uppdatera progress bar - 95% efter sidnumrering
      updateProgress(95);

      // --- 12) Spara PDF ---
      pdf.save('Offert_Isofonster.pdf');

      // Uppdatera progress bar - 100% när PDF är sparad
      updateProgress(100);

      // Dölj progress bar efter en kort stund
      hideProgressBar();

    } catch (err) {
      console.error('Fel vid PDF-export:', err);
      alert('Något gick fel vid generering av PDF. Se console för detaljer.');

      // Dölj progress bar vid fel
      hideProgressBar();
    }
  });
});
