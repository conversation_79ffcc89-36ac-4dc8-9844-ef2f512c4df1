<!DOCTYPE html>
<html lang="sv">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Fönster Konfigurator</title>
  <link rel="stylesheet" href="css/base.css">
  <link rel="stylesheet" href="css/theme.css">
  <!-- Sektioner -->
  <link rel="stylesheet" href="css/sections/new-design.css">
  <link rel="stylesheet" href="css/sections/saved-designs.css"> <!-- eller media="all" -->
  <link rel="stylesheet" href="css/sections/export.css">
  <link rel="stylesheet" href="css/sections/steg4.css">
  <link rel="stylesheet" href="css/sections/steg5.css">
  <link rel="stylesheet" href="css/sections/steg6.css">
  <link rel="stylesheet" href="css/sections/steg7.css">
  <link rel="stylesheet" href="css/sections/steg8.css">
  <link rel="stylesheet" href="css/sections/steg9.css">
  <link rel="stylesheet" href="css/sections/steg10.css">
  <link rel="stylesheet" href="css/step-navigation.css">
  <link rel="stylesheet" href="css/preview-enhanced.css">
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="css/pdf.css">
  <link rel="stylesheet" href="css/sections/configurator-selection.css">
  <link rel="stylesheet" href="css/sections/door-configurator.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <!-- Three.js bibliotek för 3D-preview -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
  <style>
    /* Små inline-stilar för navbar */
    .hidden { display: none; }
    .top-nav { display: flex; gap: 1rem; background: var(--color-panel); padding: 1rem; }
    .top-nav button {
      background: none;
      border: none;
      color: var(--color-secondary);
      padding: 0.5rem 1rem;
      cursor: pointer;
      border-radius: var(--radius);
      transition: background var(--transition);
    }
    .top-nav button:hover { background: rgba(245,199,0,0.1); }
    .top-nav button.active {
      background: var(--color-primary);
      color: #121212;
    }
  </style>
</head>
<body>
  <header>
    <h1>Fönster Konfigurator</h1>
  </header>


  <!-- NAVBAR -->
  <nav class="top-nav">
    <button id="navNewDesign"   data-target="sectionNewDesign" class="active">Ny Design</button>
    <button id="navSavedDesigns" data-target="sectionSavedDesigns">Mina sparade designs</button>
    <button id="navExport"       data-target="sectionExport">Exportera</button>
  </nav>

  <!-- SEKTION: Ny design (configurator) -->
  <section id="sectionNewDesign">
    <!-- Konfigurator-val -->
    <div id="configuratorSelection" class="configurator-selection">
      <h2>Välj konfigurator</h2>
      <div class="configurator-options">
        <div id="windowConfiguratorOption" class="configurator-option">
          <div class="icon">🪟</div>
          <h3>Fönster Konfigurator</h3>
          <p>Designa ditt eget fönster med vårt enkla verktyg</p>
          <button id="startWindowConfigurator" class="btn">Designa ett fönster</button>
        </div>
        <div id="doorConfiguratorOption" class="configurator-option">
          <div class="icon">🚪</div>
          <h3>Dörr Konfigurator</h3>
          <p>Designa din egen dörr med vårt enkla verktyg</p>
          <button id="startDoorConfigurator" class="btn">Designa en dörr</button>
        </div>
      </div>
    </div>

    <!-- Fönsterkonfigurator -->
    <div id="windowConfigurator" class="window-configurator hidden">
      <div class="step-nav-wrapper">
        <h2 class="nav-title">Steg i Fönsterkonfiguratorn</h2>
        <nav class="step-nav">
          <ul>
            <li class="step active" data-step="0">1</li>
            <li class="step"       data-step="1">2</li>
            <li class="step"       data-step="2">3</li>
            <li class="step"       data-step="3">4</li>
            <li class="step"       data-step="4">5</li>
            <li class="step"       data-step="5">6</li>
            <li class="step"       data-step="6">7</li>
            <li class="step"       data-step="7">8</li>
            <li class="step"       data-step="8">9</li>
            <li class="step"       data-step="9">10</li>
            <li class="step"       data-step="10">11</li>
          </ul>
        </nav>
      </div>

    <main class="builder-container">
      <aside class="controls-column">
        <!-- STEG 1: Karmmått -->
        <section id="section-0" class="builder-section expanded">
          <h2>1. Karmmått</h2>
          <div class="section-body dimension-section">
            <div class="dim-row">
              <div class="dim-label">
                <span>Bredd</span>
                <span class="dim-value" id="widthLabel">180 cm</span>
              </div>
              <input type="number" id="widthInput" class="dim-input" min="50" max="180" value="180">
              <div class="dim-hint">Min: 50 cm / Max: 180 cm</div>
            </div>
            <div class="dim-row">
              <div class="dim-label">
                <span>Höjd</span>
                <span class="dim-value" id="heightLabel">150 cm</span>
              </div>
              <input type="number" id="heightInput" class="dim-input" min="60" max="180" value="150">
              <div class="dim-hint">Min: 60 cm / Max: 180 cm</div>
            </div>
            <div class="builder-actions">
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 2: Antal luft -->
        <section id="section-1" class="builder-section collapsed">
          <h2>2. Antal luft</h2>
          <div class="section-body">
            <div class="quantity-group">
              <button class="qty-option-btn active" data-value="1">1 luft</button>
              <button class="qty-option-btn" data-value="2">2 luft</button>
              <button class="qty-option-btn" data-value="3">3 luft</button>
              <button class="qty-option-btn" data-value="custom">Anpassad</button>
            </div>
            <div class="custom-control hidden">
              <button id="decrement" class="qty-btn">−</button>
              <input id="customCount" class="custom-input" type="number" min="1" max="10" value="1">
              <button id="increment" class="qty-btn">+</button>
            </div>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 3: Öppning per luft -->
        <section id="section-2" class="builder-section collapsed">
          <h2>3. Öppning per luft</h2>
          <div class="section-body">
            <div id="openingsContainer"></div>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 4: Material -->
        <section id="section-3" class="builder-section collapsed">
          <h2>4. Material</h2>
          <div class="section-body">
            <div class="material-group">
              <button class="material-option-btn" data-value="tra">Trä</button>
              <button class="material-option-btn" data-value="pvc">PVC</button>
              <button class="material-option-btn" data-value="aluminium">Aluminium</button>
              <button class="material-option-btn" data-value="tra-aluminium">Trä/Aluminium</button>
              <button class="material-option-btn" data-value="pvc-aluminium">PVC/Aluminium</button>
            </div>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>
        <section id="section-4" class="builder-section collapsed">
          <h2>5. Antal glas</h2>
          <div class="section-body">
            <p>Välj antal glas för ditt fönster. Olika antal glas ger olika isoleringsförmåga och ljudreduktion.</p>
            <div class="glass-group">
              <button class="glass-option-btn" data-value="double">
                2-glas
                <span class="option-description">Standardalternativ med bra isolering och ljudreduktion.</span>
              </button>
              <button class="glass-option-btn" data-value="triple">
                3-glas
                <span class="option-description">Förbättrad isolering och energieffektivitet, perfekt för kallare klimat.</span>
              </button>
            </div>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>
        <section id="section-5" class="builder-section collapsed">
          <h2>6. Glastyp</h2>
          <div class="section-body">
            <p>Välj vilken typ av glas du vill ha i ditt fönster.</p>
            <div class="glass-type-grid">
              <!-- Klart glas -->
              <div class="glass-type-option" data-value="clear">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Vanlig-glas.png" alt="Klart glas" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Klart glas</h3>
                    <p class="glass-type-description">Det vanligaste glaset till flest ändamål</p>
                  </div>
                </div>

              </div>

              <!-- Ljudreducerande glas -->
              <div class="glass-type-option" data-value="sound">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Ljusreducerande-glas.png" alt="Ljudreducerande glas" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Ljudreducerande glas</h3>
                    <p class="glass-type-description">Ljudreducerande glas hjälper genom att skapa en tyst inomhusmiljö. En tyst inomhusmiljö ger större bekvämlighet och trivsel.</p>
                  </div>
                </div>

              </div>

              <!-- Solskyddsglas -->
              <div class="glass-type-option" data-value="sun">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Vanlig-glas.png" alt="Solskyddsglas" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Solskyddsglas</h3>
                    <p class="glass-type-description">Solskyddsglas blockerar 63% av solens värme och har 10-12% mindre ljusinsläpp</p>
                  </div>
                </div>

              </div>

              <!-- Granit -->
              <div class="glass-type-option" data-value="granite">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Vanlig-glas.png" alt="Granit" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Granit</h3>
                    <p class="glass-type-description">Granit är det mest använda rågluset och kännetecknas av dess utseendemässiga grova och ojämna yta.</p>
                  </div>
                </div>

              </div>

              <!-- Cotswold -->
              <div class="glass-type-option" data-value="cotswold">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Vanlig-glas.png" alt="Cotswold" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Cotswold</h3>
                    <p class="glass-type-description">Cotswold är ett råglas som kännetecknas av de vertikala linjerna och den ojämna ytan.</p>
                  </div>
                </div>

              </div>

              <!-- Frostat glas -->
              <div class="glass-type-option" data-value="frosted">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Frostat glas.jpg" alt="Frostat glas" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Frostat glas</h3>
                    <p class="glass-type-description">Frostat glas är den mer moderna typen av råglas, med en mer mjuk och silkeslik yta.</p>
                  </div>
                </div>

              </div>

              <!-- Härdat glas -->
              <div class="glass-type-option" data-value="tempered">
                <div class="glass-type-header">
                  <img src="assets/glastyper/Härdat-glas.jpg" alt="Härdat glas" class="glass-type-img">
                  <div class="glass-type-content">
                    <h3 class="glass-type-title">Härdat glas</h3>
                    <p class="glass-type-description">Våra härdade glas är 4 gånger starkare än normalt glas</p>
                  </div>
                </div>

              </div>
            </div>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 7: Färg -->
        <section id="section-6" class="builder-section collapsed">
          <h2>7. Färg</h2>
          <div class="section-body">
            <p>Välj färg för ditt fönster.</p>

            <!-- Insida färg -->
            <div class="color-section">
              <h3>Insida</h3>
              <div class="color-options">
                <div class="color-option" data-value="white" data-side="inside">
                  <div class="color-swatch" style="background-color: #ffffff; border: 1px solid #cccccc;"></div>
                  <div class="color-label">Vit</div>
                </div>
                <div class="color-option" data-value="gray" data-side="inside">
                  <div class="color-swatch" style="background-color: #808080;"></div>
                  <div class="color-label">Grå</div>
                </div>
                <div class="color-option" data-value="black" data-side="inside">
                  <div class="color-swatch" style="background-color: #222222;"></div>
                  <div class="color-label">Svart</div>
                </div>
                <div class="color-option" data-value="anthracite" data-side="inside">
                  <div class="color-swatch" style="background-color: #383c3f;"></div>
                  <div class="color-label">Antracitgrå</div>
                </div>
                <div class="color-option" data-value="darkgray" data-side="inside">
                  <div class="color-swatch" style="background-color: #505050;"></div>
                  <div class="color-label">Mörkgrå</div>
                </div>
                <div class="color-option" data-value="silver" data-side="inside">
                  <div class="color-swatch" style="background-color: #c0c0c0;"></div>
                  <div class="color-label">Silver</div>
                </div>
                <div class="color-option" data-value="beige" data-side="inside">
                  <div class="color-swatch" style="background-color: #f5f5dc;"></div>
                  <div class="color-label">Beige</div>
                </div>
              </div>
            </div>

            <!-- Utsida färg (visas endast för alu-beklädda fönster) -->
            <div id="outsideColorSection" class="color-section hidden">
              <h3>Utsida</h3>
              <div class="color-options">
                <div class="color-option" data-value="white" data-side="outside">
                  <div class="color-swatch" style="background-color: #ffffff; border: 1px solid #cccccc;"></div>
                  <div class="color-label">Vit</div>
                </div>
                <div class="color-option" data-value="gray" data-side="outside">
                  <div class="color-swatch" style="background-color: #808080;"></div>
                  <div class="color-label">Grå</div>
                </div>
                <div class="color-option" data-value="black" data-side="outside">
                  <div class="color-swatch" style="background-color: #222222;"></div>
                  <div class="color-label">Svart</div>
                </div>
                <div class="color-option" data-value="anthracite" data-side="outside">
                  <div class="color-swatch" style="background-color: #383c3f;"></div>
                  <div class="color-label">Antracitgrå</div>
                </div>
                <div class="color-option" data-value="darkgray" data-side="outside">
                  <div class="color-swatch" style="background-color: #505050;"></div>
                  <div class="color-label">Mörkgrå</div>
                </div>
                <div class="color-option" data-value="silver" data-side="outside">
                  <div class="color-swatch" style="background-color: #c0c0c0;"></div>
                  <div class="color-label">Silver</div>
                </div>
                <div class="color-option" data-value="beige" data-side="outside">
                  <div class="color-swatch" style="background-color: #f5f5dc;"></div>
                  <div class="color-label">Beige</div>
                </div>
              </div>
            </div>

            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 8: Spröjs -->
        <section id="section-7" class="builder-section collapsed">
          <h2>8. Spröjs</h2>
          <div class="section-body">
            <p>Välj spröjs för ditt fönster.</p>

            <!-- Globala spröjs-inställningar -->
            <div class="sprojs-section">
              <h3>Spröjs för alla lufter</h3>
              <div class="sprojs-options">
                <div class="sprojs-option active" data-value="none">
                  <div class="sprojs-preview none"></div>
                  <div class="sprojs-label">Inga spröjs</div>
                </div>
                <div class="sprojs-option" data-value="one-horizontal">
                  <div class="sprojs-preview one-horizontal"></div>
                  <div class="sprojs-label">Horisontell</div>
                </div>
                <div class="sprojs-option" data-value="one-vertical">
                  <div class="sprojs-preview one-vertical"></div>
                  <div class="sprojs-label">Vertikal</div>
                </div>
                <div class="sprojs-option" data-value="cross">
                  <div class="sprojs-preview cross"></div>
                  <div class="sprojs-label">Kors</div>
                </div>
                <div class="sprojs-option" data-value="grid">
                  <div class="sprojs-preview grid">
                    <div class="h-line"></div>
                    <div class="v-line"></div>
                  </div>
                  <div class="sprojs-label">Rutnät</div>
                </div>
                <div class="sprojs-option" data-value="custom">
                  <div class="sprojs-preview">
                    <div class="sprojs-h-line" style="top: 33%"></div>
                    <div class="sprojs-h-line" style="top: 66%"></div>
                    <div class="sprojs-v-line" style="left: 33%"></div>
                    <div class="sprojs-v-line" style="left: 66%"></div>
                  </div>
                  <div class="sprojs-label">Anpassad</div>
                </div>
              </div>

              <!-- Anpassade spröjs-inställningar -->
              <div id="customSprojsSettings" class="custom-sprojs-settings" style="display: none;">
                <h4>Anpassa antal spröjs</h4>
                <div class="sprojs-count-controls">
                  <div class="sprojs-count-group">
                    <label for="horizontalSprojsCount">Horisontella spröjs:</label>
                    <div class="sprojs-count-input">
                      <button class="decrease-btn" data-target="horizontalSprojsCount">-</button>
                      <input type="number" id="horizontalSprojsCount" min="0" max="4" value="2">
                      <button class="increase-btn" data-target="horizontalSprojsCount">+</button>
                    </div>
                  </div>
                  <div class="sprojs-count-group">
                    <label for="verticalSprojsCount">Vertikala spröjs:</label>
                    <div class="sprojs-count-input">
                      <button class="decrease-btn" data-target="verticalSprojsCount">-</button>
                      <input type="number" id="verticalSprojsCount" min="0" max="4" value="2">
                      <button class="increase-btn" data-target="verticalSprojsCount">+</button>
                    </div>
                  </div>
                </div>
                <div class="sprojs-preview-container" id="customSprojsPreview">
                  <!-- Dynamiska spröjs-linjer genereras här -->
                </div>
              </div>
            </div>

            <!-- Luftspecifika spröjs-inställningar -->
            <div id="luftSprojsContainer" class="luft-sprojs-container">
              <!-- Genereras dynamiskt baserat på antal lufter -->
            </div>

            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 9: Handtag -->
        <section id="section-8" class="builder-section collapsed">
          <h2>9. Handtag</h2>
          <div class="section-body">
            <p>Välj handtag för ditt fönster.</p>

            <div class="handle-section">
              <div class="handle-options">
                <div class="handle-option" data-value="stormkrok" onclick="window.handleHandleClick(this, 'stormkrok')">
                  <img src="assets/handtagtyper/stromkrok.png" alt="Handtag med stormkrok" class="handle-image">
                  <div class="handle-label">Handtag med stormkrok</div>
                </div>
                <div class="handle-option" data-value="barnlas-stormkrok" onclick="window.handleHandleClick(this, 'barnlas-stormkrok')">
                  <img src="assets/handtagtyper/stromkrok-barnlos.png" alt="Handtag med barnlås och stormkrok" class="handle-image">
                  <div class="handle-label">Handtag med barnlås och stormkrok</div>
                </div>
                <div class="handle-option" data-value="krom" onclick="window.handleHandleClick(this, 'krom')">
                  <img src="assets/handtagtyper/kromhandtag.png" alt="Kromhandtag" class="handle-image">
                  <div class="handle-label">Kromhandtag</div>
                </div>
                <div class="handle-option" data-value="vitt" onclick="window.handleHandleClick(this, 'vitt')">
                  <img src="assets/handtagtyper/vitthandtag.png" alt="Vitt handtag" class="handle-image">
                  <div class="handle-label">Vitt handtag</div>
                </div>
                <div class="handle-option" data-value="vitt-las" onclick="window.handleHandleClick(this, 'vitt-las')">
                  <img src="assets/handtagtyper/Vitthandtagmedlas.png" alt="Vitt handtag med lås" class="handle-image">
                  <div class="handle-label">Vitt handtag med lås</div>
                </div>
                <div class="handle-option" data-value="krom-las-nyckel" onclick="window.handleHandleClick(this, 'krom-las-nyckel')">
                  <img src="assets/handtagtyper/Kromhandtagmedlas.png" alt="Kromhandtag med lås + nyckel" class="handle-image">
                  <div class="handle-label">Kromhandtag med lås + nyckel (barnsäker)</div>
                </div>
              </div>
            </div>

            <script>
              // Definiera handtagsetiketter direkt i HTML-filen för att säkerställa att de är tillgängliga omedelbart
              window.HANDLE_LABELS = {
                'none': 'Inget handtag',
                'stormkrok': 'Handtag med stormkrok',
                'barnlas-stormkrok': 'Handtag med barnlås och stormkrok',
                'krom': 'Kromhandtag',
                'vitt': 'Vitt handtag',
                'vitt-las': 'Vitt handtag med lås',
                'krom-las-nyckel': 'Kromhandtag med lås + nyckel (barnsäker)'
              };

              // Initiera handtagshantering när sidan laddas
              document.addEventListener('DOMContentLoaded', function() {
                // Sätt standardhandtag om inget är valt, men visa det inte i specs
                if (!window.selectedHandle) {
                  window.selectedHandle = null; // Inget handtag valt från början
                  console.log('Inget handtag valt från början');
                }

                // Använd setupHandleOptions från steg9.js för att konfigurera handtagsalternativ
                if (typeof window.setupHandleOptions === 'function') {
                  window.setupHandleOptions();
                } else {
                  console.error('setupHandleOptions funktion saknas');
                }

                // Lyssna på ändringar i fönstertyp
                document.addEventListener('openingTypeChanged', function(event) {
                  if (typeof window.setupHandleOptions === 'function') {
                    window.setupHandleOptions();
                  }
                });
              });
            </script>

            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 10: Myggnät -->
        <section id="section-9" class="builder-section collapsed">
          <h2>10. Myggnät</h2>
          <div class="section-body">
            <p>Välj om du vill ha myggnät till ditt fönster.</p>

            <div class="mosquito-net-section">
              <div class="mosquito-net-options">
                <button id="noMosquitoNet" class="mosquito-net-option-btn active">Utan myggnät</button>
                <button id="withMosquitoNet" class="mosquito-net-option-btn">Med myggnät</button>
              </div>

              <div class="mosquito-net-info">
                <i class="info-icon">ⓘ</i>
                <span>Myggnät kommer finnas på det fönster som har en öppning och inte på fasta fönster.</span>
              </div>
            </div>

            <script>
              // Direkt JavaScript för att hantera myggnätsval
              document.addEventListener('DOMContentLoaded', function() {
                const noMosquitoNetBtn = document.getElementById('noMosquitoNet');
                const withMosquitoNetBtn = document.getElementById('withMosquitoNet');

                if (noMosquitoNetBtn && withMosquitoNetBtn) {
                  // Utan myggnät
                  noMosquitoNetBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log("Klick på 'Utan myggnät'");

                    noMosquitoNetBtn.classList.add('active');
                    withMosquitoNetBtn.classList.remove('active');
                    window.selectedMosquitoNet = false;

                    if (typeof window.updateMosquitoNetSpec === 'function') {
                      window.updateMosquitoNetSpec();
                    }
                  });

                  // Med myggnät
                  withMosquitoNetBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log("Klick på 'Med myggnät'", this.disabled);

                    // Kontrollera om knappen är inaktiverad
                    if (this.disabled) {
                      console.log("Knappen är inaktiverad, ignorerar klick");
                      return;
                    }

                    withMosquitoNetBtn.classList.add('active');
                    noMosquitoNetBtn.classList.remove('active');
                    window.selectedMosquitoNet = true;

                    if (typeof window.updateMosquitoNetSpec === 'function') {
                      window.updateMosquitoNetSpec();
                    }
                  });
                }
              });
            </script>

            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn next-btn">Nästa</button>
            </div>
          </div>
        </section>

        <!-- STEG 11: Sammanfattning -->
        <section id="section-10" class="builder-section collapsed">
          <h2>11. Sammanfattning</h2>
          <div class="section-body">
            <p>Här kan du se en summering av din design innan du sparar.</p>
            <div class="builder-actions">
              <button class="btn prev-btn">Tillbaka</button>
              <button class="btn save-btn">Spara</button>
            </div>
          </div>
        </section>
      </aside>

      <!-- Förhandsvisning & Specifikation -->
      <section class="result-column">
        <div class="preview-container">
          <div id="preview" class="preview"></div>
          <div id="preview3d" class="preview3d hidden"></div>
          <button id="toggle3dPreview" class="preview-3d-btn" title="Växla 3D-vy">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 3l8 4.5v9L12 21l-8-4.5v-9L12 3z"/>
              <path d="M12 12l8-4.5M12 12v9M12 12L4 7.5"/>
            </svg>
            <span>3D-vy</span>
          </button>
          <!-- Kontrollknappar borttagna enligt önskemål -->
          <div id="viewLabel" class="view-label hidden">Insida</div>
        </div>
        <div class="spec-container">
          <h3>Specifikation</h3>
          <table id="specTable">
            <thead>
              <tr><th>Egenskap</th><th>Värde</th></tr>
            </thead>
            <tbody></tbody>
          </table>
        </div>
      </section>
    </main>
      </div> <!-- End of windowConfigurator -->

      <!-- Dörrkonfigurator -->
      <div id="doorConfigurator" class="door-configurator hidden">
        <div class="step-nav-wrapper">
          <h2 class="nav-title">Steg i Dörrkonfiguratorn</h2>
          <nav class="step-nav">
            <ul>
              <li class="step active" data-step="0">1</li>
              <li class="step"       data-step="1">2</li>
              <li class="step"       data-step="2">3</li>
              <li class="step"       data-step="3">4</li>
              <li class="step"       data-step="4">5</li>
              <li class="step"       data-step="5">6</li>
              <li class="step"       data-step="6">7</li>
            </ul>
          </nav>
        </div>

        <main class="builder-container">
          <aside class="controls-column">
            <!-- STEG 1: Karmmått -->
            <section id="door-section-0" class="builder-section expanded">
              <h2>1. Karmmått</h2>
              <div class="section-body dimension-section">
                <div class="dim-row">
                  <div class="dim-label">
                    <span>Bredd</span>
                    <span class="dim-value" id="doorWidthLabel">90 cm</span>
                  </div>
                  <input type="number" id="doorWidthInput" class="dim-input" min="60" max="450" value="90">
                  <div class="dim-hint">Min: 60 cm / Max: 450 cm</div>
                </div>
                <div class="dim-row">
                  <div class="dim-label">
                    <span>Höjd</span>
                    <span class="dim-value" id="doorHeightLabel">210 cm</span>
                  </div>
                  <input type="number" id="doorHeightInput" class="dim-input" min="180" max="240" value="210">
                  <div class="dim-hint">Min: 180 cm / Max: 240 cm</div>
                </div>
                <div class="builder-actions">
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 2: Antal luft -->
            <section id="door-section-1" class="builder-section collapsed">
              <h2>2. Antal luft</h2>
              <div class="section-body">
                <div class="quantity-group">
                  <button class="door-qty-option-btn active" data-value="1">1 luft</button>
                  <button class="door-qty-option-btn" data-value="2">2 luft</button>
                  <button class="door-qty-option-btn" data-value="custom">Anpassad</button>
                </div>
                <div class="custom-control hidden">
                  <button id="doorDecrement" class="qty-btn">−</button>
                  <input id="doorCustomCount" class="custom-input" type="number" min="1" max="3" value="1">
                  <button id="doorIncrement" class="qty-btn">+</button>
                </div>
                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 3: Öppning -->
            <section id="door-section-2" class="builder-section collapsed">
              <h2>3. Öppning</h2>
              <div class="section-body">
                <div id="doorOpeningsContainer"></div>
                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 4: Panel -->
            <section id="door-section-3" class="builder-section collapsed">
              <h2>4. Panel</h2>
              <div class="section-body">
                <p>Välj höjd på panelen i botten av dörren.</p>
                <div class="panel-control">
                  <div class="panel-label">
                    <span>Panelhöjd</span>
                    <span class="panel-value" id="panelHeightLabel">40 cm</span>
                  </div>
                  <input type="range" id="panelHeightSlider" min="0" max="100" value="40" step="5" class="panel-slider">
                  <input type="number" id="panelHeightInput" class="panel-input" min="0" max="100" value="40">
                  <span class="unit">cm</span>
                </div>
                <div class="panel-info">
                  <p>Panelen täcker hela dörrens bredd och placeras i botten av dörren.</p>
                  <p>Sätt höjden till 0 cm om du inte vill ha någon panel.</p>
                </div>
                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 5: Glastyp -->
            <section id="door-section-4" class="builder-section collapsed">
              <h2>5. Glastyp</h2>
              <div class="section-body">
                <p>Välj vilken typ av glas du vill ha i din dörr.</p>
                <div class="door-glass-options">
                  <div class="door-glass-option" data-value="normal">
                    <div class="door-glass-header">
                      <div class="door-glass-content">
                        <h3 class="door-glass-title">Vanligt glas</h3>
                        <p class="door-glass-description">Standardglas för dörrar</p>
                      </div>
                    </div>
                  </div>
                  <div class="door-glass-option" data-value="tempered">
                    <div class="door-glass-header">
                      <div class="door-glass-content">
                        <h3 class="door-glass-title">Härdat glas</h3>
                        <p class="door-glass-description">Säkerhetsglas som är 4-5 gånger starkare än vanligt glas</p>
                      </div>
                    </div>
                  </div>
                  <div class="door-glass-option" data-value="laminated">
                    <div class="door-glass-header">
                      <div class="door-glass-content">
                        <h3 class="door-glass-title">Laminerat glas</h3>
                        <p class="door-glass-description">Säkerhetsglas som håller ihop vid krossning</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 6: Material -->
            <section id="door-section-5" class="builder-section collapsed">
              <h2>6. Material</h2>
              <div class="section-body">
                <div class="material-group">
                  <button class="door-material-option-btn" data-value="tra">Trä</button>
                  <button class="door-material-option-btn" data-value="pvc">PVC</button>
                  <button class="door-material-option-btn" data-value="aluminium">Aluminium</button>
                  <button class="door-material-option-btn" data-value="tra-aluminium">Trä/Aluminium</button>
                  <button class="door-material-option-btn" data-value="pvc-aluminium">PVC/Aluminium</button>
                </div>
                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn next-btn">Nästa</button>
                </div>
              </div>
            </section>

            <!-- STEG 7: Färg -->
            <section id="door-section-6" class="builder-section collapsed">
              <h2>7. Färg</h2>
              <div class="section-body">
                <p>Välj färg för din dörr.</p>

                <!-- Insida färg -->
                <div class="color-section">
                  <h3>Insida</h3>
                  <div class="door-color-options">
                    <div class="door-color-option" data-value="white" data-side="inside">
                      <div class="color-swatch" style="background-color: #ffffff; border: 1px solid #cccccc;"></div>
                      <div class="color-label">Vit</div>
                    </div>
                    <div class="door-color-option" data-value="gray" data-side="inside">
                      <div class="color-swatch" style="background-color: #808080;"></div>
                      <div class="color-label">Grå</div>
                    </div>
                    <div class="door-color-option" data-value="black" data-side="inside">
                      <div class="color-swatch" style="background-color: #222222;"></div>
                      <div class="color-label">Svart</div>
                    </div>
                    <div class="door-color-option" data-value="anthracite" data-side="inside">
                      <div class="color-swatch" style="background-color: #383c3f;"></div>
                      <div class="color-label">Antracitgrå</div>
                    </div>
                    <div class="door-color-option" data-value="darkgray" data-side="inside">
                      <div class="color-swatch" style="background-color: #505050;"></div>
                      <div class="color-label">Mörkgrå</div>
                    </div>
                    <div class="door-color-option" data-value="brown" data-side="inside">
                      <div class="color-swatch" style="background-color: #654321;"></div>
                      <div class="color-label">Brun</div>
                    </div>
                  </div>
                </div>

                <!-- Utsida färg (visas endast för alu-beklädda dörrar) -->
                <div id="doorOutsideColorSection" class="color-section hidden">
                  <h3>Utsida</h3>
                  <div class="door-color-options">
                    <div class="door-color-option" data-value="white" data-side="outside">
                      <div class="color-swatch" style="background-color: #ffffff; border: 1px solid #cccccc;"></div>
                      <div class="color-label">Vit</div>
                    </div>
                    <div class="door-color-option" data-value="gray" data-side="outside">
                      <div class="color-swatch" style="background-color: #808080;"></div>
                      <div class="color-label">Grå</div>
                    </div>
                    <div class="door-color-option" data-value="black" data-side="outside">
                      <div class="color-swatch" style="background-color: #222222;"></div>
                      <div class="color-label">Svart</div>
                    </div>
                    <div class="door-color-option" data-value="anthracite" data-side="outside">
                      <div class="color-swatch" style="background-color: #383c3f;"></div>
                      <div class="color-label">Antracitgrå</div>
                    </div>
                    <div class="door-color-option" data-value="darkgray" data-side="outside">
                      <div class="color-swatch" style="background-color: #505050;"></div>
                      <div class="color-label">Mörkgrå</div>
                    </div>
                    <div class="door-color-option" data-value="brown" data-side="outside">
                      <div class="color-swatch" style="background-color: #654321;"></div>
                      <div class="color-label">Brun</div>
                    </div>
                  </div>
                </div>

                <div class="builder-actions">
                  <button class="btn prev-btn">Tillbaka</button>
                  <button class="btn save-btn">Spara</button>
                </div>
              </div>
            </section>
          </aside>

          <!-- Förhandsvisning & Specifikation -->
          <section class="result-column">
            <div class="preview-container">
              <div id="doorPreview" class="preview"></div>
            </div>
            <div class="spec-container">
              <h3>Specifikation</h3>
              <table id="doorSpecTable">
                <thead>
                  <tr><th>Egenskap</th><th>Värde</th></tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </section>
        </main>
      </div> <!-- End of doorConfigurator -->
  </section>

  <!-- SEKTION: Mina sparade designs -->
  <section id="sectionSavedDesigns" class="hidden">
    <div class="saved-section">
      <!-- SIDOPANEL -->
      <aside class="saved-sidebar">
        <div class="saved-toolbar">
          <input
            type="search"
            id="searchSaved"
            class="search-input"
            placeholder="Sök dina sparade designs…"
          />
          <button id="sortDate" class="sort-btn" data-order="desc">Senaste först</button>
        </div>
        <div id="savedList" class="saved-list">
          <!-- JS renderar .saved-card här -->
        </div>
      </aside>
      <!-- DETALJPANEL -->
      <div id="savedDetail" class="saved-detail">
        <p class="placeholder-text">Välj en design för att se detaljer</p>
      </div>
    </div>
  </section>

  <!-- SEKTION: Exportera -->
  <section id="sectionExport" class="hidden">
    <h2>Exportera till PDF</h2>
    <p>PDF-export-funktion kommer här.</p>
    <button id="exportBtn" class="btn">Exportera alla sparade designs som PDF</button>

    <!-- Progress bar container -->
    <div id="pdfProgressContainer" class="pdf-progress-container hidden">
      <div class="pdf-progress-text">Genererar PDF...</div>
      <div class="pdf-progress-bar-outer">
        <div id="pdfProgressBar" class="pdf-progress-bar-inner"></div>
      </div>
      <div id="pdfProgressStatus" class="pdf-progress-status">0%</div>
    </div>
  </section>

  <!-- TEMPLATE FOR PDF EXPORT (hidden on screen) -->
  <div id="pdfExportContainer" class="hidden">
    <!-- Cover page -->
    <div class="pdf-page cover">
      <img src="logo.png" class="pdf-logo" alt="Company Logo">
      <h1 class="pdf-title">Mina Sparade Designs</h1>
      <p class="pdf-date">Exportdatum: <span id="exportDate"></span></p>
    </div>
    <!-- Per‐design pages will be injected here by pdf.js -->
  </div>
  <script src="js/script.js"></script>
  <script src="js/handle-init.js"></script>
  <script src="js/steg4.js"></script>
  <script src="js/steg5.js"></script>
  <script src="js/steg6.js"></script>
  <script src="js/steg7.js"></script>
  <script src="js/steg8.js"></script>
  <script src="js/steg9.js"></script>
  <script src="js/steg10.js"></script>
  <script src="js/step-navigation.js"></script>
  <script src="js/preview-renderer.js"></script>
  <script src="js/door-preview-renderer.js"></script>
  <script src="js/3d-preview.js"></script>
  <script src="js/pdf.js"></script>
  <script src="js/configurator-selection.js"></script>
  <script src="js/door-configurator.js"></script>
  <script src="js/door-save.js"></script>
</body>
</html>
