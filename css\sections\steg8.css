/* CSS för <PERSON><PERSON>-steget */

.sprojs-section {
  margin-bottom: 2rem;
}

.sprojs-section h3 {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: var(--color-text);
}

.sprojs-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.sprojs-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  border: 2px solid transparent;
  transition: all 0.2s ease;
  width: 100px;
}

.sprojs-option:hover {
  background-color: rgba(245, 199, 0, 0.1);
}

.sprojs-option.active {
  border-color: var(--color-primary);
  background-color: rgba(245, 199, 0, 0.15);
}

.sprojs-preview {
  width: 80px;
  height: 80px;
  border: 2px solid #666;
  margin-bottom: 0.5rem;
  position: relative;
  background-color: rgba(200, 230, 255, 0.2);
}

/* Spröjs-linjer för olika alternativ */
.sprojs-preview.none {
  /* Inga spröjs */
}

.sprojs-preview.one-horizontal::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  height: 2px;
  background-color: #666;
  transform: translateY(-50%);
}

.sprojs-preview.one-vertical::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #666;
  transform: translateX(-50%);
}

.sprojs-preview.cross::before,
.sprojs-preview.cross::after {
  content: '';
  position: absolute;
  background-color: #666;
}

.sprojs-preview.cross::before {
  left: 0;
  top: 50%;
  width: 100%;
  height: 2px;
  transform: translateY(-50%);
}

.sprojs-preview.cross::after {
  left: 50%;
  top: 0;
  width: 2px;
  height: 100%;
  transform: translateX(-50%);
}

.sprojs-preview.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.sprojs-preview.grid::before,
.sprojs-preview.grid::after,
.sprojs-preview.grid .h-line,
.sprojs-preview.grid .v-line {
  content: '';
  position: absolute;
  background-color: #666;
}

.sprojs-preview.grid::before {
  left: 0;
  top: 50%;
  width: 100%;
  height: 2px;
  transform: translateY(-50%);
}

.sprojs-preview.grid::after {
  left: 50%;
  top: 0;
  width: 2px;
  height: 100%;
  transform: translateX(-50%);
}

.sprojs-label {
  font-size: 0.9rem;
  text-align: center;
}

/* Luftspecifika spröjs-inställningar */
.luft-sprojs-container {
  margin-top: 1.5rem;
}

.luft-sprojs-item {
  margin-bottom: 1.5rem;
  padding: 0.8rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
}

.luft-sprojs-header {
  font-weight: bold;
  margin-bottom: 0.8rem;
}

.luft-sprojs-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.luft-sprojs-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  cursor: pointer;
  position: relative;
  background-color: rgba(200, 230, 255, 0.2);
}

.luft-sprojs-option:hover {
  background-color: rgba(245, 199, 0, 0.1);
}

.luft-sprojs-option.active {
  border-color: var(--color-primary);
  background-color: rgba(245, 199, 0, 0.15);
}

/* Spröjs-linjer för luftspecifika alternativ */
.luft-sprojs-option.none {
  /* Inga spröjs */
}

.luft-sprojs-option.one-horizontal::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  height: 1px;
  background-color: #666;
  transform: translateY(-50%);
}

.luft-sprojs-option.one-vertical::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: #666;
  transform: translateX(-50%);
}

.luft-sprojs-option.cross::before,
.luft-sprojs-option.cross::after {
  content: '';
  position: absolute;
  background-color: #666;
}

.luft-sprojs-option.cross::before {
  left: 0;
  top: 50%;
  width: 100%;
  height: 1px;
  transform: translateY(-50%);
}

.luft-sprojs-option.cross::after {
  left: 50%;
  top: 0;
  width: 1px;
  height: 100%;
  transform: translateX(-50%);
}

.luft-sprojs-option.grid::before,
.luft-sprojs-option.grid::after,
.luft-sprojs-option.grid .h-line,
.luft-sprojs-option.grid .v-line {
  content: '';
  position: absolute;
  background-color: #666;
}

.luft-sprojs-option.grid::before {
  left: 0;
  top: 33.33%;
  width: 100%;
  height: 1px;
}

.luft-sprojs-option.grid::after {
  left: 0;
  top: 66.66%;
  width: 100%;
  height: 1px;
}

.luft-sprojs-option.grid .v-line:nth-child(1) {
  left: 33.33%;
  top: 0;
  width: 1px;
  height: 100%;
}

.luft-sprojs-option.grid .v-line:nth-child(2) {
  left: 66.66%;
  top: 0;
  width: 1px;
  height: 100%;
}

/* Anpassade spröjs-inställningar */
.custom-sprojs-settings {
  margin-top: 1rem;
  padding: 0.8rem;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  background-color: rgba(245, 245, 245, 0.5);
}

.custom-sprojs-settings h4 {
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.sprojs-count-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.8rem;
}

.sprojs-count-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sprojs-count-group label {
  font-size: 0.9rem;
  font-weight: bold;
}

.sprojs-count-input {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sprojs-count-input button {
  width: 30px;
  height: 30px;
  border: 1px solid var(--color-border);
  background-color: #f0f0f0;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.sprojs-count-input button:hover {
  background-color: #e0e0e0;
}

.sprojs-count-input input {
  width: 40px;
  height: 30px;
  text-align: center;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

.sprojs-preview-container {
  width: 100px;
  height: 100px;
  border: 2px solid #666;
  position: relative;
  background-color: rgba(200, 230, 255, 0.2);
  margin: 0 auto;
}

/* Dynamiska spröjs-linjer */
.sprojs-h-line, .sprojs-v-line {
  position: absolute;
  background-color: #666;
}

.sprojs-h-line {
  left: 0;
  width: 100%;
  height: 1px;
}

.sprojs-v-line {
  top: 0;
  width: 1px;
  height: 100%;
}

/* 2D-preview spröjs */
.window-frame .sprojs-line {
  stroke: #333;
  stroke-width: 1px;
  pointer-events: none;
}
