/* CSS för myggnät-steget */

.mosquito-net-section {
  margin-bottom: 20px;
}

/* Grupp för myggnätsval - matchar material-group */
.mosquito-net-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 15px;
  margin-bottom: 1.5rem;
}

/* Myggnätsknappar - matchar antal glas-knappar */
.mosquito-net-option-btn {
  flex: 1 1 calc(33% - 1rem);
  padding: 1rem 0.5rem;
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  color: var(--color-secondary);
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.mosquito-net-option-btn:hover:not(:disabled) {
  border-color: var(--color-primary);
}

/* Aktivt val */
.mosquito-net-option-btn.active {
  background: transparent;
  color: var(--color-secondary);
  border: 2px solid var(--color-primary);
  transform: none;
  font-weight: bold;
}

/* Inak<PERSON>rad knapp */
.mosquito-net-option-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  transform: none;
  pointer-events: none;
}

/* Förbättrad informationsruta */
.mosquito-net-info {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  padding: 12px 15px;
  background-color: #333333;
  color: white;
  border-radius: var(--radius);
  border-left: 4px solid #f5c700;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-icon {
  font-style: normal;
  color: #f5c700;
  font-size: 1.2rem;
  margin-right: 10px;
  font-weight: bold;
}
