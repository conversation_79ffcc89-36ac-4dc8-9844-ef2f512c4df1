/**
 * pdf-renderer.js
 * Specialversion av preview-renderer för PDF-export
 */

(() => {
  // Exponera funktioner globalt
  window.PDFRenderer = {
    renderWindowFrame
  };

  /**
   * Skapar ett fönsterelement baserat på konfiguration för PDF-export
   * @param {Object} config - Konfiguration för fönstret
   * @returns {HTMLElement} - DOM-element för fönstret
   */
  function renderWindowFrame(config) {
    const { size, opening, hinge, sprojsType, customSprojs } = config;

    // Skapa fönsterram med korrekt positionering
    const frame = document.createElement('div');
    frame.className = 'window-frame';
    frame.style.flex = `${size} 0 0`;
    frame.style.position = 'relative';
    frame.style.boxSizing = 'border-box';
    frame.style.padding = '0';
    frame.style.margin = '0';

    // Lägg till glasruta med korrekt positionering
    const pane = document.createElement('div');
    pane.className = 'pane';
    pane.style.position = 'absolute';
    pane.style.top = '8px';
    pane.style.left = '8px';
    pane.style.right = '8px';
    pane.style.bottom = '8px';
    pane.style.width = 'calc(100% - 16px)';
    pane.style.height = 'calc(100% - 16px)';
    frame.appendChild(pane);

    // Skapa SVG för öppningslinjer med förbättrad positionering
    const svg = createSVG('svg', {
      class: 'frame-svg',
      viewBox: '0 0 100 100',
      preserveAspectRatio: 'none',
      width: '100%',
      height: '100%',
      'vector-effect': 'non-scaling-stroke'
    });

    // Säkerställ att SVG är korrekt positionerad i fönsterrutan
    svg.style.position = 'absolute';
    svg.style.top = '0';
    svg.style.left = '0';
    svg.style.right = '0';
    svg.style.bottom = '0';
    svg.style.width = '100%';
    svg.style.height = '100%';

    frame.appendChild(svg);

    // Lägg till öppningslinjer om det inte är ett fast fönster
    if (opening !== 'fixed') {
      addOpeningLines(svg, opening, hinge);

      // Lägg till handtag
      const handle = createHandle(opening, hinge);
      frame.appendChild(handle);
    }

    // Lägg till spröjs om det finns
    if (sprojsType && sprojsType !== 'none') {
      addSprojsLines(svg, sprojsType, customSprojs);
    }

    return frame;
  }

  /**
   * Lägger till spröjslinjer i SVG baserat på spröjstyp
   * @param {SVGElement} svg - SVG-element att lägga till linjer i
   * @param {string} sprojsType - Typ av spröjs
   * @param {Object} customSprojs - Anpassade spröjsinställningar (för custom-typ)
   */
  function addSprojsLines(svg, sprojsType, customSprojs) {
    // Skapa linjer baserat på spröjstyp
    switch (sprojsType) {
      case 'one-horizontal': {
        // En horisontell spröjs
        const line = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 100,
          y2: 50,
          'stroke-width': '1',
          'stroke': '#333',
          'vector-effect': 'non-scaling-stroke',
          class: 'sprojs-line'
        });
        svg.appendChild(line);
        break;
      }
      case 'one-vertical': {
        // En vertikal spröjs
        const line = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '1',
          'stroke': '#333',
          'vector-effect': 'non-scaling-stroke',
          class: 'sprojs-line'
        });
        svg.appendChild(line);
        break;
      }
      case 'cross': {
        // Korsande spröjs (en horisontell och en vertikal)
        const hLine = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 100,
          y2: 50,
          'stroke-width': '1',
          'stroke': '#333',
          'vector-effect': 'non-scaling-stroke',
          class: 'sprojs-line'
        });

        const vLine = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '1',
          'stroke': '#333',
          'vector-effect': 'non-scaling-stroke',
          class: 'sprojs-line'
        });

        svg.appendChild(hLine);
        svg.appendChild(vLine);
        break;
      }
      case 'grid': {
        // Rutnät (två horisontella och två vertikala)
        // Horisontella linjer
        for (let i = 1; i < 3; i++) {
          const line = createSVG('line', {
            x1: 0,
            y1: i * 33.33,
            x2: 100,
            y2: i * 33.33,
            'stroke-width': '1',
            'stroke': '#333',
            'vector-effect': 'non-scaling-stroke',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }

        // Vertikala linjer
        for (let i = 1; i < 3; i++) {
          const line = createSVG('line', {
            x1: i * 33.33,
            y1: 0,
            x2: i * 33.33,
            y2: 100,
            'stroke-width': '1',
            'stroke': '#333',
            'vector-effect': 'non-scaling-stroke',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }
        break;
      }
      case 'custom': {
        // Anpassade spröjs
        if (!customSprojs) {
          customSprojs = { horizontal: 2, vertical: 2 };
        }

        const hCount = customSprojs.horizontal || 0;
        const vCount = customSprojs.vertical || 0;

        // Horisontella linjer
        for (let i = 0; i < hCount; i++) {
          const position = (i + 1) * (100 / (hCount + 1));
          const line = createSVG('line', {
            x1: 0,
            y1: position,
            x2: 100,
            y2: position,
            'stroke-width': '1',
            'stroke': '#333',
            'vector-effect': 'non-scaling-stroke',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }

        // Vertikala linjer
        for (let i = 0; i < vCount; i++) {
          const position = (i + 1) * (100 / (vCount + 1));
          const line = createSVG('line', {
            x1: position,
            y1: 0,
            x2: position,
            y2: 100,
            'stroke-width': '1',
            'stroke': '#333',
            'vector-effect': 'non-scaling-stroke',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }
        break;
      }
    }
  }

  /**
   * Lägger till öppningslinjer i SVG baserat på öppningstyp
   * @param {SVGElement} svg - SVG-element att lägga till linjer i
   * @param {string} opening - Typ av öppning
   * @param {string} hinge - Typ av gångjärn (för sidohängda fönster)
   */
  function addOpeningLines(svg, opening, hinge) {
    // Säkerställ att SVG har rätt attribut för korrekt rendering
    svg.setAttribute('preserveAspectRatio', 'none');
    svg.setAttribute('viewBox', '0 0 100 100');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');

    // Skapa linjer baserat på öppningstyp
    switch (opening) {
      case 'side-hung': {
        const px = hinge === 'side-left' ? 100 : 0;
        const ox = 100 - px;

        // Skapa linjer med förbättrad synlighet
        const line1 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 0,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line2 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 100,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'top-hung': {
        const line1 = createSVG('line', {
          x1: 0,
          y1: 0,
          x2: 50,
          y2: 50,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line2 = createSVG('line', {
          x1: 100,
          y1: 0,
          x2: 50,
          y2: 50,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'bottom-hung': {
        const line1 = createSVG('line', {
          x1: 0,
          y1: 100,
          x2: 50,
          y2: 50,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line2 = createSVG('line', {
          x1: 100,
          y1: 100,
          x2: 50,
          y2: 50,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'tilt-turn': {
        const line1 = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 0,
          y2: 50,
          'stroke-dasharray': '4,4',
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line2 = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 100,
          y2: 50,
          'stroke-dasharray': '4,4',
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line3 = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        const line4 = createSVG('line', {
          x1: 100,
          y1: 50,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke',
          'class': 'opening-line'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        svg.appendChild(line3);
        svg.appendChild(line4);
        break;
      }
    }
  }

  /**
   * Skapar ett handtag baserat på öppningstyp och gångjärnsplacering
   * @param {string} opening - Typ av öppning
   * @param {string} hinge - Typ av gångjärn (för sidohängda fönster)
   * @returns {HTMLElement} - DOM-element för handtaget
   */
  function createHandle(opening, hinge) {
    const handle = document.createElement('div');
    handle.className = 'handle';

    // Placera handtaget baserat på öppningstyp och gångjärnsplacering
    switch (opening) {
      case 'side-hung':
        handle.classList.add(hinge === 'side-left' ? 'side-right' : 'side-left');
        break;
      case 'top-hung':
        handle.classList.add('bottom-center');
        break;
      case 'bottom-hung':
        handle.classList.add('top-center');
        break;
      case 'tilt-turn':
        handle.classList.add('side-right');
        break;
    }

    return handle;
  }

  /**
   * Hjälpfunktion för att skapa SVG-element
   * @param {string} tag - SVG-tagg
   * @param {Object} attrs - Attribut för elementet
   * @returns {SVGElement} - Skapat SVG-element
   */
  function createSVG(tag, attrs) {
    const ns = 'http://www.w3.org/2000/svg';
    const el = document.createElementNS(ns, tag);

    for (const [key, value] of Object.entries(attrs)) {
      el.setAttribute(key, value);
    }

    return el;
  }
})();
