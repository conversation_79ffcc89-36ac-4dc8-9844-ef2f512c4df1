// handle-debug.js - <PERSON><PERSON><PERSON> to debug handle selection functionality

// Add this script to index.html after steg9.js to debug handle selection

(function() {
  console.log('Handle Debug Script Loaded');
  
  // Function to check if handle selection is working
  function checkHandleSelection() {
    console.log('Checking handle selection functionality...');
    
    // Check if handleHandleClick function exists
    if (typeof window.handleHandleClick === 'function') {
      console.log('✅ window.handleHandleClick function exists');
    } else {
      console.error('❌ window.handleHandleClick function is missing');
    }
    
    // Check if setupHandleOptions function exists
    if (typeof window.setupHandleOptions === 'function') {
      console.log('✅ window.setupHandleOptions function exists');
    } else {
      console.error('❌ window.setupHandleOptions function is missing');
    }
    
    // Check if HANDLE_LABELS exists
    if (window.HANDLE_LABELS) {
      console.log('✅ window.HANDLE_LABELS exists');
    } else {
      console.error('❌ window.HANDLE_LABELS is missing');
    }
    
    // Add click event listeners to handle options
    const handleOptions = document.querySelectorAll('.handle-option');
    console.log(`Found ${handleOptions.length} handle options`);
    
    handleOptions.forEach(option => {
      console.log(`Adding debug click listener to handle option: ${option.dataset.value}`);
      
      // Add a debug click event listener
      option.addEventListener('click', function(event) {
        console.log(`Handle option clicked: ${this.dataset.value}`);
        console.log('Event target:', event.target);
        
        // Call the original click handler
        if (typeof window.handleHandleClick === 'function') {
          console.log('Calling window.handleHandleClick...');
          window.handleHandleClick(this, this.dataset.value);
        }
      });
    });
  }
  
  // Run the check when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', checkHandleSelection);
  } else {
    // DOM is already loaded
    setTimeout(checkHandleSelection, 500);
  }
})();
