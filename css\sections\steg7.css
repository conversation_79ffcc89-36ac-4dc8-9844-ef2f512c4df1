/* CSS för <PERSON>steget */

.color-section {
  margin-bottom: 2rem;
}

.color-section h3 {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: var(--color-text);
}

.color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.color-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option:hover {
  background-color: rgba(245, 199, 0, 0.1);
}

.color-option.active {
  border-color: var(--color-primary);
  background-color: rgba(245, 199, 0, 0.15);
}

.color-swatch {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-label {
  font-size: 0.9rem;
  color: var(--color-text);
}

/* <PERSON><PERSON><PERSON>j utsida färgsektion som standard */
#outsideColorSection.hidden {
  display: none;
}
