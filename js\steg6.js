// steg6.js - Glastyp (olika typer av glas)

(() => {
  // Konstanter för glastyper - definiera globalt så att alla funktioner kan använda dem
  window.GLASS_TYPE_LABELS = {
    'clear': '<PERSON>lart glas',
    'sound': 'Ljudreducerande glas',
    'sun': 'Solskyddsglas',
    'granite': 'Granit',
    'cotswold': 'Cotswold',
    'frosted': 'Frostat glas',
    'tempered': 'Härdat glas'
  };

  // Priser för olika glastyper (borttagna för nu)
  window.GLASS_TYPE_PRICES = {};

  // Beskrivningar för olika glastyper
  window.GLASS_TYPE_DESCRIPTIONS = {
    'clear': 'Det vanligaste glaset till flest ändamål',
    'sound': 'Ljudreducerande glas hjälper genom att skapa en tyst inomhusmiljö. En tyst inomhusmiljö ger större bekvämlighet och trivsel.',
    'sun': 'Solskyddsglas blockerar 63% av solens värme och har 10-12% mindre ljusinsläpp',
    'granite': 'Granit är det mest använda rågluset och kännetecknas av dess utseendemässiga grova och ojämna yta.',
    'cotswold': 'Cotswold är ett råglas som kännetecknas av de vertikala linjerna och den ojämna ytan.',
    'frosted': 'Frostat glas är den mer moderna typen av råglas, med en mer mjuk och silkeslik yta.',
    'tempered': 'Våra härdade glas är 4 gånger starkare än normalt glas'
  };

  // --- Steg 6: Glastyp-knappar & live-render i "Ny design" ---
  // Definiera specBody globalt så att den är tillgänglig för renderGlassTypeSpec
  let specBody;

  document.addEventListener('DOMContentLoaded', () => {
    const glassTypeBtns = document.querySelectorAll('.glass-type-option');
    specBody = document.querySelector('#specTable tbody');
    window.selectedGlassTypeValue = 'clear'; // Standard är klart glas

    // Markera standardvalet
    document.querySelector('.glass-type-option[data-value="clear"]').classList.add('active');

    // Gör funktionen tillgänglig globalt så att den kan anropas från loadConfiguration
    window.renderGlassTypeValueSpec = function() {
      // Kontrollera att specBody är tillgänglig
      if (!specBody) {
        specBody = document.querySelector('#specTable tbody');
        if (!specBody) return; // Om specBody fortfarande inte finns, avbryt
      }

      const old = specBody.querySelector('tr[data-key="glassTypeValue"]');
      if (old) old.remove();
      if (!window.selectedGlassTypeValue) return;

      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'glassTypeValue');
      tr.innerHTML = `<td>Glastyp</td><td>${window.GLASS_TYPE_LABELS[window.selectedGlassTypeValue] || window.selectedGlassTypeValue}</td>`;
      specBody.appendChild(tr);
    }

    glassTypeBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        glassTypeBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        window.selectedGlassTypeValue = btn.dataset.value;
        window.renderGlassTypeValueSpec();
      });
    });

    if (typeof window.updateSpecTable === 'function') {
      const orig = window.updateSpecTable;
      window.updateSpecTable = () => {
        orig();
        window.renderGlassTypeValueSpec();
      };
    }

    // Spara glastyp tillsammans med resten
    const saveBtn = document.querySelector('#section-7 .save-btn'); // Steg 8 (section-7)
    if (saveBtn) { // Kontrollera att knappen finns
      saveBtn.addEventListener('click', () => {
        setTimeout(() => {
          const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
          const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
          if (idx >= 0) {
            arr[idx].glassTypeValue = window.selectedGlassTypeValue;
            localStorage.setItem('myWindows', JSON.stringify(arr));
          }
        }, 0);
      });
    } else {
      console.warn("Kunde inte hitta save-knappen i section-7 (steg 8)");
    }
  });

  // --- Patch av showSavedDetail för "Mina sparade designs" ---
  function patchShowSavedDetail() {
    if (typeof window.showSavedDetail !== 'function') {
      // Om inte definierad än, testa igen om 50ms
      return setTimeout(patchShowSavedDetail, 50);
    }

    const orig = window.showSavedDetail;
    window.showSavedDetail = (cfg, idx) => {
      orig(cfg, idx);

      // Hämta <tbody> i sparad-vy
      const tbody = document.querySelector('#savedDetail .saved-spec tbody');
      if (!tbody) return;

      // Ta bort ev. tidigare glastyp-rad
      const existing = tbody.querySelector('tr[data-key="glassTypeValue"]');
      if (existing) existing.remove();

      // Skapa och lägg till glastyp-rad om det finns
      if (cfg.glassTypeValue) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'glassTypeValue');
        tr.innerHTML = `<td>Glastyp</td><td>${window.GLASS_TYPE_LABELS[cfg.glassTypeValue] || cfg.glassTypeValue}</td>`;
        tbody.appendChild(tr);
      }
    };
  }

  // Kör patchen efter att hela sidan laddat
  document.addEventListener('DOMContentLoaded', patchShowSavedDetail);

})();
