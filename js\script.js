// js/script.js

document.addEventListener('DOMContentLoaded', () => {
  // ─── NAVBAR ─────────────────────────────────────────
  const navBtns = document.querySelectorAll('.top-nav button');
  const mainSections = [
    document.getElementById('sectionNewDesign'),
    document.getElementById('sectionSavedDesigns'),
    document.getElementById('sectionExport'),
  ];
  navBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      navBtns.forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
      mainSections.forEach(sec => sec.classList.add('hidden'));
      document.getElementById(btn.dataset.target).classList.remove('hidden');

      if (btn.id === 'navNewDesign') {
        // Visa konfigurator-valsidan istället för att direkt gå till fönsterkonfiguratorn
        const configuratorSelection = document.getElementById('configuratorSelection');
        const windowConfigurator = document.getElementById('windowConfigurator');

        if (configuratorSelection && windowConfigurator) {
          configuratorSelection.classList.remove('hidden');
          windowConfigurator.classList.add('hidden');
        } else {
          // Fallback om DOM-elementen inte finns
          goTo(0);
          setTimeout(updateAll, 0);
        }
      }

      if (btn.id === 'navSavedDesigns') {
        renderSavedList();
      }
    });
  });

  window.showSavedDetail = showSavedDetail;
  window.renderSavedList  = renderSavedList;  // om du behöver anropa den också
  window.selectedMaterial = null;
  window.selectedGlassType = null;
  window.buildOpeningControls = buildOpeningControls; // Exponera för att kunna anropas från step-navigation.js

  // --- Material-etiketter ---
  const MATERIAL_LABELS = {
    tra:           'Trä',
    pvc:           'PVC',
    aluminium:     'Aluminium',
    'tra-aluminium': 'Trä/Aluminium',
    'pvc-aluminium': 'PVC/Aluminium'
  };

  // --- Spröjs-etiketter ---
  window.SPROJS_LABELS = {
    'none': 'Inga spröjs',
    'one-horizontal': 'En horisontell',
    'one-vertical': 'En vertikal',
    'cross': 'Kors',
    'grid': 'Rutnät',
    'custom': 'Anpassad'
  };

  // ─── REFERENSER FÖR KONFIGURATORN ───────────────────
  const sections      = Array.from(document.querySelectorAll('.builder-section'));
  const previewEl     = document.getElementById('preview');
  const specBody      = document.querySelector('#specTable tbody');
  const stepBtns      = document.querySelectorAll('.step-nav .step');
  const widthInput    = document.getElementById('widthInput');
  const heightInput   = document.getElementById('heightInput');
  const widthLabel    = document.getElementById('widthLabel');
  const heightLabel   = document.getElementById('heightLabel');
  const saveBtn       = document.querySelector('#section-10 .save-btn'); // Uppdaterat till section-10 (steg 11)
  const PIXELS_PER_CM = 2;

  let windowWidthCm   = +widthInput.value;
  let windowHeightCm  = +heightInput.value;
  let count           = 1;
  let openings        = ['fixed']; // Fast glas som standard
  let hinges          = [null];
  let openSizes       = [ windowWidthCm ];
  let editingIndex    = null; // index när en befintlig design redigeras

  const clamp = (v, min, max) => v < min ? min : v > max ? max : v;

  // ─── Dimensioner ────────────────────────────────────
  widthInput.addEventListener('input', () => {
    windowWidthCm = clamp(+widthInput.value, +widthInput.min, +widthInput.max);
    widthLabel.textContent = `${windowWidthCm} cm`;
    redistributeSizes();

    // Uppdatera öppning per luft-sektionen om den är synlig
    const openingSection = document.getElementById('section-3');
    if (openingSection && !openingSection.classList.contains('collapsed')) {
      buildOpeningControls();
    }

    updateAll();
  });
  heightInput.addEventListener('input', () => {
    windowHeightCm = clamp(+heightInput.value, +heightInput.min, +heightInput.max);
    heightLabel.textContent = `${windowHeightCm} cm`;
    updateAll();
  });
  function redistributeSizes() {
    // Beräkna basbredd för varje luft
    const base = Math.floor(windowWidthCm / count);

    // Om vi redan har storlekar, försök bevara proportionerna
    if (openSizes && openSizes.length > 0 && openSizes.length === count) {
      const oldTotal = openSizes.reduce((sum, size) => sum + size, 0);
      if (oldTotal > 0) {
        // Beräkna nya storlekar baserat på proportioner
        const newSizes = openSizes.map(size =>
          Math.floor((size / oldTotal) * windowWidthCm)
        );

        // Justera för avrundningsfel
        let sum = newSizes.reduce((a, b) => a + b, 0);
        let diff = windowWidthCm - sum;

        // Lägg till eller dra bort differensen från sista luften
        if (diff !== 0) {
          newSizes[count - 1] += diff;
        }

        // Säkerställ att ingen luft är mindre än 10cm
        const minSize = 10;
        let needsRebalance = false;

        for (let i = 0; i < newSizes.length; i++) {
          if (newSizes[i] < minSize) {
            needsRebalance = true;
            break;
          }
        }

        if (!needsRebalance) {
          openSizes = newSizes;
          return;
        }
      }
    }

    // Fallback: Jämn fördelning om vi inte kan bevara proportioner
    openSizes = Array(count).fill(base);
    openSizes[count - 1] = windowWidthCm - base * (count - 1);
  }

  // ─── Antal luft ─────────────────────────────────────
  const qtyBtns    = document.querySelectorAll('.qty-option-btn');
  const customCtrl = document.querySelector('.custom-control');
  const customIn   = document.getElementById('customCount');
  const decBtn     = document.getElementById('decrement');
  const incBtn     = document.getElementById('increment');

  qtyBtns.forEach(btn => btn.addEventListener('click', () => {
    qtyBtns.forEach(b => b.classList.remove('active'));
    btn.classList.add('active');
    const v = btn.dataset.value;
    if (v === 'custom') {
      customCtrl.classList.remove('hidden');
      updateCount(+customIn.value);
    } else {
      customCtrl.classList.add('hidden');
      updateCount(+v);
    }
  }));
  decBtn.addEventListener('click', () => updateCount(count - 1));
  incBtn.addEventListener('click', () => updateCount(count + 1));
  customIn.addEventListener('input', () => updateCount(+customIn.value));

  function updateCount(v) {
    count = clamp(v || 1, 1, 10);
    customIn.value = count;
    openings = Array(count).fill('fixed');
    hinges   = Array(count).fill(null);
    redistributeSizes();

    // Uppdatera öppning per luft-sektionen om den är synlig
    const openingSection = document.getElementById('section-2'); // Korrekt ID för öppning per luft-sektionen
    if (openingSection && !openingSection.classList.contains('collapsed')) {
      buildOpeningControls();
    }

    updateAll();
  }

  // ─── Steg-navigation ─────────────────────────────────
  sections.forEach((sec, i) => {
    sec.querySelectorAll('.next-btn').forEach(b => b.addEventListener('click', () => goTo(i + 1)));
    sec.querySelectorAll('.prev-btn').forEach(b => b.addEventListener('click', () => goTo(i - 1)));
  });
  stepBtns.forEach(b => b.addEventListener('click', () => goTo(+b.dataset.step)));

  // Denna funktion används fortfarande av vissa delar av koden
  // men huvudfunktionaliteten har flyttats till step-navigation.js
  function goTo(i) {
    if (i < 0 || i >= sections.length) return;

    // Kontrollera om sektionen redan är öppen
    const isAlreadyOpen = !sections[i].classList.contains('collapsed');

    if (isAlreadyOpen) {
      // Om sektionen redan är öppen, stäng den och avaktivera alla
      sections.forEach(s => {
        s.classList.remove('expanded');
        s.classList.add('collapsed');
      });
      stepBtns.forEach(b => b.classList.remove('active'));
    } else {
      // Annars, öppna den valda sektionen och stäng övriga
      sections.forEach((s, idx) => {
        s.classList.toggle('expanded', idx === i);
        s.classList.toggle('collapsed', idx !== i);
      });
      stepBtns.forEach(b => b.classList.toggle('active', +b.dataset.step === i));

      // Scrolla till den öppnade sektionen
      if (sections[i]) {
        sections[i].scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }

    // Bygg öppningskontroller om vi är på steg 3
    if (i === 2) buildOpeningControls();
  }

  // ─── STEG 3: Öppning per luft ───────────────────────
  // ─── STEG 3: Öppning per luft ───────────────────────
function buildOpeningControls() {
  // Säkerställ att openings har rätt värde för 1 luft
  if (count === 1 && (!openings || openings.length !== 1)) {
    openings = ['fixed']; //
    hinges = ['null']; //
  }
  const cont = document.getElementById('openingsContainer');
  cont.innerHTML = '';

  const types = [
    { value: 'fixed',       label: 'Fast' },
    { value: 'side-hung',   label: 'Sidohängt' },
    { value: 'top-hung',    label: 'Topphängt' },
    { value: 'tilt-turn',   label: 'Vridfönster' },
    { value: 'bottom-hung', label: 'Bottenhängt' }
  ];
  const globalMax = windowWidthCm - 10 * (count - 1);

  // Säkerställ att openSizes har rätt längd
  if (openSizes.length !== count) {
    redistributeSizes();
  }

  function recalcLast() {
    const used = openSizes.slice(0, count - 1).reduce((a, b) => a + b, 0);
    openSizes[count - 1] = windowWidthCm - used;
  }

  function refreshFields() {
    cont.querySelectorAll('.size-control').forEach((ctrl, j) => {
      const dec = ctrl.querySelector('.size-dec');
      const inc = ctrl.querySelector('.size-inc');
      const inp = ctrl.querySelector('.size-input');
      if (dec) dec.disabled = openSizes[j] <= 10;
      if (inc) inc.disabled = openSizes[j] >= globalMax;
      inp.value = openSizes[j];
      inp.max   = globalMax;
    });
  }

  function changeSize(idx, delta) {
    openSizes[idx] = clamp(openSizes[idx] + delta, 10, globalMax);
    recalcLast();
    refreshFields();
    updateAll();
  }

  // Byt ut specialfallet för count===1 – vi bygger alltid kontrollerna
  for (let i = 0; i < count; i++) {
    const wrap = document.createElement('div');
    wrap.className = 'opening-wrap';

    const typesHtml = types.map(opt =>
      `<button class="type-btn${openings[i] === opt.value ? ' active' : ''}" data-value="${opt.value}">${opt.label}</button>`
    ).join('');

    // Storlekskontroll: om bara en luft kan du välja hela storleken, annars som tidigare
    let sizeHtml = `
      <div class="size-control">
        <label for="size_${i}">Bredd:</label>
        ${i < count - 1
          ? `<button class="size-btn size-dec" data-index="${i}">−</button>
             <input type="number" id="size_${i}" class="size-input" min="10" max="${globalMax}" value="${openSizes[i]}">
             <button class="size-btn size-inc" data-index="${i}">+</button>`
          : `<input type="number" id="size_${i}" class="size-input" value="${openSizes[i]}" disabled>`
        }
        <span class="unit">cm</span>
      </div>
    `;

    wrap.innerHTML = `
      <div class="opening-header">Luft ${i+1}</div>
      <div class="type-group">${typesHtml}</div>
      ${sizeHtml}
      <div class="hinge-group${openings[i] === 'side-hung' ? ' visible' : ''}">
        <button class="hinge-btn${hinges[i] === 'side-left' ? ' active' : ''}" data-index="${i}" data-value="side-left">Vänsterhängd</button>
        <button class="hinge-btn${hinges[i] === 'side-right' ? ' active' : ''}" data-index="${i}" data-value="side-right">Högerhängd</button>
      </div>
    `;
    cont.appendChild(wrap);

    // Typ-knappar
    wrap.querySelectorAll('.type-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        openings[i] = btn.dataset.value;
        wrap.querySelectorAll('.type-btn').forEach(b => b.classList.toggle('active', b === btn));
        const hg = wrap.querySelector('.hinge-group');
        if (openings[i] === 'side-hung') {
          hinges[i] = 'side-left';
          hg.classList.add('visible');
          hg.querySelectorAll('.hinge-btn').forEach(b =>
            b.classList.toggle('active', b.dataset.value === 'side-left')
          );
        } else {
          hinges[i] = null;
          hg.classList.remove('visible');
        }
        updateAll();

        // Uppdatera window.openings direkt för att säkerställa att den är korrekt
        window.openings = [...openings];

        // Trigga händelsen för att meddela andra delar av applikationen
        document.dispatchEvent(new CustomEvent('openingTypeChanged', {
          detail: { openings: [...openings] }
        }));

        // Om handtagssteget är öppet, uppdatera handtagsalternativen direkt
        const handleSection = document.getElementById('section-8');
        if (handleSection && !handleSection.classList.contains('collapsed')) {
          if (typeof window.setupHandleOptions === 'function') {
            setTimeout(window.setupHandleOptions, 100);
          }
        }
      });
    });

    // Gångjärns-knappar
    wrap.querySelectorAll('.hinge-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const idx = +btn.dataset.index;
        hinges[idx] = btn.dataset.value;
        wrap.querySelectorAll('.hinge-btn').forEach(b => b.classList.toggle('active', b === btn));
        updateAll();
      });
    });
  }

  // Hold‐funktion för storleksknappar
  cont.querySelectorAll('.size-btn.size-dec, .size-btn.size-inc').forEach(btn => {
    const idx   = +btn.dataset.index;
    const delta = btn.classList.contains('size-inc') ? 1 : -1;
    let iv;
    btn.addEventListener('mousedown', () => {
      changeSize(idx, delta);
      iv = setInterval(() => changeSize(idx, delta), 150);
    });
    ['mouseup','mouseleave'].forEach(e => btn.addEventListener(e, () => clearInterval(iv)));
  });

  // Direktinmatning storlek
  cont.querySelectorAll('.size-input:not([disabled])').forEach(inp => {
    const idx = +inp.id.split('_')[1];
    inp.addEventListener('input', e => {
      const raw = e.target.value;
      if (!raw) return;
      openSizes[idx] = +raw > globalMax ? globalMax : +raw;
      recalcLast(); refreshFields(); updateAll();
    });
    inp.addEventListener('blur', e => {
      let num = +e.target.value;
      if (!e.target.value || num < 10) num = 10;
      openSizes[idx] = num;
      recalcLast(); refreshFields(); updateAll();
    });
  });
}

  // ─── Update Dimensions ──────────────────────────────
  function updateDimensions() {
    // Använd den nya preview-renderaren om tillgänglig
    if (window.PreviewRenderer && window.PreviewRenderer.updateDimensions) {
      window.PreviewRenderer.updateDimensions(windowWidthCm, windowHeightCm);
    } else {
      // Fallback till gammal kod
      const c = previewEl.parentNode;
      c.style.width  = (windowWidthCm  * PIXELS_PER_CM) + 'px';
      c.style.height = (windowHeightCm * PIXELS_PER_CM) + 'px';
    }
  }

  // ─── Render Preview ────────────────────────────────
  function renderPreview() {
    // Använd den nya preview-renderaren om tillgänglig
    if (window.PreviewRenderer && window.PreviewRenderer.renderPreview) {
      // Skapa spröjs-konfiguration för alla lufter
      const sprojsType = window.selectedSprojsType || 'none';
      let customSprojsPerLuft = null;

      if (sprojsType === 'custom' && window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0) {
        customSprojsPerLuft = window.customSprojsPerLuft;
      }

      window.PreviewRenderer.renderPreview({
        count,
        openSizes,
        openings,
        hinges,
        sprojsType: sprojsType,
        sprojsPerLuft: Array(count).fill(sprojsType),
        customSprojsPerLuft: customSprojsPerLuft
      });
    } else {
      // Fallback till gammal kod (borttagen för att göra koden mer clean)
      console.warn('PreviewRenderer not available, using fallback');
      // Fallback-kod skulle vara här
    }
  }

  // ─── Update Spec Table (NY DESIGN) ─────────────────
  function updateSpecTable() {
    specBody.innerHTML = '';
    [['Karmbredd',`${windowWidthCm} cm`],['Karmhöjd',`${windowHeightCm} cm`],['Antal luft',count]]
      .forEach(([k,v]) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>${k}</td><td>${v}</td>`;
        specBody.append(tr);
      });
    openSizes.forEach((w,i) => {
      const op = openings[i], hg = (op==='side-hung'&&hinges[i])?` (${hinges[i]})`:'';
      const tr = document.createElement('tr');
      tr.innerHTML = `<td>Luft ${i+1}</td><td>${op} ${w} cm${hg}</td>`;
      specBody.append(tr);
    });
    if (window.selectedMaterial) {
      const tr = document.createElement('tr');
      tr.innerHTML = `<td>Material</td><td>${MATERIAL_LABELS[window.selectedMaterial]}</td>`;
      specBody.append(tr);
    }

    // Lägg till glastyp om det finns
    if (window.selectedGlassType && typeof window.renderGlassTypeSpec === 'function') {
      window.renderGlassTypeSpec();
    }

    // Lägg till handtag i specifikationstabellen om ett handtag är valt
    if (window.selectedHandle && window.selectedHandle !== 'none' && window.HANDLE_LABELS) {
      const handleRow = specBody.querySelector('tr[data-key="handle"]');
      if (handleRow) handleRow.remove();

      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'handle');
      tr.innerHTML = `<td>Handtag</td><td>${window.HANDLE_LABELS[window.selectedHandle]}</td>`;
      specBody.appendChild(tr);
      console.log(`Lade till handtag i specifikationstabellen: ${window.HANDLE_LABELS[window.selectedHandle]}`);
    } else {
      // Ta bort handtagsrad om inget handtag är valt
      const handleRow = specBody.querySelector('tr[data-key="handle"]');
      if (handleRow) handleRow.remove();
    }
  }

// ─── Spara design (Steg 7) ───────────────────────────
saveBtn.addEventListener('click', () => {
  const arr = JSON.parse(localStorage.getItem('myDesigns') || '[]');

  // fråga alltid om namn
  const name = prompt('Ange namn för din design:');
  if (!name) return;

  // fråga om antal fönster
  const quantityStr = prompt('Ange antal fönster av denna design:');
  const quantity = parseInt(quantityStr, 10);
  if (isNaN(quantity) || quantity < 1) {
    return alert('Ogiltigt antal. Ange ett heltal ≥ 1.');
  }

  if (editingIndex !== null) {
    // Uppdatera befintlig
    // Logga aktuellt handtagsval innan sparande
    console.log('Uppdaterar design med handtagsval:', window.selectedHandle || 'stormkrok');

    arr[editingIndex] = {
      type: 'window', // Markera att detta är en fönsterdesign
      name,
      quantity,                  // <-- NYTT
      width:     windowWidthCm,
      height:    windowHeightCm,
      count,
      openings:  [...openings],
      hinges:    [...hinges],
      openSizes: [...openSizes],
      material:  window.selectedMaterial,
      glassType: window.selectedGlassType,
      glassTypeValue: window.selectedGlassTypeValue,
      insideColor: window.selectedInsideColor || 'white',
      outsideColor: window.selectedOutsideColor || 'white',
      sprojsType: window.selectedSprojsType || 'none',
      handle: window.selectedHandle || 'stormkrok',
      mosquitoNet: window.selectedMosquitoNet || false,
      customSprojsPerLuft: window.selectedSprojsType === 'custom' && window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0 ?
        [window.customSprojsPerLuft[0]] : undefined,
      savedAt:   new Date().toISOString()
    };
    localStorage.setItem('myDesigns', JSON.stringify(arr));
    alert(`Design uppdaterad: ${arr[editingIndex].name} (Antal: ${quantity})`);
    editingIndex = null;
  } else {
    // Ny design
    // Logga aktuellt handtagsval innan sparande
    console.log('Sparar ny design med handtagsval:', window.selectedHandle || 'stormkrok');

    arr.push({
      type: 'window', // Markera att detta är en fönsterdesign
      name,
      quantity,                  // <-- NYTT
      width:     windowWidthCm,
      height:    windowHeightCm,
      count,
      openings:  [...openings],
      hinges:    [...hinges],
      openSizes: [...openSizes],
      material:  window.selectedMaterial,
      glassType: window.selectedGlassType,
      glassTypeValue: window.selectedGlassTypeValue,
      insideColor: window.selectedInsideColor || 'white',
      outsideColor: window.selectedOutsideColor || 'white',
      sprojsType: window.selectedSprojsType || 'none',
      handle: window.selectedHandle || 'stormkrok',
      mosquitoNet: window.selectedMosquitoNet || false,
      customSprojsPerLuft: window.selectedSprojsType === 'custom' && window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0 ?
        [window.customSprojsPerLuft[0]] : undefined,
      savedAt:   new Date().toISOString()
    });
    localStorage.setItem('myDesigns', JSON.stringify(arr));
    alert(`Design sparad som “${name}” (Antal: ${quantity})`);
  }

  renderSavedList();
});

  // ─── Lista sparade designs ───────────────────────────
  function renderSavedList() {
    // Använd myDesigns istället för myWindows för att lagra både fönster och dörrar
    const raw       = JSON.parse(localStorage.getItem('myDesigns') || '[]');
    const container = document.getElementById('savedList');
    const detail    = document.getElementById('savedDetail');
    const term      = document.getElementById('searchSaved').value.toLowerCase();
    const order     = document.getElementById('sortDate').dataset.order || 'desc';

    // Rensa lista & detaljvy
    container.innerHTML = '';
    detail.innerHTML = '<p class="placeholder-text">Välj en design för att se detaljer</p>';
    detail.classList.add('hidden');

    if (!raw.length) {
      container.innerHTML = '<p class="placeholder-text">Inga sparade designs</p>';
      return;
    }

    // Filtrera och sortera
    const list = raw
      .map((c, i) => ({ cfg: c, idx: i }))
      .filter(o => o.cfg.name.toLowerCase().includes(term))
      .sort((a, b) => {
        const da = new Date(a.cfg.savedAt), db = new Date(b.cfg.savedAt);
        return order === 'desc' ? db - da : da - db;
      });

    // Skapa kort
    list.forEach(o => {
      const card = document.createElement('div');
      card.className = 'saved-card';

      // Lägg till typ-indikator (Fönster eller Dörr)
      const typePrefix = o.cfg.type === 'door' ? 'Dörr: ' : 'Fönster: ';

      card.innerHTML = `
        <div class="card-title">${typePrefix}${o.cfg.name}</div>
        <div class="card-date">${new Date(o.cfg.savedAt).toLocaleDateString('sv-SE')}</div>
      `;
      // Klick på kort
      card.addEventListener('click', () => {
        // 1) markera active
        container.querySelectorAll('.saved-card').forEach(c => c.classList.remove('active'));
        card.classList.add('active');
        // 2) visa detaljvy
        detail.classList.remove('hidden');
        // 3) rendera detaljer
        showSavedDetail(o.cfg, o.idx);
      });
      container.appendChild(card);
    });

    // Auto-preview första kortet
    const firstCard = container.querySelector('.saved-card');
    if (firstCard) {
      firstCard.classList.add('active');
      detail.classList.remove('hidden');
      // hämta första objektet från list, inte raw
      showSavedDetail(list[0].cfg, list[0].idx);
    }
  }

  function showSavedDetail(cfg, idx) {
    const detail = document.getElementById('savedDetail');
    detail.classList.remove('hidden');

    // Lägg till typ-indikator (Fönster eller Dörr)
    const typePrefix = cfg.type === 'door' ? 'Dörr: ' : 'Fönster: ';

    detail.innerHTML = `
      <h3 class="detail-title">${typePrefix}${cfg.name}</h3>
      <div class="preview-container saved-container"></div>
      <div class="detail-actions">
        <button class="btn edit-design-btn">Redigera</button>
        <button class="btn edit-title-btn">Byt titel</button>
        <button class="btn delete-btn">Ta bort</button>
      </div>
      <div class="spec-container saved-spec">
        <h4>Specifikation</h4>
        <table>
          <thead><tr><th>Egenskap</th><th>Värde</th></tr></thead>
          <tbody></tbody>
        </table>
      </div>
    `;

    // ─── Bygg preview ─────────────────────────────────────
    const pc = detail.querySelector('.saved-container');

    // Beräkna lämplig storlek för förhandsvisningen
    const maxWidth = 500; // Maximal bredd i pixlar
    const maxHeight = 400; // Maximal höjd i pixlar

    // Kontrollera att bredd och höjd är giltiga värden
    const width = cfg.width || 100; // Använd standardvärde om bredd saknas
    const height = cfg.height || 200; // Använd standardvärde om höjd saknas

    // Beräkna skalningsfaktor baserat på bredd och höjd
    const widthScale = maxWidth / (width * PIXELS_PER_CM);
    const heightScale = maxHeight / (height * PIXELS_PER_CM);

    // Använd den minsta skalningsfaktorn för att behålla proportionerna
    const scale = Math.min(widthScale, heightScale, 1); // Begränsa till max 1 (ingen förstoring)

    // Skapa en preview-container med samma struktur som i konfiguratorn
    pc.innerHTML = ''; // Rensa tidigare innehåll

    // Skapa preview-element med samma struktur som i konfiguratorn
    const pv = document.createElement('div');
    // Använd exakt samma klassnamn som i konfiguratorn
    pv.className = 'preview';
    // Lägg till door-preview klassen separat för dörrar
    if (cfg.type === 'door') {
      pv.classList.add('door-preview');
    }
    pv.id = 'saved-preview-' + idx;

    // Definiera CSS-variabler för trästruktur direkt i elementet
    const woodColor = 'rgba(50, 50, 50, 0.95)';
    pv.style.setProperty('--wood-color', woodColor);
    pv.style.setProperty('--wood-grain',
      'linear-gradient(90deg, ' +
      'rgba(50, 50, 50, 0.95) 0%, ' +
      'rgba(70, 70, 70, 0.95) 20%, ' +
      'rgba(50, 50, 50, 0.95) 40%, ' +
      'rgba(70, 70, 70, 0.95) 60%, ' +
      'rgba(50, 50, 50, 0.95) 80%, ' +
      'rgba(70, 70, 70, 0.95) 100%)'
    );

    // Lägg till trästruktur i bakgrunden för karm
    pv.style.background = 'rgba(60, 60, 60, 0.95)';
    pv.style.backgroundImage = 'var(--wood-grain)';
    pv.style.backgroundSize = '100% 100%';
    pv.style.border = '10px solid ' + woodColor;

    // Lägg till preview-elementet i containern
    pc.appendChild(pv);

    // Sätt dimensioner med skalning
    if (cfg.type === 'door') {
      // För dörrar, använd skalning baserat på bredd för att visa rätt proportioner
      const doorScale = Math.min(maxWidth / (width * PIXELS_PER_CM), 1);
      const doorWidth = width * PIXELS_PER_CM * doorScale;
      const doorHeight = height * PIXELS_PER_CM * doorScale;

      // Sätt dimensioner på container
      pc.style.width = `${doorWidth}px`;
      pc.style.height = `${doorHeight}px`;

      // Sätt dimensioner på preview-elementet
      pv.style.width = '100%';
      pv.style.height = '100%';

      console.log(`Setting saved door preview dimensions: ${width}x${height} cm, scale: ${doorScale}, pixels: ${doorWidth}x${doorHeight}px`);

      // Använd samma renderingslogik som i dörrkonfiguratorn
      // Skapa dörrar baserat på antal luft
      for (let i = 0; i < cfg.count; i++) {
        const doorFrame = document.createElement('div');
        doorFrame.className = 'door-frame';
        doorFrame.style.flex = `${cfg.openSizes[i]}`;

        // Sätt dörrramens stil direkt
        doorFrame.style.display = 'flex';
        doorFrame.style.flexDirection = 'column';
        doorFrame.style.position = 'relative';
        doorFrame.style.background = 'rgba(65, 65, 65, 0.95)';
        doorFrame.style.boxShadow = 'inset 0 0 10px rgba(0, 0, 0, 0.2)';
        doorFrame.style.padding = '0';
        doorFrame.style.margin = '0';
        doorFrame.style.borderRight = '8px solid ' + woodColor;
        doorFrame.style.borderTop = '8px solid ' + woodColor;
        doorFrame.style.borderBottom = '8px solid ' + woodColor;
        doorFrame.style.borderLeft = '8px solid ' + woodColor;
        doorFrame.style.minWidth = '60px';
        doorFrame.style.overflow = 'visible';
        doorFrame.style.height = '100%';

        // Lägg till trästruktur i dörramen för bättre visuell effekt
        doorFrame.style.backgroundImage = 'var(--wood-grain)';
        doorFrame.style.backgroundSize = '100% 100%';

        // Skapa glasdel
        const glassPane = document.createElement('div');
        glassPane.className = 'door-glass-pane';

        // Sätt glasrutans stil direkt
        glassPane.style.flex = '1';
        glassPane.style.background = 'rgba(150, 170, 190, 0.2)';
        glassPane.style.border = '2px solid ' + woodColor;
        glassPane.style.boxShadow = 'inset 0 0 20px rgba(200, 200, 220, 0.1), inset 0 0 40px rgba(255, 255, 255, 0.05)';
        glassPane.style.position = 'relative';
        glassPane.style.overflow = 'hidden';
        glassPane.style.margin = '8px';

        // Justera glasets höjd baserat på panelhöjd
        const glassHeight = cfg.height - cfg.panelHeight;
        glassPane.style.height = `${(glassHeight / cfg.height) * 100}%`;

        // Lägg till glaset i dörren
        doorFrame.appendChild(glassPane);

        // Skapa panel om panelhöjden är större än 0
        if (cfg.panelHeight > 0) {
          const panel = document.createElement('div');
          panel.className = 'door-panel';

          // Sätt panelens stil direkt
          panel.style.background = woodColor;
          panel.style.borderTop = '2px solid rgba(70, 70, 70, 0.95)';
          panel.style.boxShadow = 'inset 0 2px 5px rgba(0, 0, 0, 0.3), inset 0 -2px 5px rgba(255, 255, 255, 0.1)';
          panel.style.position = 'relative';
          panel.style.height = `${(cfg.panelHeight / cfg.height) * 100}%`;

          // Lägg till trästruktur i panelen för bättre visuell effekt
          panel.style.backgroundImage = 'var(--wood-grain)';
          panel.style.backgroundSize = '100% 100%';

          doorFrame.appendChild(panel);
        }

        // Lägg till handtag om dörren är öppningsbar
        if (cfg.openings[i] !== 'fixed') {
          const handle = document.createElement('div');
          handle.className = 'door-handle';

          // Sätt handtagets grundstil direkt
          handle.style.position = 'absolute';
          handle.style.width = '16px';
          handle.style.height = '60px';
          handle.style.background = 'linear-gradient(180deg, #a0a0a0 0%, #808080 50%, #a0a0a0 100%)';
          handle.style.borderRadius = '4px';
          handle.style.boxShadow = 'inset 0 2px 6px rgba(255, 255, 255, 0.4), inset 0 -2px 6px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.5)';
          handle.style.top = '50%';
          handle.style.transform = 'translateY(-50%)';
          handle.style.zIndex = '5';

          // Lägg till metallisk effekt
          handle.style.backgroundImage = 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.3) 60%, rgba(255, 255, 255, 0) 100%)';

          // Hantera olika öppningstyper
          if (cfg.openings[i] === 'side-left') {
            handle.classList.add('handle-right');
            handle.style.right = '15px';
          } else if (cfg.openings[i] === 'side-right') {
            handle.classList.add('handle-left');
            handle.style.left = '15px';
          } else if (cfg.openings[i].startsWith('sliding')) {
            // Speciell hantering för skjutparti
            handle.classList.add('handle-sliding');
            handle.style.width = '20px';
            handle.style.height = '40px';
            handle.style.borderRadius = '10px';

            // Hantera olika skjutriktningar
            if (cfg.openings[i] === 'sliding' || cfg.openings[i] === 'sliding-right') {
              // Från höger (standard)
              handle.classList.add('sliding-right');
              handle.style.right = '15px';
              doorFrame.classList.add('sliding-door', 'sliding-right');
            } else if (cfg.openings[i] === 'sliding-left') {
              // Från vänster
              handle.classList.add('sliding-left');
              handle.style.left = '15px';
              doorFrame.classList.add('sliding-door', 'sliding-left');
            }
          }

          doorFrame.appendChild(handle);
        }

        // Lägg till dörren i preview
        pv.appendChild(doorFrame);
      }

      // Lägg till spröjs om det finns flera lufter
      if (cfg.count > 1) {
        const doorFrames = pv.querySelectorAll('.door-frame');

        // Lägg till vertikala linjer mellan lufterna
        for (let i = 0; i < cfg.count - 1; i++) {
          const sproj = document.createElement('div');
          sproj.className = 'door-sproj';

          // Sätt spröjsens stil direkt
          sproj.style.width = '8px';
          sproj.style.background = woodColor;
          sproj.style.zIndex = '2';

          // Lägg till trästruktur i spröjsen för bättre visuell effekt
          sproj.style.backgroundImage = 'var(--wood-grain)';
          sproj.style.backgroundSize = '100% 100%';

          pv.insertBefore(sproj, doorFrames[i + 1]);
        }
      }

      // Rendera öppningslinjer om DoorPreviewRenderer finns
      if (window.DoorPreviewRenderer && window.DoorPreviewRenderer.renderOpeningLines) {
        // Lägg till en fördröjning för att säkerställa att DOM-elementen är helt renderade
        setTimeout(() => {
          try {
            console.log('Rendering door opening lines for saved preview:', 'saved-preview-' + idx, cfg.openings);
            window.DoorPreviewRenderer.renderOpeningLines('saved-preview-' + idx, cfg.openings);
          } catch (error) {
            console.error('Error rendering door opening lines:', error);
          }
        }, 300);
      }
    } else {
      // För fönster, använd skalning som tidigare
      pc.style.width = `${width * PIXELS_PER_CM * scale}px`;
      pc.style.height = `${height * PIXELS_PER_CM * scale}px`;

      console.log(`Setting saved window preview dimensions: ${width}x${height} cm, scale: ${scale}, pixels: ${width * PIXELS_PER_CM * scale}x${height * PIXELS_PER_CM * scale}px`);

      // Använd samma renderingslogik som i fönsterkonfiguratorn
      if (window.PreviewRenderer && window.PreviewRenderer.renderPreview) {
        // Skapa spröjs-konfiguration för alla lufter
        const sprojsType = cfg.sprojsType || 'none';
        let customSprojsPerLuft = null;

        if (sprojsType === 'custom' && cfg.customSprojsPerLuft && cfg.customSprojsPerLuft.length > 0) {
          customSprojsPerLuft = cfg.customSprojsPerLuft;
        }

        // Rendera hela preview med alla ramar på en gång
        window.PreviewRenderer.renderPreview({
          count: cfg.count,
          openSizes: cfg.openSizes,
          openings: cfg.openings,
          hinges: cfg.hinges,
          sprojsType: sprojsType,
          sprojsPerLuft: Array(cfg.count).fill(sprojsType),
          customSprojsPerLuft: customSprojsPerLuft
        }, pv);
      } else {
        // Fallback till enkel rendering om PreviewRenderer inte är tillgänglig
        console.warn('PreviewRenderer not available for saved designs, using fallback');

        // Rendera varje fönsterram manuellt
        cfg.openSizes.forEach((size, i) => {
          const frame = document.createElement('div');
          frame.className = 'window-frame';
          frame.style.flex = `${size} 0 0`;

          const pane = document.createElement('div');
          pane.className = 'pane';
          frame.appendChild(pane);

          pv.appendChild(frame);
        });
      }
    }

    // ─── Fyll spec-tabell ───────────────────────────────────
    const tb = detail.querySelector('tbody');
    tb.innerHTML = '';

    // Kontrollera om det är en dörr eller ett fönster
    if (cfg.type === 'door') {
      // Dörrspecifikationer

      // Antal dörrar
      if (cfg.quantity !== undefined) {
        const trQ = document.createElement('tr');
        trQ.innerHTML = `<td>Antal dörrar</td><td>${cfg.quantity}</td>`;
        tb.appendChild(trQ);
      }

      // Basrader för dörr
      [
        ['Karmbredd', cfg.width ? `${cfg.width} cm` : 'Ej angiven'],
        ['Karmhöjd', cfg.height ? `${cfg.height} cm` : 'Ej angiven']
      ].forEach(([k, v]) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>${k}</td><td>${v}</td>`;
        tb.appendChild(tr);
      });

      // Panelhöjd (om det finns)
      if (cfg.panelHeight !== undefined) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>Panelhöjd</td><td>${cfg.panelHeight} cm</td>`;
        tb.appendChild(tr);
      }

      // Öppningstyp
      if (cfg.openings && cfg.openings.length > 0) {
        const tr = document.createElement('tr');

        // Översätt öppningstyp till läsbar text
        let openingText = '';
        switch (cfg.openings[0]) {
          case 'fixed':
            openingText = 'Fast';
            break;
          case 'side-left':
            openingText = 'Sidohängd (vänster)';
            break;
          case 'side-right':
            openingText = 'Sidohängd (höger)';
            break;
          case 'sliding-left':
            openingText = 'Skjutdörr (vänster)';
            break;
          case 'sliding-right':
            openingText = 'Skjutdörr (höger)';
            break;
          default:
            openingText = cfg.openings[0];
        }

        tr.innerHTML = `<td>Öppningstyp</td><td>${openingText}</td>`;
        tb.appendChild(tr);
      }

      // Material
      if (cfg.material) {
        const tr = document.createElement('tr');

        // Översätt material till läsbar text
        let materialText = '';
        switch (cfg.material) {
          case 'tra':
            materialText = 'Trä';
            break;
          case 'tra-aluminium':
            materialText = 'Trä/Aluminium';
            break;
          case 'pvc':
            materialText = 'PVC';
            break;
          case 'pvc-aluminium':
            materialText = 'PVC/Aluminium';
            break;
          default:
            materialText = cfg.material;
        }

        tr.innerHTML = `<td>Material</td><td>${materialText}</td>`;
        tb.appendChild(tr);
      }

      // Glastyp
      if (cfg.glassType) {
        const tr = document.createElement('tr');

        // Översätt glastyp till läsbar text
        let glassText = '';
        switch (cfg.glassType) {
          case 'double':
            glassText = '2-glas';
            break;
          case 'triple':
            glassText = '3-glas';
            break;
          default:
            glassText = cfg.glassType;
        }

        tr.innerHTML = `<td>Glastyp</td><td>${glassText}</td>`;
        tb.appendChild(tr);
      }

      // Färg insida
      if (cfg.insideColor) {
        const tr = document.createElement('tr');

        // Översätt färg till läsbar text
        let colorText = '';
        switch (cfg.insideColor) {
          case 'white':
            colorText = 'Vit';
            break;
          case 'gray':
            colorText = 'Grå';
            break;
          case 'black':
            colorText = 'Svart';
            break;
          case 'brown':
            colorText = 'Brun';
            break;
          default:
            colorText = cfg.insideColor;
        }

        tr.innerHTML = `<td>Färg insida</td><td>${colorText}</td>`;
        tb.appendChild(tr);
      }

      // Färg utsida (om det finns och är annorlunda)
      if (cfg.outsideColor && cfg.outsideColor !== cfg.insideColor) {
        const tr = document.createElement('tr');

        // Översätt färg till läsbar text
        let colorText = '';
        switch (cfg.outsideColor) {
          case 'white':
            colorText = 'Vit';
            break;
          case 'gray':
            colorText = 'Grå';
            break;
          case 'black':
            colorText = 'Svart';
            break;
          case 'brown':
            colorText = 'Brun';
            break;
          default:
            colorText = cfg.outsideColor;
        }

        tr.innerHTML = `<td>Färg utsida</td><td>${colorText}</td>`;
        tb.appendChild(tr);
      }
    } else {
      // Fönsterspecifikationer

      // Antal fönster
      if (cfg.quantity !== undefined) {
        const trQ = document.createElement('tr');
        trQ.innerHTML = `<td>Antal fönster</td><td>${cfg.quantity}</td>`;
        tb.appendChild(trQ);
      }

      // Basrader
      [['Karmbredd', `${cfg.width} cm`],
       ['Karmhöjd',  `${cfg.height} cm`],
       ['Antal luft', cfg.count]
      ].forEach(([k,v]) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>${k}</td><td>${v}</td>`;
        tb.appendChild(tr);
      });

      // Öppningsrader
      cfg.openSizes.forEach((w,i) => {
        const op = cfg.openings[i];
        const hg = (op==='side-hung' && cfg.hinges[i]) ? ` (${cfg.hinges[i]})` : '';
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>Luft ${i+1}</td><td>${op} ${w} cm${hg}</td>`;
        tb.appendChild(tr);
      });

      // Materialrad
      if (cfg.material) {
        const tr = document.createElement('tr');
        tr.innerHTML = `<td>Material</td><td>${MATERIAL_LABELS[cfg.material]}</td>`;
        tb.appendChild(tr);
      }

      // Antal glas-rad
      if (cfg.glassType && window.GLASS_LABELS) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'glassType');
        tr.innerHTML = `<td>Antal glas</td><td>${window.GLASS_LABELS[cfg.glassType] || cfg.glassType}</td>`;
        tb.appendChild(tr);
      }

      // Glastyp-rad
      if (cfg.glassTypeValue && window.GLASS_TYPE_LABELS) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'glassTypeValue');
        tr.innerHTML = `<td>Glastyp</td><td>${window.GLASS_TYPE_LABELS[cfg.glassTypeValue] || cfg.glassTypeValue}</td>`;
        tb.appendChild(tr);
      }

      // Färg insida
      if (cfg.insideColor && window.COLOR_LABELS) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'insideColor');
        tr.innerHTML = `<td>Färg insida</td><td>${window.COLOR_LABELS[cfg.insideColor] || cfg.insideColor}</td>`;
        tb.appendChild(tr);
      }

      // Färg utsida (om det finns)
      if (cfg.outsideColor && window.COLOR_LABELS && cfg.outsideColor !== cfg.insideColor) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'outsideColor');
        tr.innerHTML = `<td>Färg utsida</td><td>${window.COLOR_LABELS[cfg.outsideColor] || cfg.outsideColor}</td>`;
        tb.appendChild(tr);
      }

      // Spröjs (om det finns)
      if (cfg.sprojsType && cfg.sprojsType !== 'none' && window.SPROJS_LABELS) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'sprojs');

        if (cfg.sprojsType === 'custom' && cfg.customSprojsPerLuft && cfg.customSprojsPerLuft.length > 0) {
          const settings = cfg.customSprojsPerLuft[0];
          tr.innerHTML = `<td>Spröjs</td><td>Anpassad (${settings.horizontal} horisontella, ${settings.vertical} vertikala)</td>`;
        } else {
          tr.innerHTML = `<td>Spröjs</td><td>${window.SPROJS_LABELS[cfg.sprojsType] || cfg.sprojsType}</td>`;
        }

        tb.appendChild(tr);
      }

      // Handtag (om det finns)
      if (window.HANDLE_LABELS) {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'handle');

        if (cfg.handle && cfg.handle !== 'none') {
          tr.innerHTML = `<td>Handtag</td><td>${window.HANDLE_LABELS[cfg.handle] || cfg.handle}</td>`;
          console.log(`Visar handtag i sparad design: ${window.HANDLE_LABELS[cfg.handle] || cfg.handle}`);
        } else {
          // Om inget handtag är valt, använd standardvärdet
          const defaultHandle = 'stormkrok';
          tr.innerHTML = `<td>Handtag</td><td>${window.HANDLE_LABELS[defaultHandle] || defaultHandle}</td>`;
          console.log(`Inget handtag i sparad design, visar standard: ${window.HANDLE_LABELS[defaultHandle]}`);
        }

        tb.appendChild(tr);
      }

      // Myggnät (om det finns)
      if (window.MOSQUITO_NET_LABELS) {
        // Kontrollera om alla fönster är fasta
        const allFixed = cfg.openings && cfg.openings.every(type => type === 'fixed');

        // Visa myggnät-information endast om det inte är ett fast fönster eller om myggnät är valt
        if (!allFixed || cfg.mosquitoNet) {
          const tr = document.createElement('tr');
          tr.setAttribute('data-key', 'mosquitoNet');

          // Använd värdet från konfigurationen eller standardvärdet (false)
          const mosquitoNetValue = cfg.mosquitoNet !== undefined ? cfg.mosquitoNet : false;
          tr.innerHTML = `<td>Myggnät</td><td>${window.MOSQUITO_NET_LABELS[mosquitoNetValue.toString()]}</td>`;

          tb.appendChild(tr);
        }
      }
    }

    // ─── Handlers för knappar ───────────────────────────────
    detail.querySelector('.edit-design-btn').onclick = () => {
      // Kontrollera om det är en dörr eller ett fönster
      if (cfg.type === 'door') {
        // Redigera dörrdesign

        // Visa dörrkonfiguratorn
        document.getElementById('navNewDesign').click();
        document.getElementById('configuratorSelection').classList.add('hidden');
        document.getElementById('windowConfigurator').classList.add('hidden');
        document.getElementById('doorConfigurator').classList.remove('hidden');

        // Ladda in dörrkonfigurationen med index
        if (typeof window.loadDoorDesign === 'function') {
          window.loadDoorDesign(cfg, idx);
        } else {
          alert('Kunde inte ladda dörrdesignen. Funktionen loadDoorDesign saknas.');
        }
      } else {
        // Redigera fönsterdesign

        // Ladda konfigurationen
        loadConfiguration(cfg, idx);

        // Klicka på "Ny Design" för att visa sektionen
        document.getElementById('navNewDesign').click();

        // Visa fönsterkonfiguratorn direkt istället för valsidan
        const configuratorSelection = document.getElementById('configuratorSelection');
        const windowConfigurator = document.getElementById('windowConfigurator');

        if (configuratorSelection && windowConfigurator) {
          configuratorSelection.classList.add('hidden');
          windowConfigurator.classList.remove('hidden');
        }
      }
    };
    detail.querySelector('.edit-title-btn').onclick = () => {
      const newName = prompt('Ange nytt namn för design:', cfg.name);
      if (!newName) return;
      const data = JSON.parse(localStorage.getItem('myDesigns')||'[]');
      data[idx].name = newName;
      localStorage.setItem('myDesigns', JSON.stringify(data));
      renderSavedList();
      showSavedDetail(data[idx], idx);
    };
    detail.querySelector('.delete-btn').onclick = () => {
      if (!confirm(`Ta bort "${cfg.name}"?`)) return;
      const data = JSON.parse(localStorage.getItem('myDesigns')||'[]');
      data.splice(idx,1);
      localStorage.setItem('myDesigns', JSON.stringify(data));
      renderSavedList();
    };
  }


// ─── Läs in sparad design ───────────────────────────
function loadConfiguration(cfg, idx) {
  // ─── Återställ grundläggande fält ─────────────────────
  windowWidthCm   = cfg.width;
  windowHeightCm  = cfg.height;
  count           = cfg.count;
  openings        = [...cfg.openings];
  hinges          = [...cfg.hinges];
  openSizes       = [...cfg.openSizes];
  editingIndex    = idx;

  // Återställ färgval om de finns
  if (cfg.insideColor) window.selectedInsideColor = cfg.insideColor;
  if (cfg.outsideColor) window.selectedOutsideColor = cfg.outsideColor;

  // Återställ handtagsval om det finns
  if (cfg.handle) {
    window.selectedHandle = cfg.handle;
    console.log('Återställer handtagsval från sparad design:', cfg.handle);

    // Uppdatera UI för handtag
    setTimeout(() => {
      document.querySelectorAll('.handle-option').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.value === cfg.handle);
      });

      // Uppdatera specifikationstabellen med handtagsinformation
      if (typeof window.renderHandleSpec === 'function') {
        window.renderHandleSpec();
      }

      console.log('Handtagsval uppdaterat i UI');
    }, 500);
  }

  // uppdatera input-fält och etiketter
  widthInput.value       = windowWidthCm;
  heightInput.value      = windowHeightCm;
  widthLabel.textContent = `${windowWidthCm} cm`;
  heightLabel.textContent= `${windowHeightCm} cm`;

  // ─── Återställ “Antal luft” ───────────────────────────
  qtyBtns.forEach(b => {
    const isCustom = b.dataset.value === 'custom';
    const matches = +b.dataset.value === count;
    b.classList.toggle('active', matches || (isCustom && !['1','2','3'].includes(count.toString())));
  });
  if (!['1','2','3'].includes(count.toString())) {
    customCtrl.classList.remove('hidden');
    customIn.value = count;
  } else {
    customCtrl.classList.add('hidden');
  }

  // ─── Återställ Material-knappar ───────────────────────
  window.selectedMaterial = cfg.material;
  document.querySelectorAll('.material-option-btn').forEach(btn => {
    btn.classList.toggle('active', btn.dataset.value === cfg.material);
  });
  if (typeof renderMaterialSpec === 'function') renderMaterialSpec();

  // ─── Återställ Glastyp-knappar ───────────────────────
  window.selectedGlassType = cfg.glassType;
  document.querySelectorAll('.glass-option-btn').forEach(btn => {
    btn.classList.toggle('active', btn.dataset.value === cfg.glassType);
  });
  if (typeof renderGlassTypeSpec === 'function') renderGlassTypeSpec();

  // ─── Återställ Glastyp-val ───────────────────────
  window.selectedGlassTypeValue = cfg.glassTypeValue;
  document.querySelectorAll('.glass-type-option').forEach(btn => {
    btn.classList.toggle('active', btn.dataset.value === cfg.glassTypeValue);
  });
  if (typeof window.renderGlassTypeValueSpec === 'function') window.renderGlassTypeValueSpec();

  // ─── Återställ Spröjs-val ───────────────────────
  window.selectedSprojsType = cfg.sprojsType || 'none';

  // Uppdatera UI för spröjs
  document.querySelectorAll('.sprojs-option').forEach(btn => {
    btn.classList.toggle('active', btn.dataset.value === cfg.sprojsType);
  });

  // Visa/dölj anpassade spröjs-inställningar
  const customSprojsSettings = document.getElementById('customSprojsSettings');
  if (customSprojsSettings) {
    customSprojsSettings.style.display = cfg.sprojsType === 'custom' ? 'block' : 'none';
  }

  // Hantera anpassade spröjs
  if (cfg.customSprojsPerLuft && cfg.customSprojsPerLuft.length > 0) {
    window.customSprojsPerLuft = Array(count).fill(cfg.customSprojsPerLuft[0]);
    window.horizontalSprojsCount = cfg.customSprojsPerLuft[0].horizontal || 2;
    window.verticalSprojsCount = cfg.customSprojsPerLuft[0].vertical || 2;

    // Uppdatera inmatningsfält för anpassade spröjs
    const horizontalSprojsCountInput = document.getElementById('horizontalSprojsCount');
    const verticalSprojsCountInput = document.getElementById('verticalSprojsCount');

    if (horizontalSprojsCountInput) {
      horizontalSprojsCountInput.value = window.horizontalSprojsCount;
    }

    if (verticalSprojsCountInput) {
      verticalSprojsCountInput.value = window.verticalSprojsCount;
    }

    // Uppdatera förhandsvisningen av anpassade spröjs
    if (typeof window.updateCustomSprojsPreview === 'function') {
      window.updateCustomSprojsPreview();
    }
  }

  // Uppdatera specifikationstabellen
  if (typeof window.updateSprojsSpec === 'function') {
    window.updateSprojsSpec();
  }

  // Uppdatera 2D-modellen
  if (typeof window.updateSprojsIn2DModel === 'function') {
    window.updateSprojsIn2DModel();
  }

  // Uppdatera 3D-modellen
  if (typeof window.updateSprojsInModel === 'function') {
    window.updateSprojsInModel();
  }

  // ─── Återställ Handtagsval ───────────────────────────
  // Uppdatera UI för handtag igen efter att DOM är helt uppdaterad
  setTimeout(() => {
    if (cfg.handle) {
      document.querySelectorAll('.handle-option').forEach(btn => {
        btn.classList.toggle('active', btn.dataset.value === cfg.handle);
      });

      // Uppdatera specifikationstabellen med handtagsinformation
      if (typeof window.renderHandleSpec === 'function') {
        window.renderHandleSpec();
      }
    }

    // ─── Återställ Myggnät-val ───────────────────────────
    if (cfg.mosquitoNet !== undefined) {
      console.log('Återställer myggnätsval från sparad design:', cfg.mosquitoNet);
      window.selectedMosquitoNet = cfg.mosquitoNet;

      // Uppdatera UI för myggnät
      const noMosquitoNetBtn = document.getElementById('noMosquitoNet');
      const withMosquitoNetBtn = document.getElementById('withMosquitoNet');

      if (noMosquitoNetBtn && withMosquitoNetBtn) {
        // Kontrollera om alla fönster är fasta
        const allFixed = window.openings && window.openings.every(type => type === 'fixed');

        // Inaktivera "Med myggnät"-knappen om alla fönster är fasta
        if (allFixed) {
          withMosquitoNetBtn.disabled = true;
          withMosquitoNetBtn.style.opacity = '0.5';
          withMosquitoNetBtn.style.cursor = 'not-allowed';

          // Tvinga "Utan myggnät" om alla fönster är fasta
          noMosquitoNetBtn.classList.add('active');
          withMosquitoNetBtn.classList.remove('active');
          window.selectedMosquitoNet = false;
        } else {
          // Aktivera "Med myggnät"-knappen
          withMosquitoNetBtn.disabled = false;
          withMosquitoNetBtn.style.opacity = '';
          withMosquitoNetBtn.style.cursor = '';

          // Sätt korrekt val baserat på konfigurationen
          if (cfg.mosquitoNet) {
            withMosquitoNetBtn.classList.add('active');
            noMosquitoNetBtn.classList.remove('active');
          } else {
            noMosquitoNetBtn.classList.add('active');
            withMosquitoNetBtn.classList.remove('active');
          }
        }
      }

      // Uppdatera specifikationstabellen med myggnät-information
      if (typeof window.updateMosquitoNetSpec === 'function') {
        console.log('Anropar updateMosquitoNetSpec för att uppdatera specifikationstabellen');
        window.updateMosquitoNetSpec();
      } else {
        console.warn('updateMosquitoNetSpec-funktionen är inte tillgänglig');
      }
    }
  }, 200);

  // ─── Återuppbygg Öppning-per-luft-kontroller ──────────
  buildOpeningControls();

  // ─── Rendera preview & spec med allt nytt ─────────────
  updateAll();
}


  // ─── INIT ───────────────────────────────────────────
  // Återställ till standardvärden vid varje sidladdning
  count = 1;
  openings = ['fixed']; // Sidohängt som standard
  hinges = ['null']; // Vänsterhängd som standard
  openSizes = [windowWidthCm];

  // Återställ aktiva knappar för antal luft
  qtyBtns.forEach(b => {
    b.classList.toggle('active', b.dataset.value === '1');
  });
  customCtrl.classList.add('hidden');

  // Säkerställ att preview renderas korrekt
  setTimeout(() => {
    goTo(0);
    updateAll();

    // Forcera en extra rendering för att säkerställa att preview visas
    if (window.PreviewRenderer && window.PreviewRenderer.renderPreview) {
      window.PreviewRenderer.updateDimensions(windowWidthCm, windowHeightCm);

      // Skapa spröjs-konfiguration för alla lufter
      const sprojsType = window.selectedSprojsType || 'none';
      let customSprojsPerLuft = null;

      if (sprojsType === 'custom' && window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0) {
        customSprojsPerLuft = window.customSprojsPerLuft;
      }

      window.PreviewRenderer.renderPreview({
        count,
        openSizes,
        openings,
        hinges,
        sprojsType: sprojsType,
        sprojsPerLuft: Array(count).fill(sprojsType),
        customSprojsPerLuft: customSprojsPerLuft
      });
    }
  }, 100); // Kort fördröjning för att säkerställa att DOM är helt laddad

  // Definiera updateAll-funktionen
  function updateAll() {
    updateDimensions();
    updateSpecTable();
    renderPreview();
  }

  // Exponera funktioner globalt så att de kan anropas från andra skript
  window.updateAll = updateAll;
  window.goTo = goTo;
});
