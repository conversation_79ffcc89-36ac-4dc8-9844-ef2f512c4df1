:root {
    /* <PERSON><PERSON><PERSON><PERSON><PERSON> enligt <PERSON> */
    --color-bg: #121212;
    --color-panel: #1e1e1e;
    --color-primary: #f5c700;
    --color-primary-dark: #d4b300;
    --color-secondary: #e0e0e0;
    --color-border: #2a2a2a;
    --radius: 6px;
    --transition: 0.2s ease-in-out;
    --font-base: 'Segoe UI', sans-serif;
    --header-height: 0px;    /* skrivs över av JS */
  }
  /* Ta bort spinner-pilar på number-input */
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
  }
  /* Reset */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  /* Body & header */
  body {
    font-family: var(--font-base);
    background: var(--color-bg);
    color: var(--color-secondary);
    line-height: 1.5;
  }
  header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: var(--color-panel);
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  }
  header h1 {
    font-size: 1.75rem;
    color: var(--color-primary);
  }

  /* Mina fönster-knapp */
  .menu-btn {
    position: absolute;
    left: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: var(--color-secondary);
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    transition: background var(--transition);
  }
  .menu-btn:hover {
    background: rgba(245,199,0,0.1);
  }
  .hamburger-icon {
    font-size: 1.5rem;
    line-height: 1;
  }
  .menu-label {
    font-size: 1rem;
    color: var(--color-secondary);
  }

  /* Steg-navbar */
  .step-nav-wrapper {
    text-align: center;
    margin-bottom: 1rem;
  }
  .nav-title {
    font-size: 1rem;
    color: var(--color-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
  }
  .step-nav {
    background: var(--color-panel);
    padding: 0.5rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  }
  .step-nav ul {
    display: flex;
    justify-content: center;
    gap: 1rem;
    list-style: none;
  }
  .step-nav .step {
    width: 40px;
    height: 40px;
    background: var(--color-bg);
    border: 2px solid var(--color-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: var(--color-secondary);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition), transform var(--transition);
  }
  .step-nav .step:hover {
    transform: translateY(-2px);
    border-color: var(--color-primary);
  }
  .step-nav .step.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
  }

  /* Huvudlayout: två kolumner */
  .builder-container {
    display: grid;
    grid-template-columns: 1fr 360px;
    gap: 1rem;
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 1rem;
  }
  .controls-column,
  .result-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Sektioner */
  .builder-section {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    overflow: hidden;
  }
  .builder-section h2 {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
    background: #262626;
    color: var(--color-secondary);
  }
  .section-body {
    padding: 1rem;
  }
  .builder-section.collapsed .section-body {
    display: none;
  }

  /* Dimension Section */
  .dimension-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  .dim-row {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  .dim-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.95rem;
    color: var(--color-secondary);
  }
  .dim-value {
    background: var(--color-panel);
    padding: 0.1rem 0.5rem;
    border-radius: var(--radius);
    font-weight: 600;
    color: var(--color-primary);
  }
  .dim-input {
    width: 100%;
    padding: 0.6rem 0.75rem;
    font-size: 1rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-bg);
    color: var(--color-secondary);
    transition: border-color var(--transition);
  }
  .dim-input:focus {
    outline: none;
    border-color: var(--color-primary);
  }
  .dim-hint {
    font-size: 0.8rem;
    color: #888;
  }
  .dim-guide {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 0.75rem;
  }
  .guide-icon {
    width: 24px;
    height: 24px;
    color: var(--color-primary);
  }
  .guide-text {
    flex: 1;
    font-size: 0.85rem;
    color: var(--color-secondary);
    line-height: 1.3;
  }
  .guide-btn {
    margin-left: auto;
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    background: var(--color-primary);
    color: #121212;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition);
  }
  .guide-btn:hover {
    background: var(--color-primary-dark);
  }

  /* Antal luft – knappgrupp */
  .quantity-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  .qty-option-btn {
    flex: 1;
    padding: 0.75rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    font-size: 1rem;
    color: var(--color-secondary);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition), transform var(--transition);
  }
  .qty-option-btn:hover {
    background: #1a1a1a;
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }
  .qty-option-btn.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
  }

  /* Custom-control för “Anpassad” */
  .custom-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  .qty-btn {
    width: 36px;
    height: 36px;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    font-size: 1.25rem;
    color: var(--color-secondary);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition), transform var(--transition);
  }
  .qty-btn:hover {
    background: #1a1a1a;
    border-color: var(--color-primary);
    transform: translateY(-1px);
  }
  .custom-input {
    width: 60px;
    padding: 0.5rem;
    font-size: 1rem;
    text-align: center;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-panel);
    color: var(--color-secondary);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.5);
  }

  /* Öppningstyper – grundversion */
  .opening-wrap {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-bg);
  }
  .opening-wrap h3 {
    margin-bottom: 0.5rem;
    color: var(--color-secondary);
  }
  .opening-wrap select {
    width: 100%;
    padding: 0.5rem 1rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-panel);
    color: var(--color-secondary);
  }

  /* Hinge-knappar */
  .hinge-btn-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  .hinge-btn-group.hidden {
    display: none !important;
  }
  .hinge-btn {
    flex: 1;
    padding: 0.5rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-secondary);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
  }
  .hinge-btn:hover {
    background: #1a1a1a;
    border-color: var(--color-primary);
  }
  .hinge-btn.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
  }

  /* Navigering */
  .builder-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
  .next-btn {
    margin-right: 0.5rem;
  }
  .btn {
    padding: 0.6rem 1.2rem;
    background: var(--color-primary);
    color: #121212;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition), transform var(--transition);
  }
  .btn:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
  }
  .btn:disabled {
    background: #444;
    color: #888;
    cursor: not-allowed;
    transform: none;
  }

  /* Preview */
  .preview-container {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.5);
  }
  .preview {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    width: 100%;
    height: 100%;
    background: #2a2a2a;
    border-radius: var(--radius);
    position: relative;
  }
  .window-frame {
    flex: 1;
    margin: 0 4px;
    height: 94%;
    position: relative;
    background: #d3d3d3;
    border: 2px solid #cccccc;
    border-radius: 3px;
    box-shadow:
      inset 0 0 0 1px #ffffff,
      inset 0 0 4px rgba(0,0,0,0.25),
      0 4px 8px rgba(0,0,0,0.3);
  }

  /*  Handtag */
  .handle {
    position: absolute;
    width: 8px;
    height: 40px;
    background: linear-gradient(180deg, #888 0%, #666 50%, #888 100%);
    border-radius: 4px;
    box-shadow:
      inset 0 2px 6px rgba(255,255,255,0.5),
      inset 0 -2px 6px rgba(0,0,0,0.5),
      0 4px 8px rgba(0,0,0,0.7);
    cursor: pointer;
    transition: background var(--transition), transform var(--transition);
    z-index: 2;
  }
  .handle::before {
    content: "";
    position: absolute;
    left: 50%;
    top: 4px;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: radial-gradient(circle at center, #444 0%, #222 60%, #000 100%);
    border-radius: 50%;
  }
  .handle.side-left,
  .handle.side-right {
    top: 50%;
    transform: translateY(-50%);
  }
  .handle.side-left {
    left: 6px;
  }
  .handle.side-right {
    right: 6px;
  }
  .handle.top-center {
    top: 6px;
    left: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
  }
  .handle.bottom-center {
    bottom: 6px;
    left: 50%;
    transform: translate(-50%, 50%) rotate(90deg);
  }

  /* NU TILLFÖRDA RADER: visa SVG-linjerna */
  .frame-svg {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    pointer-events: none;
  }
  .frame-svg line {
    stroke: var(--color-secondary);
    stroke-width: 1;
  }
  .frame-svg line[stroke-dasharray] {
    stroke-dasharray: 4,4;
  }

  /* Specifikationstabell */
  .spec-container {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    margin-top: 20px;
  }
  .spec-container h3 {
    margin-bottom: 1rem;
    color: var(--color-primary);
    font-size: 1.2rem;
    font-weight: 600;
  }
  #specTable {
    width: 100%;
    border-collapse: collapse;
  }
  #specTable th,
  #specTable td {
    border: 1px solid var(--color-border);
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 1rem;
    color: var(--color-secondary);
  }
  #specTable tr:hover td {
    background: rgba(245, 199, 0, 0.05);
  }

  /* ─── Öppning per luft: Typ-knappar & storlekskontroller ───────────────── */

  /* Wrap för varje luft */
  .opening-wrap {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1rem;
    margin-bottom: 1rem;
  }

  /* Titel */
  .opening-wrap h3 {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: var(--color-primary);
  }

  /* Grupp för fönstertyp */
  .type-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }
  .type-label {
    font-size: 0.95rem;
    color: var(--color-secondary);
    white-space: nowrap;
  }
  .type-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
  .type-btn {
    padding: 0.5rem 1rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    color: var(--color-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
  }
  .type-btn:hover {
    border-color: var(--color-primary);
    background: rgba(245,199,0,0.1);
  }
  .type-btn.active {
    background: var(--color-primary);
    border-color: var(--color-primary-dark);
    color: #121212;
  }

  /* Ge varje luft‐ruta lite mer luft och bakgrundsfärg */
  .opening-wrap {
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Avstånd mellan rubrik och kontroller */
  .opening-wrap h3 {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    color: var(--color-primary);
  }

  /* Fönster‐typ‐knappar */
  .type-group {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
  }
  .type-label {
    margin-right: 0.5rem;
    font-weight: 600;
  }
  .type-buttons {
    display: flex;
    gap: 0.5rem;
  }
  .type-btn {
    padding: 0.4rem 0.75rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
  }
  .type-btn.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
  }
  .type-btn:hover {
    background: #1a1a1a;
  }

  /* Storleks‐kontroll */
  .size-control {
    display: flex;
    gap: 0.25rem;    /* tidigare 0.5rem */
    align-items: center;
    margin-bottom: 1rem;
  }
  .size-control label {
    margin-right: 0.5rem;
    font-size: 0.95rem;
  }
  .size-input {
    width: 80px;     /* tidigare 60px */
    padding: 0.3rem 0.5rem;
    font-size: 1rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    background: var(--color-bg);
    color: var(--color-secondary);
    margin-right: 0.25rem;
    text-align: left;
  }
  /* Återstående‐text */
  .size-remaining {
    font-size: 0.95rem;
    color: var(--color-secondary);
    margin-bottom: 1rem;
  }

  /* Gångjärns‐knappar */
  .hinge-btn-group {
    display: flex;
    gap: 0.5rem;
  }
  .size-btn {
    width: 32px;
    height: 32px;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    font-size: 1.25rem;
    color: var(--color-secondary);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
  }
  .size-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
  .size-btn:hover:not(:disabled) {
    background: rgba(245,199,0,0.1);
    border-color: var(--color-primary);
  }
  .hinge-btn {
    flex: 1;
    padding: 0.5rem 0;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
  }
  .hinge-btn.active {
    background: var(--color-primary);
    color: #121212;
    border-color: var(--color-primary-dark);
  }
  .hinge-btn:hover {
    background: #1a1a1a;
  }

  /* Steg 3: Öppning per luft – Förbättrad UI */
  .opening-wrap {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto 1fr auto;
    gap: 1rem;
    background: var(--color-panel);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: transform var(--transition);
  }
  .opening-wrap:hover {
    transform: translateY(-2px);
  }
  .opening-header {
    grid-column: 1 / -1;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-primary);
  }
  .type-group {
    grid-column: 1 / -1;
    display: flex;
    gap: 0.5rem;
  }
  .type-btn {
    flex: 1;
    padding: 0.6rem;
    background: var(--color-bg);
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    font-size: 0.95rem;
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
    text-align: center;
  }
  .type-btn:hover {
    background: rgba(245,199,0, 0.1);
    border-color: var(--color-primary);
  }
  .type-btn.active {
    background: var(--color-primary);
    border-color: var(--color-primary-dark);
    color: #121212;
  }
  .size-control {
    display: flex;
    align-items: center;
    gap: 0.25rem;    /* tidigare 0.5rem */
  }
  .size-control label {
    font-size: 0.95rem;
    color: var(--color-secondary);
    min-width: 80px;
  }
  .size-control input {
    width: 80px;     /* tidigare 60px */
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    text-align: center;
  }
  .size-control .unit {
    font-size: 0.9rem;
    color: var(--color-secondary);
  }
  .size-remaining {
    grid-column: 1 / -1;
    font-size: 0.9rem;
    color: var(--color-secondary);
    text-align: right;
  }
  .hinge-group {
    grid-column: 1 / -1;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    max-height: 0;
    overflow: hidden;
    transition: opacity var(--transition), max-height var(--transition);
  }
  .hinge-group.visible {
    opacity: 1;
    max-height: 3rem;
  }
  .hinge-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--color-border);
    border-radius: var(--radius);
    cursor: pointer;
    transition: background var(--transition), border-color var(--transition);
    text-align: center;
  }
  .hinge-btn:hover {
    background: rgba(245,199,0,0.1);
    border-color: var(--color-primary);
  }
  .hinge-btn.active {
    background: var(--color-primary);
    border-color: var(--color-primary-dark);
    color: #121212;
  }

  /* Responsiva brytpunkter för new-design */
  @media (max-width: 768px) {
    .builder-container {
      display: block;
      padding: 1rem;
    }
    .controls-column,
    .result-column {
      width: 100%;
      margin: 0 0 1.5rem;
    }
    .step-nav-wrapper {
      display: none !important;
    }
    .step-nav ul {
      overflow-x: auto;
      padding: 0 1rem;
    }
    .step-nav .step {
      flex: 0 0 auto;
    }
    .preview-container {
      width: 100% !important;
      box-sizing: border-box;
    }
    .spec-container {
      max-height: 200px;
      overflow-y: auto;
    }
    header {
      position: sticky;
      top: 0;
      z-index: 200;
      box-shadow: 0 2px 4px rgba(0,0,0,0.5);
    }
    .builder-container {
      display: flex;
      flex-direction: column;
      padding-top: 1rem;
    }
  }
