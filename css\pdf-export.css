/**
 * PDF-export.css
 * Stilar specifikt för PDF-export
 */

/* Säkerställ att preview-container har rätt stil */
.preview-container.saved-container {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  box-shadow: none;
}

/* Säkerställ att preview har rätt stil */
.preview.saved-preview {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  background: rgba(60, 60, 60, 0.95);
  position: relative;
  border: 10px solid rgba(50, 50, 50, 0.95);
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Säkerställ att window-frame har rätt stil */
.window-frame {
  flex: 1;
  height: 100%;
  position: relative !important;
  background: rgba(65, 65, 65, 0.95);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
  padding: 0 !important;
  margin: 0 !important;
  border-right: 8px solid rgba(50, 50, 50, 0.95);
  border-top: 8px solid rgba(50, 50, 50, 0.95);
  border-bottom: 8px solid rgba(50, 50, 50, 0.95);
  border-left: 8px solid rgba(50, 50, 50, 0.95);
  box-sizing: border-box !important;
}

/* Sista fönsterbågen ska ha höger-balk för konsekvent karm */
.window-frame:last-child {
  border-right: 8px solid rgba(50, 50, 50, 0.95);
}

/* Första fönsterbågen ska inte ha vänster-balk */
.window-frame:first-child {
  border-left: none;
}

/* Säkerställ att pane har rätt stil */
.pane {
  position: absolute !important;
  top: 8px !important;
  left: 8px !important;
  right: 8px !important;
  bottom: 8px !important;
  width: calc(100% - 16px) !important;
  height: calc(100% - 16px) !important;
  background: rgba(150, 170, 190, 0.1);
  border: 1px solid rgba(100, 100, 100, 0.3);
  box-sizing: border-box !important;
}

/* Säkerställ att SVG har rätt stil */
.frame-svg {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  pointer-events: none;
  z-index: 3;
}

/* Säkerställ att linjer har rätt stil */
.frame-svg line {
  stroke: rgba(180, 180, 180, 0.7);
  stroke-width: 2;
  vector-effect: non-scaling-stroke;
}

/* Säkerställ att spröjs-linjer har rätt stil */
.frame-svg line.sprojs-line {
  stroke: #333 !important;
  stroke-width: 1 !important;
  vector-effect: non-scaling-stroke !important;
}

/* Säkerställ att handtag har rätt stil */
.handle {
  position: absolute;
  width: 12px;
  height: 30px;
  background: linear-gradient(180deg, #909090 0%, #707070 50%, #909090 100%);
  border-radius: 4px;
  z-index: 5;
}

.handle.side-left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.handle.side-right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.handle.top-center {
  top: -6px;
  left: 50%;
  transform: translate(-50%, 0) rotate(90deg);
}

.handle.bottom-center {
  bottom: -6px;
  left: 50%;
  transform: translate(-50%, 0) rotate(90deg);
}
