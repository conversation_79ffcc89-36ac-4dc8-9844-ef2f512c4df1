/**
 * Isofönster Konfigurator - Huvudskript
 * Konsoliderad JavaScript för WordPress Plugin
 */

(function($) {
    'use strict';

    // Huvudklass för konfigurator
    window.IsofonsterConfigurator = {
        
        // Konfiguration
        config: {
            container: '.isofonster-configurator-container',
            type: 'both',
            enableSave: true,
            enableExport: true,
            ajaxUrl: '',
            nonce: ''
        },
        
        // Aktuell state
        state: {
            currentType: 'window', // 'window' eller 'door'
            currentStep: 0,
            windowData: {
                width: 180,
                height: 150,
                count: 1,
                openings: ['fixed'],
                hinges: [null],
                openSizes: [100],
                material: 'wood',
                glassType: 'double',
                glassTypeValue: 'standard',
                insideColor: 'white',
                outsideColor: 'white',
                sprojsType: 'none',
                handle: 'stormkrok',
                mosquitoNet: false,
                customSprojsPerLuft: []
            },
            doorData: {
                width: 90,
                height: 210,
                opening: 'fixed',
                hinge: null,
                material: 'wood',
                glassType: 'double',
                insideColor: 'white',
                outsideColor: 'white',
                handle: 'standard',
                panelHeight: 0
            }
        },
        
        // Initialisera konfigurator
        init: function(options) {
            this.config = $.extend(this.config, options);
            this.bindEvents();
            this.initializeState();
            
            // Sätt AJAX-variabler från WordPress
            if (typeof isofonster_ajax !== 'undefined') {
                this.config.ajaxUrl = isofonster_ajax.ajax_url;
                this.config.nonce = isofonster_ajax.nonce;
            }
            
            console.log('Isofönster Konfigurator initialiserad');
        },
        
        // Bind event handlers
        bindEvents: function() {
            var self = this;
            var $container = $(this.config.container);
            
            // Navigation
            $container.on('click', '.isofonster-top-nav button', function(e) {
                e.preventDefault();
                self.handleNavigation($(this));
            });
            
            // Konfigurator-val
            $container.on('click', '#isofonster-startWindowConfigurator', function(e) {
                e.preventDefault();
                self.showWindowConfigurator();
            });
            
            $container.on('click', '#isofonster-startDoorConfigurator', function(e) {
                e.preventDefault();
                self.showDoorConfigurator();
            });
            
            // Steg-navigation
            $container.on('click', '.isofonster-step', function(e) {
                e.preventDefault();
                var step = parseInt($(this).data('step'));
                self.goToStep(step);
            });
            
            // Sektion headers (toggle)
            $container.on('click', '.isofonster-builder-section h2', function(e) {
                e.preventDefault();
                self.toggleSection($(this).closest('.isofonster-builder-section'));
            });
            
            // Dimensioner
            $container.on('input', '#isofonster-widthInput', function() {
                self.updateDimension('width', $(this).val());
            });
            
            $container.on('input', '#isofonster-heightInput', function() {
                self.updateDimension('height', $(this).val());
            });
            
            // Antal lufter
            $container.on('click', '#isofonster-decBtn', function(e) {
                e.preventDefault();
                self.updateCount(self.state.windowData.count - 1);
            });
            
            $container.on('click', '#isofonster-incBtn', function(e) {
                e.preventDefault();
                self.updateCount(self.state.windowData.count + 1);
            });
            
            $container.on('input', '#isofonster-customIn', function() {
                self.updateCount(parseInt($(this).val()));
            });
            
            // Spara design
            if (this.config.enableSave) {
                $container.on('click', '.isofonster-save-btn', function(e) {
                    e.preventDefault();
                    self.saveDesign();
                });
            }
            
            // Export
            if (this.config.enableExport) {
                $container.on('click', '#isofonster-exportBtn', function(e) {
                    e.preventDefault();
                    self.exportPDF();
                });
            }
        },
        
        // Initialisera state
        initializeState: function() {
            this.updateDimensionLabels();
            this.updatePreview();
            this.updateSpecTable();
        },
        
        // Hantera navigation
        handleNavigation: function($btn) {
            var target = $btn.data('target');
            var $container = $(this.config.container);
            
            // Uppdatera aktiv knapp
            $container.find('.isofonster-top-nav button').removeClass('active');
            $btn.addClass('active');
            
            // Visa rätt sektion
            $container.find('.isofonster-section').addClass('hidden');
            $container.find('#isofonster-' + target).removeClass('hidden');
            
            // Speciell hantering för olika sektioner
            if (target === 'sectionNewDesign') {
                this.showConfiguratorSelection();
            } else if (target === 'sectionSavedDesigns') {
                this.loadSavedDesigns();
            }
        },
        
        // Visa konfigurator-val
        showConfiguratorSelection: function() {
            var $container = $(this.config.container);
            
            if (this.config.type === 'both') {
                $container.find('#isofonster-configuratorSelection').removeClass('hidden');
                $container.find('#isofonster-windowConfigurator, #isofonster-doorConfigurator').addClass('hidden');
            } else if (this.config.type === 'window') {
                this.showWindowConfigurator();
            } else if (this.config.type === 'door') {
                this.showDoorConfigurator();
            }
        },
        
        // Visa fönsterkonfigurator
        showWindowConfigurator: function() {
            var $container = $(this.config.container);
            
            this.state.currentType = 'window';
            $container.find('#isofonster-configuratorSelection').addClass('hidden');
            $container.find('#isofonster-doorConfigurator').addClass('hidden');
            $container.find('#isofonster-windowConfigurator').removeClass('hidden');
            
            this.goToStep(0);
            this.updateAll();
        },
        
        // Visa dörrkonfigurator
        showDoorConfigurator: function() {
            var $container = $(this.config.container);
            
            this.state.currentType = 'door';
            $container.find('#isofonster-configuratorSelection').addClass('hidden');
            $container.find('#isofonster-windowConfigurator').addClass('hidden');
            $container.find('#isofonster-doorConfigurator').removeClass('hidden');
            
            // TODO: Implementera dörrkonfigurator
            console.log('Dörrkonfigurator kommer snart...');
        },
        
        // Gå till specifikt steg
        goToStep: function(stepIndex) {
            var $container = $(this.config.container);
            var $sections = $container.find('.isofonster-builder-section');
            var $steps = $container.find('.isofonster-step');
            
            // Uppdatera aktiv klass på steg
            $steps.removeClass('active');
            $steps.eq(stepIndex).addClass('active');
            
            // Stäng alla sektioner
            $sections.addClass('collapsed');
            
            // Öppna vald sektion
            if ($sections.eq(stepIndex).length) {
                $sections.eq(stepIndex).removeClass('collapsed');
                
                // Scrolla till sektionen
                $sections.eq(stepIndex)[0].scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start' 
                });
            }
            
            this.state.currentStep = stepIndex;
            
            // Speciell hantering för vissa steg
            if (stepIndex === 2) {
                this.buildOpeningControls();
            }
        },
        
        // Toggle sektion
        toggleSection: function($section) {
            $section.toggleClass('collapsed');
        },
        
        // Uppdatera dimension
        updateDimension: function(type, value) {
            value = parseFloat(value) || 0;
            
            if (this.state.currentType === 'window') {
                if (type === 'width') {
                    this.state.windowData.width = Math.max(50, Math.min(180, value));
                } else if (type === 'height') {
                    this.state.windowData.height = Math.max(60, Math.min(180, value));
                }
            } else if (this.state.currentType === 'door') {
                if (type === 'width') {
                    this.state.doorData.width = Math.max(60, Math.min(450, value));
                } else if (type === 'height') {
                    this.state.doorData.height = Math.max(180, Math.min(240, value));
                }
            }
            
            this.updateDimensionLabels();
            this.updatePreview();
            this.updateSpecTable();
        },
        
        // Uppdatera dimensionsetiketter
        updateDimensionLabels: function() {
            var $container = $(this.config.container);
            
            if (this.state.currentType === 'window') {
                $container.find('#isofonster-widthLabel').text(this.state.windowData.width + ' cm');
                $container.find('#isofonster-heightLabel').text(this.state.windowData.height + ' cm');
                $container.find('#isofonster-widthInput').val(this.state.windowData.width);
                $container.find('#isofonster-heightInput').val(this.state.windowData.height);
            }
        },
        
        // Uppdatera antal lufter
        updateCount: function(newCount) {
            newCount = Math.max(1, Math.min(10, newCount || 1));
            
            this.state.windowData.count = newCount;
            this.state.windowData.openings = Array(newCount).fill('fixed');
            this.state.windowData.hinges = Array(newCount).fill(null);
            this.redistributeSizes();
            
            var $container = $(this.config.container);
            $container.find('#isofonster-customIn').val(newCount);
            
            // Uppdatera öppningskontroller om sektionen är synlig
            if (!$container.find('#isofonster-section-2').hasClass('collapsed')) {
                this.buildOpeningControls();
            }
            
            this.updateAll();
        },
        
        // Omfördela storlekar
        redistributeSizes: function() {
            var count = this.state.windowData.count;
            var equalSize = 100 / count;
            this.state.windowData.openSizes = Array(count).fill(equalSize);
        },
        
        // Bygg öppningskontroller
        buildOpeningControls: function() {
            var $container = $(this.config.container);
            var $controls = $container.find('#isofonster-openingControls');
            var count = this.state.windowData.count;
            
            $controls.empty();
            
            for (var i = 0; i < count; i++) {
                var $wrap = $('<div class="isofonster-opening-wrap">');
                $wrap.append('<h4>Luft ' + (i + 1) + '</h4>');
                
                // Öppningstyp
                var $typeControls = $('<div class="isofonster-opening-type-controls">');
                var types = [
                    { value: 'fixed', label: 'Fast' },
                    { value: 'side-hung', label: 'Sidohängt' },
                    { value: 'top-hung', label: 'Toppvippt' }
                ];
                
                types.forEach(function(type) {
                    var $btn = $('<button class="isofonster-opening-type-btn" data-index="' + i + '" data-type="' + type.value + '">' + type.label + '</button>');
                    if (this.state.windowData.openings[i] === type.value) {
                        $btn.addClass('active');
                    }
                    $typeControls.append($btn);
                }.bind(this));
                
                $wrap.append($typeControls);
                $controls.append($wrap);
            }
            
            // Bind events för öppningstyper
            $controls.on('click', '.isofonster-opening-type-btn', function(e) {
                e.preventDefault();
                var index = parseInt($(this).data('index'));
                var type = $(this).data('type');
                
                this.state.windowData.openings[index] = type;
                
                // Uppdatera aktiv klass
                $(this).siblings().removeClass('active');
                $(this).addClass('active');
                
                this.updateAll();
            }.bind(this));
        },
        
        // Uppdatera allt
        updateAll: function() {
            this.updateDimensionLabels();
            this.updatePreview();
            this.updateSpecTable();
        },
        
        // Uppdatera förhandsvisning
        updatePreview: function() {
            var $container = $(this.config.container);
            var $preview = $container.find('#isofonster-preview');
            
            // Enkel förhandsvisning - kan utökas med mer avancerad rendering
            $preview.html('<div style="text-align: center; color: #999;">Förhandsvisning kommer här</div>');
        },
        
        // Uppdatera specifikationstabell
        updateSpecTable: function() {
            var $container = $(this.config.container);
            var $tbody = $container.find('#isofonster-specTable tbody');
            
            $tbody.empty();
            
            if (this.state.currentType === 'window') {
                var data = this.state.windowData;
                var specs = [
                    ['Bredd', data.width + ' cm'],
                    ['Höjd', data.height + ' cm'],
                    ['Antal lufter', data.count],
                    ['Material', data.material],
                    ['Glastyp', data.glassType]
                ];
                
                specs.forEach(function(spec) {
                    $tbody.append('<tr><td>' + spec[0] + '</td><td>' + spec[1] + '</td></tr>');
                });
            }
        },
        
        // Spara design
        saveDesign: function() {
            if (!this.config.enableSave) return;
            
            var name = prompt('Ange namn för din design:');
            if (!name) return;
            
            var quantity = prompt('Ange antal av denna design:');
            quantity = parseInt(quantity) || 1;
            if (quantity < 1) {
                alert('Ogiltigt antal. Ange ett heltal ≥ 1.');
                return;
            }
            
            var designData = {
                action: 'save_design',
                nonce: this.config.nonce,
                name: name,
                type: this.state.currentType,
                quantity: quantity
            };
            
            // Lägg till specifik data beroende på typ
            if (this.state.currentType === 'window') {
                $.extend(designData, this.state.windowData);
            } else if (this.state.currentType === 'door') {
                $.extend(designData, this.state.doorData);
            }
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: designData,
                success: function(response) {
                    if (response.success) {
                        alert(response.data.message);
                    } else {
                        alert('Fel vid sparande: ' + response.data);
                    }
                },
                error: function() {
                    alert('Fel vid sparande');
                }
            });
        },
        
        // Ladda sparade designs
        loadSavedDesigns: function() {
            if (!this.config.enableSave) return;
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'load_user_designs',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    if (response.success) {
                        this.renderSavedDesigns(response.data);
                    }
                }.bind(this),
                error: function() {
                    console.error('Fel vid laddning av sparade designs');
                }
            });
        },
        
        // Rendera sparade designs
        renderSavedDesigns: function(designs) {
            var $container = $(this.config.container);
            var $list = $container.find('#isofonster-savedList');
            
            $list.empty();
            
            if (designs.length === 0) {
                $list.append('<p class="isofonster-placeholder-text">Inga sparade designs hittades</p>');
                return;
            }
            
            designs.forEach(function(design) {
                var $card = $('<div class="isofonster-saved-card">');
                $card.append('<h4>' + design.name + '</h4>');
                $card.append('<p>' + design.type + ' - ' + design.width + 'x' + design.height + ' cm</p>');
                $card.append('<small>Skapad: ' + new Date(design.created_at).toLocaleDateString() + '</small>');
                
                $list.append($card);
            });
        },
        
        // Exportera PDF
        exportPDF: function() {
            if (!this.config.enableExport) return;
            
            // Visa progress
            var $container = $(this.config.container);
            var $progress = $container.find('#isofonster-pdfProgressContainer');
            $progress.removeClass('hidden');
            
            // Simulera progress (i riktig implementation skulle detta komma från server)
            var progress = 0;
            var interval = setInterval(function() {
                progress += 10;
                $container.find('#isofonster-pdfProgressBar').css('width', progress + '%');
                
                if (progress >= 100) {
                    clearInterval(interval);
                    $progress.addClass('hidden');
                    alert('PDF export slutförd!');
                }
            }, 200);
        }
    };

    // Auto-initialisera om konfiguration finns
    $(document).ready(function() {
        if ($('.isofonster-configurator-container').length > 0) {
            // Vänta lite för att säkerställa att alla scripts är laddade
            setTimeout(function() {
                if (typeof window.IsofonsterConfigurator !== 'undefined') {
                    // Konfiguratorn kommer att initialiseras från template-filen
                    console.log('Isofönster Konfigurator redo för initialisering');
                }
            }, 100);
        }
    });

})(jQuery);
