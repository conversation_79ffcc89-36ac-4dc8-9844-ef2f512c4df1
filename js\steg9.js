// js/steg9.js - Handtag (Handles)

// Definiera handtagstyper och deras etiketter om de inte redan finns
if (!window.HANDLE_LABELS) {
  window.HANDLE_LABELS = {
    'none': 'Inget handtag',
    'stormkrok': 'Handtag med stormkrok',
    'barnlas-stormkrok': 'Handtag med barnlås och stormkrok',
    'krom': 'Kromhandtag',
    'vitt': 'Vitt handtag',
    'vitt-las': 'Vitt handtag med lås',
    'krom-las-nyckel': 'Kromhandtag med lås + nyckel (barnsäker)'
  };
}

// Standardval om inget tidigare val finns
if (typeof window.selectedHandle === 'undefined') {
  window.selectedHandle = null;
}

  // Funktion för att uppdatera specifikationstabellen med handtagsinformation
  function updateHandleSpec() {
    // Ta bort handtagsraden från alla specifikationstabeller
    const allSpecTables = document.querySelectorAll('table tbody');
    allSpecTables.forEach(tableBody => {
      const handleRow = tableBody.querySelector('tr[data-key="handle"]');
      if (handleRow) handleRow.remove();
    });

    // Lägg till ny handtagsrad om ett handtag är valt
    if (window.selectedHandle && window.selectedHandle !== 'none') {
      allSpecTables.forEach(tableBody => {
        const tr = document.createElement('tr');
        tr.setAttribute('data-key', 'handle');
        tr.innerHTML = `<td>Handtag</td><td>${window.HANDLE_LABELS[window.selectedHandle]}</td>`;
        tableBody.appendChild(tr);
      });
    }
  }

  // Gör funktionen tillgänglig globalt så att den kan anropas från loadConfiguration
  window.renderHandleSpec = updateHandleSpec;

// Funktion för att hantera klick på handtagsalternativ - global för att kunna anropas från HTML
window.handleHandleClick = function(element, handleValue) {
  if (!handleValue && element) {
    handleValue = element.dataset.value;
  }

  // Säkerställ att window.openings är initierad
  if (!window.openings) {
    window.openings = ['fixed']; // Standardvärde
  }

  // Kontrollera om alla fönster är fasta
  const allFixed = window.openings.length > 0 && window.openings.every(type => type === 'fixed');

  // Kontrollera om det finns sidohängda fönster
  const hasSideHung = window.openings.some(type => type === 'side-hung');

  // Validera handtagsval baserat på fönstertyp
  if (allFixed) {
    // Fasta fönster ska inte ha handtag
    alert('Fasta fönster behöver inga handtag');
    return;
  }

  // Kontrollera om stormkrok-handtag valdes för icke-sidohängda fönster
  if (!hasSideHung && (handleValue === 'stormkrok' || handleValue === 'barnlas-stormkrok')) {
    alert('Stormkrok-handtag kan bara användas för sidohängda fönster');
    return;
  }

  // Uppdatera aktiv klass
  document.querySelectorAll('.handle-option').forEach(opt => {
    opt.classList.toggle('active', opt.dataset.value === handleValue);
  });

  // Uppdatera valt handtag
  window.selectedHandle = handleValue;

  // Uppdatera specifikationstabellen
  updateHandleSpec();

  // Uppdatera 3D-modellen om den är tillgänglig
  if (window.Preview3D && typeof window.updateHandleInModel === 'function') {
    window.updateHandleInModel();
  }

  // Trigga en händelse för att meddela andra delar av applikationen
  const event = new CustomEvent('handleChanged', {
    detail: { handle: handleValue }
  });
  document.dispatchEvent(event);
}

// Funktion för att uppdatera handtagsalternativ baserat på fönstertyp
window.updateHandleOptions = function() {
  // Säkerställ att window.openings är initierad
  if (!window.openings) {
    window.openings = ['fixed']; // Standardvärde
  }

  // Kontrollera om alla fönster är fasta
  const allFixed = window.openings.length > 0 && window.openings.every(type => type === 'fixed');

  // Kontrollera om det finns sidohängda fönster
  const hasSideHung = window.openings.some(type => type === 'side-hung');

  // Kontrollera om det finns vridfönster, topphängda eller bottenhängda fönster
  const hasOtherOpenable = window.openings.some(type =>
    type === 'tilt-turn' || type === 'top-hung' || type === 'bottom-hung'
  );

  // Uppdatera handtagssektionen
  const handleSection = document.querySelector('.handle-section');
  const handleSectionText = document.querySelector('#section-8 .section-body > p');

  if (allFixed) {
    // Fasta fönster: Inga handtag
    if (handleSection) handleSection.style.display = 'none';
    if (handleSectionText) handleSectionText.textContent = 'Fasta fönster behöver inga handtag.';
    window.selectedHandle = null;
    updateHandleSpec();
    return;
  } else {
    // Visa handtagssektionen
    if (handleSection) handleSection.style.display = 'block';
    if (handleSectionText) handleSectionText.textContent = 'Välj handtag för ditt fönster.';
  }

  // Uppdatera handtagsalternativ
  const handleOptions = document.querySelectorAll('.handle-option');

  handleOptions.forEach(option => {
    const handleType = option.dataset.value;

    // Stormkrok-handtag (stormkrok och barnlas-stormkrok) endast för sidohängda fönster
    if (handleType === 'stormkrok' || handleType === 'barnlas-stormkrok') {
      if (hasSideHung) {
        option.style.display = 'flex';
      } else {
        option.style.display = 'none';
      }
    }
    // Övriga handtag för alla öppningsbara fönster
    else if (!allFixed) {
      option.style.display = 'flex';
    }
    // Fasta fönster: Inga handtag
    else {
      option.style.display = 'none';
    }

    // Lägg till onclick-attribut direkt
    option.setAttribute('onclick', `window.handleHandleClick(this, '${handleType}')`);
  });

  // Markera aktivt val om det finns
  document.querySelectorAll('.handle-option').forEach(opt => {
    opt.classList.remove('active');
  });

  if (window.selectedHandle) {
    const activeOption = document.querySelector(`.handle-option[data-value="${window.selectedHandle}"]`);
    if (activeOption && activeOption.style.display !== 'none') {
      activeOption.classList.add('active');
    } else {
      // Om det aktiva valet inte är synligt, återställ valet
      window.selectedHandle = null;
      updateHandleSpec();
    }
  }
}

  // Gör funktionen tillgänglig globalt
  window.setupHandleOptions = updateHandleOptions;

  // Kör setup när DOM är laddad
  setTimeout(updateHandleOptions, 100);

  // Lyssna på stegnavigering
  document.addEventListener('click', function(event) {
    if (event.target.classList.contains('step') ||
        event.target.closest('.step') ||
        event.target.classList.contains('next-btn') ||
        event.target.classList.contains('prev-btn')) {

      setTimeout(() => {
        const handleSection = document.getElementById('section-8');
        if (handleSection && !handleSection.classList.contains('collapsed')) {
          updateHandleOptions();
        }
      }, 100);
    }
  });

  // Lyssna på ändringar i fönstertyp
  document.addEventListener('openingTypeChanged', function(event) {
    // Uppdatera window.openings från event om det finns
    if (event.detail && event.detail.openings) {
      window.openings = [...event.detail.openings];
    }

    // Uppdatera handtagsalternativen
    setTimeout(updateHandleOptions, 100);

    // Visa handtagssektionen om vi är på handtagssteget
    const handleSection = document.getElementById('section-8');
    if (handleSection && !handleSection.classList.contains('collapsed')) {
      setTimeout(() => {
        // Säkerställ att handtagssektionen är synlig
        const handleSectionElement = document.querySelector('.handle-section');
        if (handleSectionElement) {
          handleSectionElement.style.display = 'block';
        }

        // Visa handtagsalternativ baserat på fönstertyper
        const allFixed = window.openings && window.openings.length > 0 &&
                        window.openings.every(type => type === 'fixed');
        const hasSideHung = window.openings && window.openings.some(type => type === 'side-hung');

        if (!allFixed) {
          document.querySelectorAll('.handle-option').forEach(option => {
            const handleType = option.dataset.value;

            // Stormkrok-handtag (stormkrok och barnlas-stormkrok) endast för sidohängda fönster
            if (handleType === 'stormkrok' || handleType === 'barnlas-stormkrok') {
              if (hasSideHung) {
                option.style.display = 'flex';
              } else {
                option.style.display = 'none';
              }
            }
            // Övriga handtag för alla öppningsbara fönster
            else {
              option.style.display = 'flex';
            }
          });

          // Om ett stormkrok-handtag är valt men det inte finns sidohängda fönster, återställ valet
          if (window.selectedHandle &&
              (window.selectedHandle === 'stormkrok' || window.selectedHandle === 'barnlas-stormkrok') &&
              !hasSideHung) {
            window.selectedHandle = null;

            // Ta bort aktiv klass från alla handtagsalternativ
            document.querySelectorAll('.handle-option').forEach(opt => {
              opt.classList.remove('active');
            });

            // Uppdatera specifikationstabellen
            if (typeof window.renderHandleSpec === 'function') {
              window.renderHandleSpec();
            }
          }
        }
      }, 200);
    }
  });

  // Spara handtagsval tillsammans med resten av designen
  const saveBtn = document.querySelector('#section-9 .save-btn');
  if (saveBtn) {
    saveBtn.addEventListener('click', () => {
      setTimeout(() => {
        const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
        const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
        if (idx >= 0) {
          arr[idx].handle = window.selectedHandle;
          localStorage.setItem('myWindows', JSON.stringify(arr));
        }
      }, 0);
    });
  }
