<?php
/**
 * Plugin Name: Isof<PERSON>nster Konfigurator
 * Plugin URI: https://isofonster.se
 * Description: En avancerad fönster- och dörrkonfigurator för WordPress och Elementor
 * Version: 1.0.0
 * Author: Isofönster.se
 * Text Domain: isofonster-configurator
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Förhindra direkt åtkomst
if (!defined('ABSPATH')) {
    exit;
}

// Plugin-konstanter
define('ISOFONSTER_CONFIGURATOR_VERSION', '1.0.0');
define('ISOFONSTER_CONFIGURATOR_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ISOFONSTER_CONFIGURATOR_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('ISOFONSTER_CONFIGURATOR_PLUGIN_FILE', __FILE__);

/**
 * Huvudklass för Isofönster Konfigurator
 */
class IsofonsterConfigurator {

    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Hämta singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Konstruktor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));

        // Aktivering och avaktivering
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialisera plugin
     */
    public function init() {
        // Ladda klasser
        $this->load_dependencies();

        // Registrera assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));

        // Elementor integration
        add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widgets'));
        add_action('elementor/elements/categories_registered', array($this, 'register_elementor_category'));

        // AJAX handlers
        add_action('wp_ajax_save_design', array($this, 'ajax_save_design'));
        add_action('wp_ajax_nopriv_save_design', array($this, 'ajax_save_design'));
        add_action('wp_ajax_load_design', array($this, 'ajax_load_design'));
        add_action('wp_ajax_nopriv_load_design', array($this, 'ajax_load_design'));
        add_action('wp_ajax_export_pdf', array($this, 'ajax_export_pdf'));
        add_action('wp_ajax_nopriv_export_pdf', array($this, 'ajax_export_pdf'));

        // Shortcode
        add_shortcode('isofonster_configurator', array($this, 'shortcode_configurator'));
    }

    /**
     * Ladda textdomain för översättningar
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'isofonster-configurator',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    /**
     * Ladda beroenden
     */
    private function load_dependencies() {
        require_once ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'includes/class-elementor-widget.php';
        require_once ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'includes/class-admin.php';
        require_once ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'includes/class-ajax-handler.php';
        require_once ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'includes/class-database.php';

        // Initiera admin-klass
        if (is_admin()) {
            new \IsofonsterConfigurator\Admin();
        }
    }

    /**
     * Registrera frontend scripts och styles
     */
    public function enqueue_scripts() {
        // CSS - använd minifierad version i produktion
        $css_file = (defined('WP_DEBUG') && WP_DEBUG) ? 'configurator.css' : 'configurator.min.css';
        wp_enqueue_style(
            'isofonster-configurator-style',
            ISOFONSTER_CONFIGURATOR_PLUGIN_URL . 'assets/css/' . $css_file,
            array(),
            ISOFONSTER_CONFIGURATOR_VERSION
        );

        // JavaScript - använd minifierad version i produktion
        $js_file = (defined('WP_DEBUG') && WP_DEBUG) ? 'configurator.js' : 'configurator.min.js';
        wp_enqueue_script(
            'isofonster-configurator-script',
            ISOFONSTER_CONFIGURATOR_PLUGIN_URL . 'assets/js/' . $js_file,
            array('jquery'),
            ISOFONSTER_CONFIGURATOR_VERSION,
            true
        );

        // Externa bibliotek
        wp_enqueue_script('html2canvas', 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js', array(), '1.4.1', true);
        wp_enqueue_script('jspdf', 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', array(), '2.5.1', true);
        wp_enqueue_script('threejs', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js', array(), 'r128', true);

        // Lokalisera script
        wp_localize_script('isofonster-configurator-script', 'isofonster_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('isofonster_nonce'),
            'strings' => array(
                'save_success' => __('Design sparad!', 'isofonster-configurator'),
                'save_error' => __('Fel vid sparande', 'isofonster-configurator'),
                'load_error' => __('Fel vid laddning', 'isofonster-configurator'),
                'export_success' => __('PDF exporterad!', 'isofonster-configurator'),
                'export_error' => __('Fel vid export', 'isofonster-configurator'),
            )
        ));
    }

    /**
     * Registrera admin scripts och styles
     */
    public function admin_enqueue_scripts($hook) {
        // Ladda endast på plugin-sidor
        if (strpos($hook, 'isofonster') === false) {
            return;
        }

        wp_enqueue_style(
            'isofonster-admin-style',
            ISOFONSTER_CONFIGURATOR_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            ISOFONSTER_CONFIGURATOR_VERSION
        );

        wp_enqueue_script(
            'isofonster-admin-script',
            ISOFONSTER_CONFIGURATOR_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            ISOFONSTER_CONFIGURATOR_VERSION,
            true
        );
    }

    /**
     * Registrera Elementor widgets
     */
    public function register_elementor_widgets() {
        if (class_exists('\Elementor\Plugin')) {
            \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \IsofonsterConfigurator\ElementorWidget());
        }
    }

    /**
     * Registrera Elementor kategori
     */
    public function register_elementor_category($elements_manager) {
        $elements_manager->add_category(
            'isofonster',
            array(
                'title' => __('Isofönster', 'isofonster-configurator'),
                'icon' => 'fa fa-window-maximize',
            )
        );
    }

    /**
     * AJAX: Spara design
     */
    public function ajax_save_design() {
        check_ajax_referer('isofonster_nonce', 'nonce');

        $ajax_handler = new \IsofonsterConfigurator\AjaxHandler();
        $ajax_handler->save_design();
    }

    /**
     * AJAX: Ladda design
     */
    public function ajax_load_design() {
        check_ajax_referer('isofonster_nonce', 'nonce');

        $ajax_handler = new \IsofonsterConfigurator\AjaxHandler();
        $ajax_handler->load_design();
    }

    /**
     * AJAX: Exportera PDF
     */
    public function ajax_export_pdf() {
        check_ajax_referer('isofonster_nonce', 'nonce');

        $ajax_handler = new \IsofonsterConfigurator\AjaxHandler();
        $ajax_handler->export_pdf();
    }

    /**
     * Shortcode för konfigurator
     */
    public function shortcode_configurator($atts) {
        $atts = shortcode_atts(array(
            'type' => 'both', // 'window', 'door', 'both'
            'theme' => 'default',
            'width' => '100%',
            'height' => 'auto'
        ), $atts, 'isofonster_configurator');

        ob_start();
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/configurator.php';
        return ob_get_clean();
    }

    /**
     * Plugin aktivering
     */
    public function activate() {
        // Skapa databastabeller
        $database = new \IsofonsterConfigurator\Database();
        $database->create_tables();

        // Sätt default options
        add_option('isofonster_configurator_version', ISOFONSTER_CONFIGURATOR_VERSION);

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin avaktivering
     */
    public function deactivate() {
        // Rensa cache
        wp_cache_flush();

        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initiera plugin
IsofonsterConfigurator::get_instance();
