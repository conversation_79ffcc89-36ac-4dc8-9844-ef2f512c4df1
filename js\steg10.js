// steg10.js - Myggnät-steget i fönsterkonfiguratorn

(() => {
  // Vänta på att DOM är laddad
  document.addEventListener('DOMContentLoaded', function() {
    // Globala variabler för myggnät
    window.selectedMosquitoNet = false; // Standard: utan myggnät

    // Konstanter för myggnät-etiketter
    window.MOSQUITO_NET_LABELS = {
      'false': 'Utan myggnät',
      'true': 'Med myggnät'
    };

    // Global klickhanterare för myggnät-alternativ
    window.handleMosquitoNetClick = function(element, value) {
      // Ta bort active-klassen från alla alternativ
      document.querySelectorAll('.mosquito-net-option').forEach(opt => opt.classList.remove('active'));

      // Lägg till active-klassen på det klickade alternativet
      element.classList.add('active');

      // Uppdatera valt myggnät
      window.selectedMosquitoNet = value;

      // Uppdatera specifikationstabellen
      if (typeof window.updateMosquitoNetSpec === 'function') {
        window.updateMosquitoNetSpec();
      }
    };

    // Initiera myggnät-funktionalitet
    initMosquitoNetFunctionality();

    // Funktion för att initiera myggnät-funktionalitet
    function initMosquitoNetFunctionality() {

      // Kontrollera om alla fönster är fasta
      function checkAllFixedWindows() {
        // Om window.openings inte är definierad, anta att alla fönster är fasta
        if (!window.openings) return true;

        // Kontrollera om alla fönster är fasta
        return window.openings.every(type => type === 'fixed');
      }

      // Funktion för att uppdatera myggnät-sektionen baserat på fönstertyper
      function updateMosquitoNetSection() {
        const mosquitoNetSection = document.querySelector('.mosquito-net-section');
        const mosquitoNetInfo = document.querySelector('.mosquito-net-info');
        const noMosquitoNetBtn = document.getElementById('noMosquitoNet');
        const withMosquitoNetBtn = document.getElementById('withMosquitoNet');

        if (!mosquitoNetSection || !mosquitoNetInfo || !noMosquitoNetBtn || !withMosquitoNetBtn) return;

        const allFixed = checkAllFixedWindows();

        if (allFixed) {
          // Om alla fönster är fasta, visa informationstext och inaktivera val
          mosquitoNetInfo.innerHTML = '<i class="info-icon" style="color: #ff5252;">ⓘ</i><span>Myggnät kan inte installeras på fasta fönster.</span>';
          mosquitoNetInfo.style.borderLeftColor = '#ff5252';
          mosquitoNetInfo.style.backgroundColor = '#333333';

          // Inaktivera myggnät-alternativ
          withMosquitoNetBtn.disabled = true;
          withMosquitoNetBtn.style.opacity = '0.5';
          withMosquitoNetBtn.style.cursor = 'not-allowed';

          // Välj "Utan myggnät" som standard
          noMosquitoNetBtn.classList.add('active');
          withMosquitoNetBtn.classList.remove('active');

          // Uppdatera valt myggnät
          window.selectedMosquitoNet = false;
        } else {
          // Om minst ett fönster är öppningsbart, aktivera val
          mosquitoNetInfo.innerHTML = '<i class="info-icon">ⓘ</i><span>Myggnät kommer finnas på det fönster som har en öppning och inte på fasta fönster.</span>';
          mosquitoNetInfo.style.borderLeftColor = '#f5c700';
          mosquitoNetInfo.style.backgroundColor = '#333333';

          // Aktivera myggnät-alternativ
          withMosquitoNetBtn.disabled = false;
          withMosquitoNetBtn.style.opacity = '';
          withMosquitoNetBtn.style.cursor = 'pointer';
        }

        // Uppdatera specifikationstabellen
        updateMosquitoNetSpec();
      }

      // Lyssna på ändringar i fönstertyp
      document.addEventListener('openingTypeChanged', updateMosquitoNetSection);

      // Kör initial uppdatering
      updateMosquitoNetSection();
    }

    // Funktion för att uppdatera myggnät i specifikationstabellen
    function updateMosquitoNetSpec() {
      console.log("Uppdaterar myggnät-specifikation, värde:", window.selectedMosquitoNet);

      // Hämta specifikationstabellen
      const specTable = document.getElementById('specTable');
      if (!specTable) return;

      const tbody = specTable.querySelector('tbody');
      if (!tbody) return;

      // Ta bort befintlig myggnät-rad om den finns
      const existingRow = tbody.querySelector('tr[data-key="mosquitoNet"]');
      if (existingRow) {
        existingRow.remove();
      }

      // Kontrollera om alla fönster är fasta
      const allFixed = window.openings && window.openings.every(type => type === 'fixed');

      // Lägg alltid till myggnät-rad, men med rätt värde baserat på fönstertyp och val
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'mosquitoNet');

      // Om alla fönster är fasta, visa alltid "Utan myggnät"
      if (allFixed) {
        tr.innerHTML = `<td>Myggnät</td><td>${window.MOSQUITO_NET_LABELS['false']}</td>`;
      } else {
        // Annars visa det valda värdet
        tr.innerHTML = `<td>Myggnät</td><td>${window.MOSQUITO_NET_LABELS[window.selectedMosquitoNet.toString()]}</td>`;
      }

      tbody.appendChild(tr);
    }

    // Gör funktionen tillgänglig globalt
    window.updateMosquitoNetSpec = updateMosquitoNetSpec;

    // Patcha updateSpecTable för att inkludera myggnät-information
    if (typeof window.updateSpecTable === 'function') {
      const originalUpdateSpecTable = window.updateSpecTable;
      window.updateSpecTable = function() {
        originalUpdateSpecTable();
        updateMosquitoNetSpec();
      };
    }

    // Spara myggnät-val tillsammans med resten av designen
    const saveBtn = document.querySelector('#section-10 .save-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        setTimeout(() => {
          const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
          const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
          if (idx >= 0) {
            arr[idx].mosquitoNet = window.selectedMosquitoNet;
            localStorage.setItem('myWindows', JSON.stringify(arr));
          }
        }, 0);
      });
    }

    // Patcha showSavedDetail för att visa myggnät-information
    function patchShowSavedDetail() {
      if (typeof window.showSavedDetail !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchShowSavedDetail, 50);
      }

      const originalShowSavedDetail = window.showSavedDetail;
      window.showSavedDetail = function(cfg, idx) {
        originalShowSavedDetail(cfg, idx);

        // Lägg till myggnät-rad om det finns
        if (cfg.mosquitoNet !== undefined) {
          const tb = document.querySelector('#savedDetail .saved-spec tbody');
          if (!tb) return;

          // Ta bort befintlig myggnät-rad om den finns
          const existingRow = tb.querySelector('tr[data-key="mosquitoNet"]');
          if (existingRow) {
            existingRow.remove();
          }

          // Lägg till myggnät-rad
          const row = document.createElement('tr');
          row.setAttribute('data-key', 'mosquitoNet');
          row.innerHTML = `<td>Myggnät</td><td>${window.MOSQUITO_NET_LABELS[cfg.mosquitoNet.toString()]}</td>`;
          tb.appendChild(row);
        }
      };
    }

    // Patcha loadConfiguration för att ladda myggnät-val
    function patchLoadConfiguration() {
      if (typeof window.loadConfiguration !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchLoadConfiguration, 50);
      }

      const originalLoadConfiguration = window.loadConfiguration;
      window.loadConfiguration = function(cfg, idx) {
        originalLoadConfiguration(cfg, idx);

        // Återställ myggnät-val om det finns
        if (cfg.mosquitoNet !== undefined) {
          console.log('patchLoadConfiguration: Återställer myggnätsval från sparad design:', cfg.mosquitoNet);
          window.selectedMosquitoNet = cfg.mosquitoNet;

          // Uppdatera UI för de nya knapparna
          const noMosquitoNetBtn = document.getElementById('noMosquitoNet');
          const withMosquitoNetBtn = document.getElementById('withMosquitoNet');

          if (noMosquitoNetBtn && withMosquitoNetBtn) {
            // Kontrollera om alla fönster är fasta
            const allFixed = window.openings && window.openings.every(type => type === 'fixed');

            if (allFixed) {
              // Om alla fönster är fasta, inaktivera "Med myggnät"-knappen
              withMosquitoNetBtn.disabled = true;
              withMosquitoNetBtn.style.opacity = '0.5';
              withMosquitoNetBtn.style.cursor = 'not-allowed';

              // Välj "Utan myggnät" som standard
              noMosquitoNetBtn.classList.add('active');
              withMosquitoNetBtn.classList.remove('active');
              window.selectedMosquitoNet = false;
            } else {
              // Aktivera "Med myggnät"-knappen
              withMosquitoNetBtn.disabled = false;
              withMosquitoNetBtn.style.opacity = '';
              withMosquitoNetBtn.style.cursor = '';

              // Sätt korrekt val baserat på konfigurationen
              if (cfg.mosquitoNet) {
                withMosquitoNetBtn.classList.add('active');
                noMosquitoNetBtn.classList.remove('active');
              } else {
                noMosquitoNetBtn.classList.add('active');
                withMosquitoNetBtn.classList.remove('active');
              }
            }
          } else {
            console.warn('Kunde inte hitta myggnätsknappar i DOM');
          }

          // Uppdatera specifikationstabellen
          if (typeof window.updateMosquitoNetSpec === 'function') {
            window.updateMosquitoNetSpec();
          }
        } else {
          // Standardvärde om inget finns sparat
          window.selectedMosquitoNet = false;

          const noMosquitoNetBtn = document.getElementById('noMosquitoNet');
          const withMosquitoNetBtn = document.getElementById('withMosquitoNet');

          if (noMosquitoNetBtn && withMosquitoNetBtn) {
            noMosquitoNetBtn.classList.add('active');
            withMosquitoNetBtn.classList.remove('active');
          }
        }
      };
    }

    // Anropa patchar
    patchShowSavedDetail();
    patchLoadConfiguration();

    // Kör en initial uppdatering av specifikationstabellen
    setTimeout(function() {
      if (typeof window.updateMosquitoNetSpec === 'function') {
        window.updateMosquitoNetSpec();
      }
    }, 500);
  });
})();
