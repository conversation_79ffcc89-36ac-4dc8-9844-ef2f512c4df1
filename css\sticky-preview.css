/* Sticky Preview and Specification */

/* Make the result column sticky */
.result-column {
  position: sticky;
  top: 1rem;
  height: calc(100vh - 2rem);
  display: flex;
  flex-direction: column;
}

/* Preview container takes most of the space */
.preview-container {
  flex: 1;
  min-height: 300px;
  overflow: auto;
  margin-bottom: 1rem;
}

/* Specification container with scrolling */
.spec-container {
  max-height: 40vh;
  overflow-y: auto;
  transition: max-height 0.3s ease;
}

/* Collapsed state for specification */
.spec-container.collapsed {
  max-height: 3rem;
  overflow: hidden;
}

/* Toggle button for specification */
.spec-toggle {
  background: var(--color-panel);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 0.5rem;
  margin-top: -1px;
  margin-bottom: 1rem;
  text-align: center;
  cursor: pointer;
  color: var(--color-secondary);
  font-size: 0.9rem;
  transition: background 0.2s ease;
}

.spec-toggle:hover {
  background: rgba(245, 199, 0, 0.1);
}

/* Media query for smaller screens */
@media (max-width: 768px) {
  .result-column {
    position: static;
    height: auto;
  }
  
  .preview-container {
    min-height: 200px;
  }
  
  .spec-container {
    max-height: none;
  }
}
