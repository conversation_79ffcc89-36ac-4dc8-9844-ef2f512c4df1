/**
 * preview-renderer.js
 * Hanterar rendering av fönsterförhandsvisning
 */

(() => {
  // Konstanter för rendering
  const PIXELS_PER_CM = 2;

  // Referens till DOM-element
  let previewContainer;
  let previewElement;

  // Initialisera när DOM är laddad
  document.addEventListener('DOMContentLoaded', () => {
    previewContainer = document.querySelector('.preview-container');
    previewElement = document.getElementById('preview');

    if (!previewElement || !previewContainer) {
      console.error('Preview elements not found');
      return;
    }

    // Exponera funktioner globalt
    window.PreviewRenderer = {
      updateDimensions,
      renderPreview,
      renderWindowFrame
    };

    // Rendera direkt ett fast fönster med 1 luft
    const widthInput = document.getElementById('widthInput');
    const heightInput = document.getElementById('heightInput');

    if (widthInput && heightInput) {
      const width = +widthInput.value;
      const height = +heightInput.value;

      // Uppdatera dimensioner
      updateDimensions(width, height);

      // Rendera ett fast fönster
      renderPreview({
        count: 1,
        openSizes: [width],
        openings: ['fixed'],
        hinges: [null]
      });
    }
  });

  /**
   * Uppdaterar dimensionerna för preview-containern
   * @param {number} width - Bredd i cm
   * @param {number} height - Höjd i cm
   * @param {HTMLElement} targetContainer - Valfri målcontainer att uppdatera (om inte angivet används previewContainer)
   */
  function updateDimensions(width, height, targetContainer) {
    // Använd den angivna målcontainern eller fallback till previewContainer
    const container = targetContainer || previewContainer;

    if (!container) {
      console.error('No valid container for updating dimensions');
      return;
    }

    container.style.width = `${width * PIXELS_PER_CM}px`;
    container.style.height = `${height * PIXELS_PER_CM}px`;
  }

  /**
   * Renderar hela preview baserat på aktuell konfiguration
   * @param {Object} config - Konfigurationsobjekt
   * @param {HTMLElement} targetElement - Valfritt målelement att rendera i (om inte angivet används previewElement)
   */
  function renderPreview(config, targetElement) {
    // Använd det angivna målelementet eller fallback till previewElement
    const target = targetElement || previewElement;

    if (!target) {
      console.error('No valid target element for preview rendering');
      return;
    }

    const { count, openSizes, openings, hinges } = config;

    // Hämta spröjsinformation om den finns
    const sprojsPerLuft = config.sprojsPerLuft || Array(count).fill('none');
    const customSprojsPerLuft = config.customSprojsPerLuft || Array(count).fill({ horizontal: 2, vertical: 2 });

    // Rensa tidigare innehåll
    target.innerHTML = '';

    // Skapa fönsterramar
    for (let i = 0; i < count; i++) {
      const frameElement = renderWindowFrame({
        size: openSizes[i],
        opening: openings[i],
        hinge: hinges[i],
        sprojsType: sprojsPerLuft[i] || 'none',
        customSprojs: customSprojsPerLuft[i] || { horizontal: 2, vertical: 2 }
      });

      target.appendChild(frameElement);
    }
  }

  /**
   * Skapar ett fönsterelement baserat på konfiguration
   * @param {Object} config - Konfiguration för fönstret
   * @returns {HTMLElement} - DOM-element för fönstret
   */
  function renderWindowFrame(config) {
    const { size, opening, hinge, sprojsType, customSprojs } = config;

    // Skapa fönsterram
    const frame = document.createElement('div');
    frame.className = 'window-frame';
    frame.style.flex = `${size} 0 0`;

    // Lägg till glasruta
    const pane = document.createElement('div');
    pane.className = 'pane';
    frame.appendChild(pane);

    // Skapa SVG för öppningslinjer med förbättrad positionering
    const svg = createSVG('svg', {
      class: 'frame-svg',
      viewBox: '0 0 100 100',
      preserveAspectRatio: 'none',
      width: '100%',
      height: '100%',
      'vector-effect': 'non-scaling-stroke'
    });
    frame.appendChild(svg);

    // Lägg till öppningslinjer om det inte är ett fast fönster
    if (opening !== 'fixed') {
      addOpeningLines(svg, opening, hinge);

      // Lägg till handtag
      const handle = createHandle(opening, hinge);
      frame.appendChild(handle);
    }

    // Lägg till spröjs om det finns
    if (sprojsType && sprojsType !== 'none') {
      addSprojsLines(svg, sprojsType, customSprojs);
    }

    return frame;
  }

  /**
   * Lägger till spröjslinjer i SVG baserat på spröjstyp
   * @param {SVGElement} svg - SVG-element att lägga till linjer i
   * @param {string} sprojsType - Typ av spröjs
   * @param {Object} customSprojs - Anpassade spröjsinställningar (för custom-typ)
   */
  function addSprojsLines(svg, sprojsType, customSprojs) {
    // Skapa linjer baserat på spröjstyp
    switch (sprojsType) {
      case 'one-horizontal': {
        // En horisontell spröjs
        const line = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 100,
          y2: 50,
          'stroke-width': '1',
          'stroke': '#333',
          class: 'sprojs-line'
        });
        svg.appendChild(line);
        break;
      }
      case 'one-vertical': {
        // En vertikal spröjs
        const line = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '1',
          'stroke': '#333',
          class: 'sprojs-line'
        });
        svg.appendChild(line);
        break;
      }
      case 'cross': {
        // Korsande spröjs (en horisontell och en vertikal)
        const hLine = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 100,
          y2: 50,
          'stroke-width': '1',
          'stroke': '#333',
          class: 'sprojs-line'
        });

        const vLine = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '1',
          'stroke': '#333',
          class: 'sprojs-line'
        });

        svg.appendChild(hLine);
        svg.appendChild(vLine);
        break;
      }
      case 'grid': {
        // Rutnät (två horisontella och två vertikala)
        // Horisontella linjer
        for (let i = 1; i < 3; i++) {
          const line = createSVG('line', {
            x1: 0,
            y1: i * 33.33,
            x2: 100,
            y2: i * 33.33,
            'stroke-width': '1',
            'stroke': '#333',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }

        // Vertikala linjer
        for (let i = 1; i < 3; i++) {
          const line = createSVG('line', {
            x1: i * 33.33,
            y1: 0,
            x2: i * 33.33,
            y2: 100,
            'stroke-width': '1',
            'stroke': '#333',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }
        break;
      }
      case 'custom': {
        // Anpassade spröjs
        if (!customSprojs) {
          customSprojs = { horizontal: 2, vertical: 2 };
        }

        const hCount = customSprojs.horizontal || 0;
        const vCount = customSprojs.vertical || 0;

        // Horisontella linjer
        for (let i = 0; i < hCount; i++) {
          const position = (i + 1) * (100 / (hCount + 1));
          const line = createSVG('line', {
            x1: 0,
            y1: position,
            x2: 100,
            y2: position,
            'stroke-width': '1',
            'stroke': '#333',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }

        // Vertikala linjer
        for (let i = 0; i < vCount; i++) {
          const position = (i + 1) * (100 / (vCount + 1));
          const line = createSVG('line', {
            x1: position,
            y1: 0,
            x2: position,
            y2: 100,
            'stroke-width': '1',
            'stroke': '#333',
            class: 'sprojs-line'
          });
          svg.appendChild(line);
        }
        break;
      }
    }
  }

  /**
   * Lägger till öppningslinjer i SVG baserat på öppningstyp
   * @param {SVGElement} svg - SVG-element att lägga till linjer i
   * @param {string} opening - Typ av öppning
   * @param {string} hinge - Typ av gångjärn (för sidohängda fönster)
   */
  function addOpeningLines(svg, opening, hinge) {
    // Säkerställ att SVG har rätt attribut för korrekt rendering
    svg.setAttribute('preserveAspectRatio', 'none');
    svg.setAttribute('viewBox', '0 0 100 100');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');

    // Skapa linjer baserat på öppningstyp
    switch (opening) {
      case 'side-hung': {
        const px = hinge === 'side-left' ? 100 : 0;
        const ox = 100 - px;

        // Skapa linjer med förbättrad synlighet
        const line1 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 0,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line2 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 100,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'top-hung': {
        const line1 = createSVG('line', {
          x1: 0,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line2 = createSVG('line', {
          x1: 100,
          y1: 0,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'bottom-hung': {
        const line1 = createSVG('line', {
          x1: 0,
          y1: 100,
          x2: 50,
          y2: 0,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line2 = createSVG('line', {
          x1: 100,
          y1: 100,
          x2: 50,
          y2: 0,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        break;
      }
      case 'tilt-turn': {
        const line1 = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 0,
          y2: 50,
          'stroke-dasharray': '4,4',
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line2 = createSVG('line', {
          x1: 50,
          y1: 0,
          x2: 100,
          y2: 50,
          'stroke-dasharray': '4,4',
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line3 = createSVG('line', {
          x1: 0,
          y1: 50,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        const line4 = createSVG('line', {
          x1: 100,
          y1: 50,
          x2: 50,
          y2: 100,
          'stroke-width': '2',
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
        svg.appendChild(line3);
        svg.appendChild(line4);
        break;
      }
    }
  }

  /**
   * Skapar ett handtag baserat på öppningstyp
   * @param {string} opening - Typ av öppning
   * @param {string} hinge - Typ av gångjärn (för sidohängda fönster)
   * @returns {HTMLElement} - DOM-element för handtaget
   */
  function createHandle(opening, hinge) {
    const handle = document.createElement('div');

    // Bestäm position för handtaget
    let positionClass;
    if (opening === 'side-hung') {
      positionClass = hinge === 'side-left' ? 'side-right' : 'side-left';
    } else {
      positionClass = (opening === 'top-hung' || opening === 'tilt-turn')
        ? 'bottom-center'
        : 'top-center';
    }

    handle.className = `handle ${positionClass}`;
    return handle;
  }

  /**
   * Hjälpfunktion för att skapa SVG-element
   * @param {string} tag - SVG-tagg
   * @param {Object} attrs - Attribut för elementet
   * @returns {SVGElement} - Skapat SVG-element
   */
  function createSVG(tag, attrs) {
    const ns = 'http://www.w3.org/2000/svg';
    const el = document.createElementNS(ns, tag);

    for (const [key, value] of Object.entries(attrs)) {
      el.setAttribute(key, value);
    }

    // Lägg till standardattribut för linjer och sökvägar
    if (tag === 'line' || tag === 'path') {
      // Lägg till standardbredd om inte specificerad
      if (!attrs['stroke-width']) {
        el.setAttribute('stroke-width', '2');
      }

      // Lägg till vector-effect för konsekvent linjebredd oavsett skalning
      if (!attrs['vector-effect']) {
        el.setAttribute('vector-effect', 'non-scaling-stroke');
      }

      // Lägg till stroke-color om inte specificerad
      if (!attrs['stroke']) {
        el.setAttribute('stroke', 'rgba(180, 180, 180, 0.7)');
      }
    }

    return el;
  }
})();
