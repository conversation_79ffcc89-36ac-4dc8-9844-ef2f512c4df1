/**
 * door-preview-renderer.js
 * <PERSON><PERSON>r rendering av SVG-linjer för dörrkonfiguratorn
 */

(() => {
  // Initialisera när DOM är laddad
  document.addEventListener('DOMContentLoaded', () => {
    // Exponera funktioner globalt
    window.DoorPreviewRenderer = {
      renderOpeningLines
    };
  });

  /**
   * Renderar öppningslinjer för en dörr
   * @param {string} doorId - ID för dörrelementet
   * @param {Array} openings - Array med öppningstyper
   */
  function renderOpeningLines(doorId, openings) {
    // Hitta dörr-preview-elementet
    const doorPreview = document.getElementById(doorId);
    if (!doorPreview) {
      console.error(`Door preview element with ID "${doorId}" not found`);
      return;
    }

    // Logga information för felsökning
    console.log(`Rendering door opening lines for ${doorId}`, {
      doorPreview: doorPreview,
      openings: openings,
      isSavedPreview: doorId.startsWith('saved-preview-')
    });

    // Ta bort befintliga SVG-element
    const existingSvgs = doorPreview.querySelectorAll('.door-svg-container');
    existingSvgs.forEach(svg => svg.remove());

    // Skapa SVG-linjer för varje dörr
    const doorFrames = doorPreview.querySelectorAll('.door-frame');

    if (doorFrames.length === 0) {
      console.warn(`No door frames found in preview with ID "${doorId}"`);
      return; // Avbryt om inga dörrar hittades
    }

    console.log(`Found ${doorFrames.length} door frames in preview with ID "${doorId}"`);

    // Kontrollera att vi har rätt antal öppningar
    if (!openings || openings.length === 0) {
      console.error(`No openings provided for preview with ID "${doorId}"`);
      return;
    }

    if (openings.length !== doorFrames.length) {
      console.warn(`Mismatch between number of openings (${openings.length}) and door frames (${doorFrames.length})`);
    }

    // Rendera öppningslinjer för varje dörr
    doorFrames.forEach((frame, index) => {
      // Kontrollera att vi har en giltig öppningstyp för denna dörr
      if (index >= openings.length) {
        console.warn(`No opening type for door frame at index ${index}`);
        return;
      }

      if (!openings[index] || openings[index] === 'fixed') {
        console.log(`Skipping fixed door at index ${index}`);
        return; // Inga linjer för fasta dörrar
      }

      // Skapa SVG-container
      const svgContainer = document.createElement('div');
      svgContainer.className = 'door-svg-container';
      svgContainer.style.position = 'absolute';
      svgContainer.style.top = '0';
      svgContainer.style.left = '0';
      svgContainer.style.width = '100%';
      svgContainer.style.height = '100%';
      svgContainer.style.pointerEvents = 'none';

      // Skapa SVG-element
      const svg = createSVG('svg', {
        viewBox: '0 0 100 100',
        preserveAspectRatio: 'none',
        width: '100%',
        height: '100%'
      });

      // Lägg till öppningslinjer baserat på öppningstyp
      if (openings[index] === 'side-left' || openings[index] === 'side-right') {
        // Korrigera riktningen: om det är högerhängt (side-right), ska triangeln peka åt vänster
        // och om det är vänsterhängt (side-left), ska triangeln peka åt höger
        const isLeft = openings[index] === 'side-left';

        // Ändra riktningen på triangeln - för att få triangeln att peka åt motsatt håll
        // jämfört med gångjärnet, inverterar vi px och ox
        const px = isLeft ? 100 : 0; // Inverterad jämfört med tidigare
        const ox = 100 - px;

        // Skapa linjer för sidohängd dörr med samma färg som fönsterlinjerna
        const line1 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 0,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)', // Samma färg som fönsterlinjerna
          'vector-effect': 'non-scaling-stroke'
        });

        const line2 = createSVG('line', {
          x1: px,
          y1: 50,
          x2: ox,
          y2: 100,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)', // Samma färg som fönsterlinjerna
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(line1);
        svg.appendChild(line2);
      } else if (openings[index].startsWith('sliding')) {
        // Skapa en pil som pekar från handtaget in mot mitten
        // Riktningen beror på om det är från vänster eller höger
        const isFromRight = openings[index] === 'sliding' || openings[index] === 'sliding-right';

        // Koordinater för pilen baserat på riktning
        const startX = isFromRight ? 80 : 20; // Startpunkt (där handtaget är)
        const endX = 50; // Mittpunkt

        // Huvudlinjen för pilen
        const arrowLine = createSVG('line', {
          x1: startX,
          y1: 50, // Mitten av dörren
          x2: endX, // Mot mitten
          y2: 50,
          'stroke-width': '2',
          'stroke': 'rgba(180, 180, 180, 0.7)',
          'vector-effect': 'non-scaling-stroke'
        });

        svg.appendChild(arrowLine);

        // Skapa en enkel pil (en linje) som pekar från handtaget mot mitten
        if (isFromRight) {
          // Från höger mot mitten
          const arrow = createSVG('polyline', {
            points: '50,50 55,45 55,55 50,50',
            'fill': 'rgba(180, 180, 180, 0.7)',
            'stroke': 'rgba(180, 180, 180, 0.7)',
            'stroke-width': '2',
            'vector-effect': 'non-scaling-stroke'
          });
          svg.appendChild(arrow);
        } else {
          // Från vänster mot mitten
          const arrow = createSVG('polyline', {
            points: '50,50 45,45 45,55 50,50',
            'fill': 'rgba(180, 180, 180, 0.7)',
            'stroke': 'rgba(180, 180, 180, 0.7)',
            'stroke-width': '2',
            'vector-effect': 'non-scaling-stroke'
          });
          svg.appendChild(arrow);
        }
      }

      svgContainer.appendChild(svg);

      // Kontrollera om detta är en sparad design (saved-preview) eller vanlig preview
      const isSavedPreview = doorId.startsWith('saved-preview-');

      try {
        // Beräkna position för SVG-containern baserat på dörrens position
        const frameRect = frame.getBoundingClientRect();
        const previewRect = doorPreview.getBoundingClientRect();

        // Justera positionen relativt till förhandsvisningen
        if (doorPreview.contains(frame)) {
          // Om dörren är inuti förhandsvisningen, använd relativ positionering
          const offsetLeft = frame.offsetLeft;
          const offsetTop = frame.offsetTop;

          // Lägg till SVG-containern direkt i dörren för bättre positionering
          svgContainer.style.position = 'absolute';
          svgContainer.style.left = '0';
          svgContainer.style.top = '0';
          svgContainer.style.width = '100%';
          svgContainer.style.height = '100%';

          console.log(`Adding SVG container to door frame at index ${index}`);

          // Lägg till SVG-containern i dörren istället för i förhandsvisningen
          frame.appendChild(svgContainer);
        } else {
          // Fallback: lägg till i förhandsvisningen om något går fel
          console.warn('Door frame not properly contained in preview, using fallback positioning');
          svgContainer.style.position = 'absolute';
          svgContainer.style.left = '0';
          svgContainer.style.top = '0';
          svgContainer.style.width = '100%';
          svgContainer.style.height = '100%';
          doorPreview.appendChild(svgContainer);
        }
      } catch (error) {
        console.error('Error positioning SVG container:', error);
        // Fallback: lägg till i dörren med standardpositionering
        svgContainer.style.position = 'absolute';
        svgContainer.style.left = '0';
        svgContainer.style.top = '0';
        svgContainer.style.width = '100%';
        svgContainer.style.height = '100%';
        frame.appendChild(svgContainer);
      }
    });
  }

  /**
   * Hjälpfunktion för att skapa SVG-element
   * @param {string} tag - SVG-tagg
   * @param {Object} attrs - Attribut för elementet
   * @returns {SVGElement} - Skapat SVG-element
   */
  function createSVG(tag, attrs) {
    const ns = 'http://www.w3.org/2000/svg';
    const el = document.createElementNS(ns, tag);

    for (const [key, value] of Object.entries(attrs)) {
      el.setAttribute(key, value);
    }

    return el;
  }
})();
