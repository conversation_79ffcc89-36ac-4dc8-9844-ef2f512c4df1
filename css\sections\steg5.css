/* Steg 5 - <PERSON><PERSON> glas */

/* Beskrivningstext för steget */
.section-body > p {
  background-color: rgba(245, 199, 0, 0.1);
  border-left: 3px solid var(--color-primary);
  padding: 0.8rem 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0 var(--radius) var(--radius) 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--color-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.glass-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  max-width: 95%;
}

.glass-option-btn {
  background: var(--color-panel);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: pointer;
  font-size: 1rem;
  color: var(--color-secondary);
  transition: all var(--transition);
  text-align: left;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.glass-option-btn:hover {
  background: rgba(245, 199, 0, 0.05);
  border-color: var(--color-primary);
}

.glass-option-btn.active {
  background: transparent;
  color: var(--color-secondary);
  border: 2px solid var(--color-primary);
  box-shadow: 0 0 0 1px rgba(245, 199, 0, 0.5);
}

/* Beskrivningen har alltid samma färg */

/* Removed radio button style */

/* Glass icon */
.glass-option-btn::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 2rem;
  height: 2rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.7;
  transition: all var(--transition);
}

.glass-option-btn[data-value="double"]::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2'/%3E%3Cline x1='12' y1='3' x2='12' y2='21'/%3E%3C/svg%3E");
}

.glass-option-btn[data-value="triple"]::after {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2'/%3E%3Cline x1='8' y1='3' x2='8' y2='21'/%3E%3Cline x1='16' y1='3' x2='16' y2='21'/%3E%3C/svg%3E");
}

.glass-option-btn.active::after {
  opacity: 1;
}

/* Description text */
.glass-option-btn .option-description {
  display: block;
  font-size: 0.85rem;
  font-weight: normal;
  margin-top: 0.5rem;
  color: var(--color-secondary);
  line-height: 1.4;
}
