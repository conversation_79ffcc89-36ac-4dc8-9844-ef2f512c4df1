<?php
namespace IsofonsterConfigurator;

use Elementor\Widget_Base;
use Elementor\Controls_Manager;
use Elementor\Group_Control_Typography;
use Elementor\Group_Control_Background;
use Elementor\Group_Control_Border;
use Elementor\Group_Control_Box_Shadow;

/**
 * Elementor Widget för Isofönster Konfigurator
 */
class ElementorWidget extends Widget_Base {
    
    /**
     * Widget namn
     */
    public function get_name() {
        return 'isofonster-configurator';
    }
    
    /**
     * Widget titel
     */
    public function get_title() {
        return __('Isofönster Konfigurator', 'isofonster-configurator');
    }
    
    /**
     * Widget ikon
     */
    public function get_icon() {
        return 'eicon-gallery-grid';
    }
    
    /**
     * Widget kategorier
     */
    public function get_categories() {
        return ['isofonster'];
    }
    
    /**
     * Widget keywords
     */
    public function get_keywords() {
        return ['isofonster', 'konfigurator', 'fönster', 'dörr', 'window', 'door'];
    }
    
    /**
     * Registrera kontroller
     */
    protected function register_controls() {
        
        // Innehåll sektion
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Konfigurator Inställningar', 'isofonster-configurator'),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );
        
        $this->add_control(
            'configurator_type',
            [
                'label' => __('Konfigurator Typ', 'isofonster-configurator'),
                'type' => Controls_Manager::SELECT,
                'default' => 'both',
                'options' => [
                    'both' => __('Både fönster och dörr', 'isofonster-configurator'),
                    'window' => __('Endast fönster', 'isofonster-configurator'),
                    'door' => __('Endast dörr', 'isofonster-configurator'),
                ],
            ]
        );
        
        $this->add_control(
            'show_header',
            [
                'label' => __('Visa rubrik', 'isofonster-configurator'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => __('Ja', 'isofonster-configurator'),
                'label_off' => __('Nej', 'isofonster-configurator'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->add_control(
            'header_text',
            [
                'label' => __('Rubriktext', 'isofonster-configurator'),
                'type' => Controls_Manager::TEXT,
                'default' => __('Fönster Konfigurator', 'isofonster-configurator'),
                'condition' => [
                    'show_header' => 'yes',
                ],
            ]
        );
        
        $this->add_control(
            'show_navigation',
            [
                'label' => __('Visa navigation', 'isofonster-configurator'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => __('Ja', 'isofonster-configurator'),
                'label_off' => __('Nej', 'isofonster-configurator'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->add_control(
            'enable_save',
            [
                'label' => __('Aktivera sparfunktion', 'isofonster-configurator'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => __('Ja', 'isofonster-configurator'),
                'label_off' => __('Nej', 'isofonster-configurator'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->add_control(
            'enable_export',
            [
                'label' => __('Aktivera PDF export', 'isofonster-configurator'),
                'type' => Controls_Manager::SWITCHER,
                'label_on' => __('Ja', 'isofonster-configurator'),
                'label_off' => __('Nej', 'isofonster-configurator'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->end_controls_section();
        
        // Stil sektion - Container
        $this->start_controls_section(
            'style_container',
            [
                'label' => __('Container', 'isofonster-configurator'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );
        
        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'container_background',
                'label' => __('Bakgrund', 'isofonster-configurator'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .isofonster-configurator-container',
            ]
        );
        
        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'container_border',
                'label' => __('Ram', 'isofonster-configurator'),
                'selector' => '{{WRAPPER}} .isofonster-configurator-container',
            ]
        );
        
        $this->add_control(
            'container_border_radius',
            [
                'label' => __('Rundade hörn', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-container' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_group_control(
            Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'container_box_shadow',
                'label' => __('Skugga', 'isofonster-configurator'),
                'selector' => '{{WRAPPER}} .isofonster-configurator-container',
            ]
        );
        
        $this->add_responsive_control(
            'container_padding',
            [
                'label' => __('Padding', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-container' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_responsive_control(
            'container_margin',
            [
                'label' => __('Margin', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-container' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->end_controls_section();
        
        // Stil sektion - Rubrik
        $this->start_controls_section(
            'style_header',
            [
                'label' => __('Rubrik', 'isofonster-configurator'),
                'tab' => Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_header' => 'yes',
                ],
            ]
        );
        
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'header_typography',
                'label' => __('Typografi', 'isofonster-configurator'),
                'selector' => '{{WRAPPER}} .isofonster-configurator-header h1',
            ]
        );
        
        $this->add_control(
            'header_color',
            [
                'label' => __('Textfärg', 'isofonster-configurator'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-header h1' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'header_background',
                'label' => __('Bakgrund', 'isofonster-configurator'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .isofonster-configurator-header',
            ]
        );
        
        $this->add_responsive_control(
            'header_padding',
            [
                'label' => __('Padding', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-header' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_responsive_control(
            'header_margin',
            [
                'label' => __('Margin', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator-header' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->end_controls_section();
        
        // Stil sektion - Knappar
        $this->start_controls_section(
            'style_buttons',
            [
                'label' => __('Knappar', 'isofonster-configurator'),
                'tab' => Controls_Manager::TAB_STYLE,
            ]
        );
        
        $this->add_group_control(
            Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'label' => __('Typografi', 'isofonster-configurator'),
                'selector' => '{{WRAPPER}} .isofonster-configurator .btn',
            ]
        );
        
        $this->start_controls_tabs('button_style_tabs');
        
        $this->start_controls_tab(
            'button_normal_tab',
            [
                'label' => __('Normal', 'isofonster-configurator'),
            ]
        );
        
        $this->add_control(
            'button_color',
            [
                'label' => __('Textfärg', 'isofonster-configurator'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator .btn' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'button_background',
                'label' => __('Bakgrund', 'isofonster-configurator'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .isofonster-configurator .btn',
            ]
        );
        
        $this->end_controls_tab();
        
        $this->start_controls_tab(
            'button_hover_tab',
            [
                'label' => __('Hover', 'isofonster-configurator'),
            ]
        );
        
        $this->add_control(
            'button_hover_color',
            [
                'label' => __('Textfärg', 'isofonster-configurator'),
                'type' => Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator .btn:hover' => 'color: {{VALUE}};',
                ],
            ]
        );
        
        $this->add_group_control(
            Group_Control_Background::get_type(),
            [
                'name' => 'button_hover_background',
                'label' => __('Bakgrund', 'isofonster-configurator'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .isofonster-configurator .btn:hover',
            ]
        );
        
        $this->end_controls_tab();
        
        $this->end_controls_tabs();
        
        $this->add_group_control(
            Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'label' => __('Ram', 'isofonster-configurator'),
                'selector' => '{{WRAPPER}} .isofonster-configurator .btn',
            ]
        );
        
        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Rundade hörn', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator .btn' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->add_responsive_control(
            'button_padding',
            [
                'label' => __('Padding', 'isofonster-configurator'),
                'type' => Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .isofonster-configurator .btn' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );
        
        $this->end_controls_section();
    }
    
    /**
     * Rendera widget
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        // Inkludera template
        include ISOFONSTER_CONFIGURATOR_PLUGIN_PATH . 'templates/elementor-widget.php';
    }
    
    /**
     * Rendera widget för editor
     */
    protected function content_template() {
        ?>
        <div class="isofonster-configurator-container">
            <# if ( settings.show_header === 'yes' ) { #>
                <header class="isofonster-configurator-header">
                    <h1>{{{ settings.header_text }}}</h1>
                </header>
            <# } #>
            <div class="isofonster-configurator-preview">
                <p><?php _e('Konfigurator kommer att visas här på frontend', 'isofonster-configurator'); ?></p>
            </div>
        </div>
        <?php
    }
}
