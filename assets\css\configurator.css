/**
 * Isof<PERSON><PERSON>er Konfigurator - Huvudstilmall
 * Konsoliderad CSS för WordPress Plugin
 * Version: 1.0.0
 */

/* ===== CSS VARIABLER ===== */
:root {
  /* Färgpalett enligt Isofönster */
  --isofonster-color-bg: #121212;
  --isofonster-color-panel: #1e1e1e;
  --isofonster-color-primary: #f5c700;
  --isofonster-color-primary-dark: #d4b300;
  --isofonster-color-secondary: #e0e0e0;
  --isofonster-color-border: #2a2a2a;
  --isofonster-radius: 6px;
  --isofonster-transition: 0.2s ease-in-out;
  --isofonster-font-base: 'Segoe UI', sans-serif;
  --isofonster-header-height: 60px;
}

/* ===== RESET OCH GRUNDLÄGGANDE STILAR ===== */
.isofonster-configurator-container * {
  box-sizing: border-box;
}

.isofonster-configurator-container {
  font-family: var(--isofonster-font-base);
  background: var(--isofonster-color-bg);
  color: var(--isofonster-color-secondary);
  line-height: 1.5;
  position: relative;
  width: 100%;
  min-height: 600px;
}

/* Ta bort spinner-pilar på number-input */
.isofonster-configurator-container input[type="number"]::-webkit-inner-spin-button,
.isofonster-configurator-container input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.isofonster-configurator-container input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* ===== HEADER ===== */
.isofonster-configurator-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: var(--isofonster-color-panel);
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.5);
  margin-bottom: 1rem;
}

.isofonster-configurator-header h1 {
  font-size: 1.75rem;
  color: var(--isofonster-color-primary);
  margin: 0;
}

/* ===== NAVIGATION ===== */
.isofonster-top-nav {
  display: flex;
  gap: 1rem;
  background: var(--isofonster-color-panel);
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: var(--isofonster-radius);
}

.isofonster-top-nav button {
  background: none;
  border: none;
  color: var(--isofonster-color-secondary);
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-radius: var(--isofonster-radius);
  transition: background var(--isofonster-transition);
  font-family: inherit;
  font-size: 1rem;
}

.isofonster-top-nav button:hover {
  background: rgba(245,199,0,0.1);
}

.isofonster-top-nav button.active {
  background: var(--isofonster-color-primary);
  color: #121212;
}

/* ===== SEKTIONER ===== */
.isofonster-section {
  width: 100%;
}

.isofonster-section.hidden {
  display: none;
}

/* ===== KONFIGURATOR-VAL ===== */
.isofonster-configurator-selection {
  text-align: center;
  padding: 2rem;
}

.isofonster-configurator-selection h2 {
  color: var(--isofonster-color-primary);
  margin-bottom: 2rem;
  font-size: 2rem;
}

.isofonster-configurator-options {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.isofonster-configurator-option {
  background: var(--isofonster-color-panel);
  border: 2px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 2rem;
  max-width: 300px;
  text-align: center;
  transition: all var(--isofonster-transition);
}

.isofonster-configurator-option:hover {
  border-color: var(--isofonster-color-primary);
  transform: translateY(-2px);
}

.isofonster-configurator-option .isofonster-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.isofonster-configurator-option h3 {
  color: var(--isofonster-color-primary);
  margin-bottom: 1rem;
}

.isofonster-configurator-option p {
  margin-bottom: 1.5rem;
  color: var(--isofonster-color-secondary);
}

/* ===== KNAPPAR ===== */
.isofonster-configurator-container .btn {
  background: var(--isofonster-color-primary);
  color: #121212;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--isofonster-radius);
  cursor: pointer;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  transition: all var(--isofonster-transition);
  text-decoration: none;
  display: inline-block;
}

.isofonster-configurator-container .btn:hover {
  background: var(--isofonster-color-primary-dark);
  transform: translateY(-1px);
}

.isofonster-configurator-container .btn:active {
  transform: translateY(0);
}

.isofonster-configurator-container .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== STEG-NAVIGERING ===== */
.isofonster-step-nav-wrapper {
  margin-bottom: 1.5rem;
  overflow-x: auto;
}

.isofonster-step-nav {
  display: flex;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--isofonster-color-panel);
  border-radius: var(--isofonster-radius);
  min-width: max-content;
}

.isofonster-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  border-radius: var(--isofonster-radius);
  cursor: pointer;
  transition: all var(--isofonster-transition);
  min-width: 80px;
  text-align: center;
}

.isofonster-step:hover {
  background: rgba(245,199,0,0.1);
}

.isofonster-step.active {
  background: var(--isofonster-color-primary);
  color: #121212;
}

.isofonster-step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--isofonster-color-border);
  color: var(--isofonster-color-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.isofonster-step.active .isofonster-step-number {
  background: #121212;
  color: var(--isofonster-color-primary);
}

.isofonster-step-title {
  font-size: 0.75rem;
  font-weight: 500;
}

/* ===== BYGGARE-CONTAINER ===== */
.isofonster-builder-container {
  display: flex;
  gap: 1.5rem;
  width: 100%;
}

.isofonster-controls-column {
  flex: 0 0 40%;
  max-width: 40%;
}

.isofonster-result-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* ===== BYGGARE-SEKTIONER ===== */
.isofonster-builder-section {
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  margin-bottom: 1rem;
  overflow: hidden;
}

.isofonster-builder-section h2 {
  background: var(--isofonster-color-border);
  color: var(--isofonster-color-primary);
  padding: 1rem;
  margin: 0;
  font-size: 1.125rem;
  cursor: pointer;
  position: relative;
  transition: background var(--isofonster-transition);
}

.isofonster-builder-section h2:hover {
  background: #2a2a2a;
}

.isofonster-builder-section h2::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 0.8rem;
  height: 0.8rem;
  border-right: 2px solid var(--isofonster-color-secondary);
  border-bottom: 2px solid var(--isofonster-color-secondary);
  transition: transform 0.3s ease;
}

.isofonster-builder-section.collapsed h2::after {
  transform: translateY(-50%) rotate(-135deg);
}

.isofonster-section-body {
  padding: 1.5rem;
  display: block;
}

.isofonster-builder-section.collapsed .isofonster-section-body {
  display: none;
}

/* ===== DIMENSIONER ===== */
.isofonster-dimension-section .isofonster-dim-row {
  margin-bottom: 1.5rem;
}

.isofonster-dim-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.isofonster-dim-value {
  color: var(--isofonster-color-primary);
  font-weight: 700;
}

.isofonster-dim-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  background: var(--isofonster-color-bg);
  color: var(--isofonster-color-secondary);
  font-family: inherit;
  font-size: 1rem;
  transition: border-color var(--isofonster-transition);
}

.isofonster-dim-input:focus {
  outline: none;
  border-color: var(--isofonster-color-primary);
}

.isofonster-dim-hint {
  font-size: 0.875rem;
  color: #999;
  margin-top: 0.25rem;
}

/* ===== ANTAL KONTROLLER ===== */
.isofonster-count-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.isofonster-count-btn {
  width: 40px;
  height: 40px;
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  background: var(--isofonster-color-bg);
  color: var(--isofonster-color-secondary);
  font-size: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--isofonster-transition);
}

.isofonster-count-btn:hover {
  border-color: var(--isofonster-color-primary);
  background: var(--isofonster-color-primary);
  color: #121212;
}

.isofonster-count-input {
  width: 80px;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  background: var(--isofonster-color-bg);
  color: var(--isofonster-color-secondary);
  font-family: inherit;
  font-size: 1rem;
}

.isofonster-count-hint {
  text-align: center;
  font-size: 0.875rem;
  color: #999;
}

/* ===== FÖRHANDSVISNING ===== */
.isofonster-preview-container {
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 1.5rem;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.isofonster-preview {
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(60, 60, 60, 0.95);
  border: 10px solid rgba(50, 50, 50, 0.95);
  border-radius: var(--isofonster-radius);
  position: relative;
}

/* ===== 3D FÖRHANDSVISNING ===== */
.isofonster-3d-preview-container {
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 1rem;
  min-height: 300px;
}

.isofonster-3d-preview {
  width: 100%;
  height: 250px;
  border-radius: var(--isofonster-radius);
  overflow: hidden;
}

/* ===== SPECIFIKATIONSTABELL ===== */
.isofonster-spec-container {
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 1.5rem;
  margin-top: 1rem;
}

.isofonster-spec-container h3 {
  color: var(--isofonster-color-primary);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.isofonster-spec-table {
  width: 100%;
  border-collapse: collapse;
}

.isofonster-spec-table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--isofonster-color-border);
}

.isofonster-spec-table td:first-child {
  font-weight: 600;
  color: var(--isofonster-color-secondary);
  width: 40%;
}

.isofonster-spec-table td:last-child {
  color: var(--isofonster-color-primary);
}

/* ===== SPARADE DESIGNS ===== */
.isofonster-saved-section {
  display: flex;
  gap: 1.5rem;
  min-height: 600px;
}

.isofonster-saved-sidebar {
  flex: 0 0 300px;
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 1rem;
}

.isofonster-saved-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.isofonster-search-input {
  flex: 1;
  padding: 0.6rem;
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  background: var(--isofonster-color-bg);
  color: var(--isofonster-color-secondary);
  font-family: inherit;
  transition: border-color var(--isofonster-transition);
}

.isofonster-search-input:focus {
  border-color: var(--isofonster-color-primary);
  outline: none;
}

.isofonster-sort-btn {
  padding: 0.6rem 1rem;
  border: none;
  border-radius: var(--isofonster-radius);
  background: var(--isofonster-color-primary);
  color: #121212;
  cursor: pointer;
  font-family: inherit;
  transition: background var(--isofonster-transition);
}

.isofonster-sort-btn:hover {
  background: var(--isofonster-color-primary-dark);
}

.isofonster-saved-detail {
  flex: 1;
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.isofonster-placeholder-text {
  color: #999;
  font-style: italic;
  text-align: center;
}

/* ===== EXPORT ===== */
.isofonster-export-section {
  background: var(--isofonster-color-panel);
  border: 1px solid var(--isofonster-color-border);
  border-radius: var(--isofonster-radius);
  padding: 2rem;
  text-align: center;
}

.isofonster-export-section h2 {
  color: var(--isofonster-color-primary);
  margin-bottom: 1rem;
}

.isofonster-export-section p {
  margin-bottom: 2rem;
  color: var(--isofonster-color-secondary);
}

.isofonster-export-controls {
  margin-bottom: 2rem;
}

.isofonster-progress-container {
  max-width: 400px;
  margin: 0 auto;
}

.isofonster-progress-bar {
  width: 100%;
  height: 8px;
  background: var(--isofonster-color-border);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.isofonster-progress-fill {
  height: 100%;
  background: var(--isofonster-color-primary);
  width: 0%;
  transition: width 0.3s ease;
}

.isofonster-progress-status {
  color: var(--isofonster-color-secondary);
  font-size: 0.875rem;
}

/* ===== RESPONSIV DESIGN ===== */
@media (max-width: 768px) {
  .isofonster-builder-container {
    flex-direction: column;
  }

  .isofonster-controls-column {
    flex: none;
    max-width: none;
  }

  .isofonster-saved-section {
    flex-direction: column;
  }

  .isofonster-saved-sidebar {
    flex: none;
  }

  .isofonster-step-nav {
    justify-content: flex-start;
  }

  .isofonster-configurator-options {
    flex-direction: column;
    align-items: center;
  }
}

/* ===== UTILITY KLASSER ===== */
.hidden {
  display: none !important;
}

.isofonster-text-center {
  text-align: center;
}

.isofonster-mb-1 {
  margin-bottom: 1rem;
}

.isofonster-mt-1 {
  margin-top: 1rem;
}
