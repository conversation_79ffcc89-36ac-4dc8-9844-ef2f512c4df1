/**
 * door-configurator.js
 * Huvudskript för dörrkonfiguratorn
 */

document.addEventListener('DOMContentLoaded', () => {
  // ─── REFERENSER FÖR DÖRRKONFIGURATORN ───────────────────
  const doorSections = Array.from(document.querySelectorAll('.door-configurator .builder-section'));
  const doorPreviewEl = document.getElementById('doorPreview');
  const doorSpecBody = document.querySelector('#doorSpecTable tbody');
  const doorStepBtns = document.querySelectorAll('.door-configurator .step-nav .step');
  const doorWidthInput = document.getElementById('doorWidthInput');
  const doorHeightInput = document.getElementById('doorHeightInput');
  const doorWidthLabel = document.getElementById('doorWidthLabel');
  const doorHeightLabel = document.getElementById('doorHeightLabel');
  const panelHeightSlider = document.getElementById('panelHeightSlider');
  const panelHeightInput = document.getElementById('panelHeightInput');
  const panelHeightLabel = document.getElementById('panelHeightLabel');
  const doorSaveBtn = document.querySelector('.door-configurator .save-btn');
  const PIXELS_PER_CM = 2;

  // ─── GLOBALA VARIABLER ───────────────────────────────
  let doorWidthCm = +doorWidthInput.value;
  let doorHeightCm = +doorHeightInput.value;
  let doorCount = 1;
  let doorOpenings = ['side-left']; // Vänsterhängd som standard
  let doorOpenSizes = [doorWidthCm];
  let panelHeightCm = +panelHeightInput.value;
  let doorEditingIndex = null;

  // Exponera variabler globalt för att kunna användas av andra skript
  window.doorWidthCm = doorWidthCm;
  window.doorHeightCm = doorHeightCm;
  window.doorCount = doorCount;
  window.doorOpenings = doorOpenings;
  window.doorOpenSizes = doorOpenSizes;
  window.panelHeightCm = panelHeightCm;
  window.doorEditingIndex = doorEditingIndex;

  // Globala variabler för val
  window.doorSelectedMaterial = null;
  window.doorSelectedGlassType = 'normal';
  window.doorSelectedInsideColor = 'white';
  window.doorSelectedOutsideColor = 'white';

  // ─── HJÄLPFUNKTIONER ───────────────────────────────
  const clamp = (v, min, max) => v < min ? min : v > max ? max : v;

  // ─── MATERIAL-ETIKETTER ───────────────────────────────
  const DOOR_MATERIAL_LABELS = {
    tra: 'Trä',
    pvc: 'PVC',
    aluminium: 'Aluminium',
    'tra-aluminium': 'Trä/Aluminium',
    'pvc-aluminium': 'PVC/Aluminium'
  };

  // ─── GLASTYP-ETIKETTER ───────────────────────────────
  window.DOOR_GLASS_TYPE_LABELS = {
    'normal': 'Vanligt glas',
    'tempered': 'Härdat glas',
    'laminated': 'Laminerat glas'
  };

  // ─── FÄRG-ETIKETTER ───────────────────────────────
  window.DOOR_COLOR_LABELS = {
    'white': 'Vit',
    'gray': 'Grå',
    'black': 'Svart',
    'anthracite': 'Antracitgrå',
    'darkgray': 'Mörkgrå',
    'brown': 'Brun'
  };

  // ─── DIMENSIONER ────────────────────────────────────
  doorWidthInput.addEventListener('input', () => {
    doorWidthCm = clamp(+doorWidthInput.value, +doorWidthInput.min, +doorWidthInput.max);
    window.doorWidthCm = doorWidthCm; // Uppdatera global variabel
    doorWidthLabel.textContent = `${doorWidthCm} cm`;
    redistributeDoorSizes();

    // Uppdatera öppning per luft-sektionen om den är synlig
    const openingSection = document.getElementById('door-section-2');
    if (openingSection && !openingSection.classList.contains('collapsed')) {
      buildDoorOpeningControls();
    }

    updateDoorAll();
  });

  doorHeightInput.addEventListener('input', () => {
    doorHeightCm = clamp(+doorHeightInput.value, +doorHeightInput.min, +doorHeightInput.max);
    window.doorHeightCm = doorHeightCm; // Uppdatera global variabel
    doorHeightLabel.textContent = `${doorHeightCm} cm`;
    updateDoorAll();
  });

  // ─── PANEL HÖJD ────────────────────────────────────
  panelHeightSlider.addEventListener('input', () => {
    panelHeightCm = +panelHeightSlider.value;
    window.panelHeightCm = panelHeightCm; // Uppdatera global variabel
    panelHeightInput.value = panelHeightCm;
    panelHeightLabel.textContent = `${panelHeightCm} cm`;
    updateDoorAll();
  });

  panelHeightInput.addEventListener('input', () => {
    panelHeightCm = clamp(+panelHeightInput.value, 0, 100);
    window.panelHeightCm = panelHeightCm; // Uppdatera global variabel
    panelHeightSlider.value = panelHeightCm;
    panelHeightLabel.textContent = `${panelHeightCm} cm`;
    updateDoorAll();
  });

  function redistributeDoorSizes() {
    // Beräkna basbredd för varje luft
    const base = Math.floor(doorWidthCm / doorCount);

    // Om vi redan har storlekar, försök bevara proportionerna
    if (doorOpenSizes && doorOpenSizes.length > 0 && doorOpenSizes.length === doorCount) {
      const oldTotal = doorOpenSizes.reduce((sum, size) => sum + size, 0);
      if (oldTotal > 0) {
        // Beräkna nya storlekar baserat på proportioner
        const newSizes = doorOpenSizes.map(size =>
          Math.floor((size / oldTotal) * doorWidthCm)
        );

        // Justera för avrundningsfel
        let sum = newSizes.reduce((a, b) => a + b, 0);
        let diff = doorWidthCm - sum;

        // Lägg till eller dra bort differensen från sista luften
        if (diff !== 0) {
          newSizes[doorCount - 1] += diff;
        }

        // Säkerställ att ingen luft är mindre än 10cm
        const minSize = 10;
        let needsRebalance = false;

        for (let i = 0; i < newSizes.length; i++) {
          if (newSizes[i] < minSize) {
            needsRebalance = true;
            break;
          }
        }

        if (!needsRebalance) {
          doorOpenSizes = newSizes;
          window.doorOpenSizes = doorOpenSizes; // Uppdatera global variabel
          return;
        }
      }
    }

    // Fallback: Jämn fördelning om vi inte kan bevara proportioner
    doorOpenSizes = Array(doorCount).fill(base);
    doorOpenSizes[doorCount - 1] = doorWidthCm - base * (doorCount - 1);
    window.doorOpenSizes = doorOpenSizes; // Uppdatera global variabel
  }

  // ─── ANTAL LUFT ─────────────────────────────────────
  const doorQtyBtns = document.querySelectorAll('.door-qty-option-btn');
  const doorCustomCtrl = document.querySelector('.door-configurator .custom-control');
  const doorCustomIn = document.getElementById('doorCustomCount');
  const doorDecBtn = document.getElementById('doorDecrement');
  const doorIncBtn = document.getElementById('doorIncrement');

  doorQtyBtns.forEach(btn => btn.addEventListener('click', () => {
    doorQtyBtns.forEach(b => b.classList.remove('active'));
    btn.classList.add('active');
    const v = btn.dataset.value;
    if (v === 'custom') {
      doorCustomCtrl.classList.remove('hidden');
      updateDoorCount(+doorCustomIn.value);
    } else {
      doorCustomCtrl.classList.add('hidden');
      updateDoorCount(+v);
    }
  }));

  doorDecBtn.addEventListener('click', () => updateDoorCount(doorCount - 1));
  doorIncBtn.addEventListener('click', () => updateDoorCount(doorCount + 1));
  doorCustomIn.addEventListener('input', () => updateDoorCount(+doorCustomIn.value));

  function updateDoorCount(v) {
    doorCount = clamp(v || 1, 1, 3);
    window.doorCount = doorCount; // Uppdatera global variabel
    doorCustomIn.value = doorCount;
    doorOpenings = Array(doorCount).fill('fixed'); // Fast som standard för alla lufter
    window.doorOpenings = doorOpenings; // Uppdatera global variabel
    redistributeDoorSizes();

    // Uppdatera öppning per luft-sektionen om den är synlig
    const openingSection = document.getElementById('door-section-2');
    if (openingSection && !openingSection.classList.contains('collapsed')) {
      buildDoorOpeningControls();
    }

    updateDoorAll();
  }

  // ─── STEG-NAVIGATION ─────────────────────────────────
  doorSections.forEach((sec, i) => {
    sec.querySelectorAll('.next-btn').forEach(b => b.addEventListener('click', () => doorGoTo(i + 1)));
    sec.querySelectorAll('.prev-btn').forEach(b => b.addEventListener('click', () => doorGoTo(i - 1)));
  });
  doorStepBtns.forEach(b => b.addEventListener('click', () => doorGoTo(+b.dataset.step)));

  function doorGoTo(i) {
    if (i < 0 || i >= doorSections.length) return;

    // Kontrollera om sektionen redan är öppen
    const isAlreadyOpen = !doorSections[i].classList.contains('collapsed');

    if (isAlreadyOpen) {
      // Om sektionen redan är öppen, stäng den och avaktivera alla
      doorSections.forEach(s => {
        s.classList.remove('expanded');
        s.classList.add('collapsed');
      });
      doorStepBtns.forEach(b => b.classList.remove('active'));
    } else {
      // Annars, öppna den valda sektionen och stäng övriga
      doorSections.forEach((s, idx) => {
        s.classList.toggle('expanded', idx === i);
        s.classList.toggle('collapsed', idx !== i);
      });
      doorStepBtns.forEach(b => b.classList.toggle('active', +b.dataset.step === i));

      // Scrolla till den öppnade sektionen
      if (doorSections[i]) {
        doorSections[i].scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Om vi är på steg 1 (dimensioner), uppdatera UI-element med aktuella värden
      if (i === 0) {
        // Uppdatera bredd och höjd från globala variabler
        doorWidthInput.value = window.doorWidthCm;
        doorHeightInput.value = window.doorHeightCm;
        doorWidthLabel.textContent = `${window.doorWidthCm} cm`;
        doorHeightLabel.textContent = `${window.doorHeightCm} cm`;

        // Uppdatera panelhöjd
        panelHeightSlider.value = window.panelHeightCm;
        panelHeightInput.value = window.panelHeightCm;
        panelHeightLabel.textContent = `${window.panelHeightCm} cm`;

        console.log(`Uppdaterar dimensioner: ${window.doorWidthCm}x${window.doorHeightCm} cm, panel: ${window.panelHeightCm} cm`);
      }

      // Om vi är på steg 2 (antal luft), uppdatera UI-element med aktuella värden
      if (i === 1) {
        // Aktivera rätt antal luft-knapp
        doorQtyBtns.forEach(btn => {
          const isCustom = btn.dataset.value === 'custom';
          const matches = +btn.dataset.value === window.doorCount;
          btn.classList.toggle('active', matches || (isCustom && !['1','2'].includes(window.doorCount.toString())));
        });

        // Visa/dölj anpassad inmatning för antal luft
        if (!['1','2'].includes(window.doorCount.toString())) {
          doorCustomCtrl.classList.remove('hidden');
          doorCustomIn.value = window.doorCount;
        } else {
          doorCustomCtrl.classList.add('hidden');
        }

        console.log(`Uppdaterar antal luft: ${window.doorCount}`);
      }
    }

    // Bygg öppningskontroller om vi är på steg 3
    if (i === 2) buildDoorOpeningControls();
  }

  // ─── ÖPPNING ───────────────────────────────────────
  function buildDoorOpeningControls() {
    const cont = document.getElementById('doorOpeningsContainer');
    cont.innerHTML = '';

    const types = [
      { value: 'fixed', label: 'Fast' },
      { value: 'side', label: 'Sidohängt' },
      { value: 'sliding', label: 'Skjutparti' }
    ];

    // Beräkna maximal bredd för varje luft, med hänsyn till att minst 10 cm måste finnas för varje annan luft
    const globalMax = doorWidthCm - 10 * (doorCount - 1);

    // Säkerställ att doorOpenSizes har rätt längd
    if (doorOpenSizes.length !== doorCount) {
      redistributeDoorSizes();
    }

    function recalcLast() {
      const used = doorOpenSizes.slice(0, doorCount - 1).reduce((a, b) => a + b, 0);
      doorOpenSizes[doorCount - 1] = doorWidthCm - used;
    }

    function refreshFields() {
      cont.querySelectorAll('.size-control').forEach((ctrl, j) => {
        const dec = ctrl.querySelector('.size-dec');
        const inc = ctrl.querySelector('.size-inc');
        const inp = ctrl.querySelector('.size-input');
        if (dec) dec.disabled = doorOpenSizes[j] <= 10;
        if (inc) inc.disabled = doorOpenSizes[j] >= globalMax;
        inp.value = doorOpenSizes[j];
        inp.max = globalMax;
      });
    }

    function changeSize(idx, delta) {
      doorOpenSizes[idx] = clamp(doorOpenSizes[idx] + delta, 10, globalMax);
      recalcLast();
      refreshFields();
      updateDoorAll();
    }

    // Bygg kontroller för varje luft
    for (let i = 0; i < doorCount; i++) {
      const wrap = document.createElement('div');
      wrap.className = 'opening-wrap';

      // Skapa HTML för typknappar
      // Hantera specialfall för fixed, side-left, side-right, sliding-left, sliding-right
      const typesHtml = types.map(opt => {
        let isActive = false;

        if (opt.value === 'fixed' && doorOpenings[i] === 'fixed') {
          isActive = true;
        } else if (opt.value === 'side' && (doorOpenings[i] === 'side-left' || doorOpenings[i] === 'side-right')) {
          isActive = true;
        } else if (opt.value === 'sliding' && doorOpenings[i].startsWith('sliding')) {
          isActive = true;
        }

        return `<button class="type-btn${isActive ? ' active' : ''}" data-value="${opt.value}">${opt.label}</button>`;
      }).join('');

      // Storlekskontroll
      let sizeHtml = `
        <div class="size-control">
          <label for="door_size_${i}">Bredd:</label>
          ${i < doorCount - 1
            ? `<button class="size-btn size-dec" data-index="${i}">−</button>
               <input type="number" id="door_size_${i}" class="size-input" min="10" max="${globalMax}" value="${doorOpenSizes[i]}">
               <button class="size-btn size-inc" data-index="${i}">+</button>`
            : `<input type="number" id="door_size_${i}" class="size-input" value="${doorOpenSizes[i]}" disabled>`
          }
          <span class="unit">cm</span>
        </div>
      `;

      // Skapa HTML för skjutpartiriktning (visas endast om skjutparti är valt)
      const slidingDirectionHtml = `
        <div class="sliding-direction-control" style="display: none; margin-top: 10px;">
          <div class="sliding-direction-label">Skjutriktning:</div>
          <div class="sliding-direction-options">
            <button class="sliding-direction-btn" data-direction="left">Från Vänster</button>
            <button class="sliding-direction-btn" data-direction="right">Från Höger</button>
          </div>
        </div>
      `;

      // Skapa HTML för sidohängd riktning (visas endast om sidohängt är valt)
      const sideDirectionHtml = `
        <div class="side-direction-control" style="display: none; margin-top: 10px;">
          <div class="side-direction-label">Hängriktning:</div>
          <div class="side-direction-options">
            <button class="side-direction-btn" data-direction="left">Vänster</button>
            <button class="side-direction-btn" data-direction="right">Höger</button>
          </div>
        </div>
      `;

      wrap.innerHTML = `
        <div class="opening-header">Luft ${i+1}</div>
        <div class="type-group">${typesHtml}</div>
        ${sideDirectionHtml}
        ${slidingDirectionHtml}
        ${sizeHtml}
      `;
      cont.appendChild(wrap);

      // Typ-knappar
      wrap.querySelectorAll('.type-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const value = btn.dataset.value;
          wrap.querySelectorAll('.type-btn').forEach(b => b.classList.toggle('active', b === btn));

          // Visa/dölj sidohängd riktning baserat på om sidohängt är valt
          const sideDirectionControl = wrap.querySelector('.side-direction-control');
          if (sideDirectionControl) {
            sideDirectionControl.style.display = value === 'side' ? 'block' : 'none';

            // Aktivera "Vänster" som standard om sidohängt väljs
            if (value === 'side') {
              doorOpenings[i] = 'side-left'; // Sätt standardvärde
              const leftBtn = sideDirectionControl.querySelector('[data-direction="left"]');
              if (leftBtn && !leftBtn.classList.contains('active')) {
                leftBtn.classList.add('active');
                wrap.querySelectorAll('.side-direction-btn').forEach(b =>
                  b.classList.toggle('active', b === leftBtn)
                );
              }
            } else {
              doorOpenings[i] = value; // För fixed och andra typer
            }
          }

          // Visa/dölj skjutpartiriktning baserat på om skjutparti är valt
          const slidingDirectionControl = wrap.querySelector('.sliding-direction-control');
          if (slidingDirectionControl) {
            slidingDirectionControl.style.display = value === 'sliding' ? 'block' : 'none';

            // Aktivera "Från Vänster" som standard om skjutparti väljs
            if (value === 'sliding') {
              doorOpenings[i] = 'sliding-left'; // Sätt standardvärde
              const leftBtn = slidingDirectionControl.querySelector('[data-direction="left"]');
              if (leftBtn && !leftBtn.classList.contains('active')) {
                leftBtn.classList.add('active');
                wrap.querySelectorAll('.sliding-direction-btn').forEach(b =>
                  b.classList.toggle('active', b === leftBtn)
                );
              }
            }
          }

          updateDoorAll();
        });
      });

      // Skjutpartiriktning-knappar
      wrap.querySelectorAll('.sliding-direction-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const direction = btn.dataset.direction;
          // Uppdatera doorOpenings med skjutriktning
          doorOpenings[i] = 'sliding-' + direction;

          // Uppdatera aktiv knapp
          wrap.querySelectorAll('.sliding-direction-btn').forEach(b =>
            b.classList.toggle('active', b === btn)
          );

          updateDoorAll();
        });
      });

      // Sidohängd riktning-knappar
      wrap.querySelectorAll('.side-direction-btn').forEach(btn => {
        btn.addEventListener('click', () => {
          const direction = btn.dataset.direction;
          // Uppdatera doorOpenings med hängriktning
          doorOpenings[i] = 'side-' + direction;

          // Uppdatera aktiv knapp
          wrap.querySelectorAll('.side-direction-btn').forEach(b =>
            b.classList.toggle('active', b === btn)
          );

          updateDoorAll();
        });
      });

      // Visa rätt kontroller baserat på aktuell öppningstyp
      if (doorOpenings[i].startsWith('side')) {
        // Visa sidohängd riktningskontroll
        const sideDirectionControl = wrap.querySelector('.side-direction-control');
        if (sideDirectionControl) {
          sideDirectionControl.style.display = 'block';
          const direction = doorOpenings[i] === 'side-left' ? 'left' : 'right';
          const dirBtn = sideDirectionControl.querySelector(`[data-direction="${direction}"]`);
          if (dirBtn) {
            dirBtn.classList.add('active');
          }
        }

        // Aktivera sidohängt-knappen
        const sideBtn = wrap.querySelector(`.type-btn[data-value="side"]`);
        if (sideBtn) {
          sideBtn.classList.add('active');
        }
      } else if (doorOpenings[i].startsWith('sliding')) {
        // Visa skjutparti riktningskontroll
        const slidingDirectionControl = wrap.querySelector('.sliding-direction-control');
        if (slidingDirectionControl) {
          slidingDirectionControl.style.display = 'block';
          const direction = doorOpenings[i] === 'sliding-right' ? 'right' : 'left';
          const dirBtn = slidingDirectionControl.querySelector(`[data-direction="${direction}"]`);
          if (dirBtn) {
            dirBtn.classList.add('active');
          }
        }

        // Aktivera skjutparti-knappen
        const slidingBtn = wrap.querySelector(`.type-btn[data-value="sliding"]`);
        if (slidingBtn) {
          slidingBtn.classList.add('active');
        }
      }
    }

    // Hold‐funktion för storleksknappar
    cont.querySelectorAll('.size-btn.size-dec, .size-btn.size-inc').forEach(btn => {
      const idx = +btn.dataset.index;
      const delta = btn.classList.contains('size-inc') ? 1 : -1;
      let iv;
      btn.addEventListener('mousedown', () => {
        changeSize(idx, delta);
        iv = setInterval(() => changeSize(idx, delta), 150);
      });
      ['mouseup', 'mouseleave'].forEach(e => btn.addEventListener(e, () => clearInterval(iv)));
    });

    // Direktinmatning storlek
    cont.querySelectorAll('.size-input:not([disabled])').forEach(inp => {
      const idx = +inp.id.split('_')[2];
      inp.addEventListener('input', e => {
        const raw = e.target.value;
        if (!raw) return;
        doorOpenSizes[idx] = +raw > globalMax ? globalMax : +raw;
        recalcLast(); refreshFields(); updateDoorAll();
      });
      inp.addEventListener('blur', e => {
        let num = +e.target.value;
        if (!e.target.value || num < 10) num = 10;
        doorOpenSizes[idx] = num;
        recalcLast(); refreshFields(); updateDoorAll();
      });
    });
  }

  // ─── GLASTYP ───────────────────────────────────────
  const doorGlassOptions = document.querySelectorAll('.door-glass-option');
  doorGlassOptions.forEach(option => {
    option.addEventListener('click', () => {
      doorGlassOptions.forEach(opt => opt.classList.remove('active'));
      option.classList.add('active');
      window.doorSelectedGlassType = option.dataset.value;
      updateDoorAll();
    });
  });

  // ─── MATERIAL ───────────────────────────────────────
  const doorMaterialOptions = document.querySelectorAll('.door-material-option-btn');
  doorMaterialOptions.forEach(option => {
    option.addEventListener('click', () => {
      doorMaterialOptions.forEach(opt => opt.classList.remove('active'));
      option.classList.add('active');
      window.doorSelectedMaterial = option.dataset.value;

      // Visa/dölj utsidans färgval baserat på material
      const outsideColorSection = document.getElementById('doorOutsideColorSection');
      if (window.doorSelectedMaterial === 'tra-aluminium' || window.doorSelectedMaterial === 'pvc-aluminium') {
        outsideColorSection.classList.remove('hidden');
      } else {
        outsideColorSection.classList.add('hidden');
      }

      updateDoorAll();
    });
  });

  // ─── FÄRG ───────────────────────────────────────
  const doorColorOptions = document.querySelectorAll('.door-color-option');
  doorColorOptions.forEach(option => {
    option.addEventListener('click', () => {
      const side = option.dataset.side;
      const value = option.dataset.value;

      // Avaktivera tidigare val för samma sida
      document.querySelectorAll(`.door-color-option[data-side="${side}"]`).forEach(opt => {
        opt.classList.remove('active');
      });

      option.classList.add('active');

      if (side === 'inside') {
        window.doorSelectedInsideColor = value;
      } else {
        window.doorSelectedOutsideColor = value;
      }

      updateDoorAll();
    });
  });

  // ─── UPPDATERA DIMENSIONER ──────────────────────────
  function updateDoorDimensions() {
    const c = doorPreviewEl.parentNode;
    // Använd globala variabler för att säkerställa att vi använder de senaste värdena
    c.style.width = (window.doorWidthCm * PIXELS_PER_CM) + 'px';
    c.style.height = (window.doorHeightCm * PIXELS_PER_CM) + 'px';

    // Logga dimensioner för felsökning
    console.log(`Uppdaterar dörrpreview dimensioner: ${window.doorWidthCm}x${window.doorHeightCm} cm`);
  }

  // ─── RENDERA PREVIEW ────────────────────────────────
  function renderDoorPreview() {
    doorPreviewEl.innerHTML = '';

    // Skapa dörr med panel och glas
    for (let i = 0; i < window.doorCount; i++) {
      const doorFrame = document.createElement('div');
      doorFrame.className = 'door-frame';
      doorFrame.style.flex = `${window.doorOpenSizes[i]}`;

      // Skapa glasdel
      const glassPane = document.createElement('div');
      glassPane.className = 'door-glass-pane';

      // Justera glasets höjd baserat på panelhöjd
      const glassHeight = window.doorHeightCm - window.panelHeightCm;
      glassPane.style.height = `${(glassHeight / window.doorHeightCm) * 100}%`;

      // Lägg till glaset i dörren
      doorFrame.appendChild(glassPane);

      // Skapa panel om panelhöjden är större än 0
      if (window.panelHeightCm > 0) {
        const panel = document.createElement('div');
        panel.className = 'door-panel';
        panel.style.height = `${(window.panelHeightCm / window.doorHeightCm) * 100}%`;
        doorFrame.appendChild(panel);
      }

      // Lägg till handtag om dörren är öppningsbar
      if (window.doorOpenings[i] !== 'fixed') {
        const handle = document.createElement('div');
        handle.className = 'door-handle';

        // Hantera olika öppningstyper
        if (window.doorOpenings[i] === 'side-left') {
          handle.classList.add('handle-right');
        } else if (window.doorOpenings[i] === 'side-right') {
          handle.classList.add('handle-left');
        } else if (window.doorOpenings[i].startsWith('sliding')) {
          // Speciell hantering för skjutparti
          handle.classList.add('handle-sliding');

          // Hantera olika skjutriktningar
          if (window.doorOpenings[i] === 'sliding' || window.doorOpenings[i] === 'sliding-right') {
            // Från höger (standard)
            handle.classList.add('sliding-right');
            doorFrame.classList.add('sliding-door', 'sliding-right');
          } else if (window.doorOpenings[i] === 'sliding-left') {
            // Från vänster
            handle.classList.add('sliding-left');
            doorFrame.classList.add('sliding-door', 'sliding-left');
          }
        }

        doorFrame.appendChild(handle);
      }

      // Lägg till dörren i preview
      doorPreviewEl.appendChild(doorFrame);
    }

    // Lägg till spröjs om det finns flera lufter
    if (window.doorCount > 1) {
      const doorFrames = doorPreviewEl.querySelectorAll('.door-frame');

      // Lägg till vertikala linjer mellan lufterna
      for (let i = 0; i < window.doorCount - 1; i++) {
        const sproj = document.createElement('div');
        sproj.className = 'door-sproj';
        doorPreviewEl.insertBefore(sproj, doorFrames[i + 1]);
      }
    }

    // Rendera SVG-linjer för öppningar med DoorPreviewRenderer
    if (window.DoorPreviewRenderer && typeof window.DoorPreviewRenderer.renderOpeningLines === 'function') {
      window.DoorPreviewRenderer.renderOpeningLines('doorPreview', window.doorOpenings);
    }
  }

  // ─── UPPDATERA SPEC-TABELL ─────────────────────────
  function updateDoorSpecTable() {
    doorSpecBody.innerHTML = '';

    // Lägg till grundläggande specifikationer
    [
      ['Karmbredd', `${window.doorWidthCm} cm`],
      ['Karmhöjd', `${window.doorHeightCm} cm`],
      ['Antal luft', window.doorCount],
      ['Panelhöjd', `${window.panelHeightCm} cm`]
    ].forEach(([k, v]) => {
      const tr = document.createElement('tr');
      tr.innerHTML = `<td>${k}</td><td>${v}</td>`;
      doorSpecBody.append(tr);
    });

    // Lägg till information om varje luft
    window.doorOpenSizes.forEach((w, i) => {
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', `luft-${i+1}`);

      // Formatera öppningstypen på samma sätt som i fönsterkonfiguratorn
      let openingText = window.doorOpenings[i];
      if (openingText === 'side-left') {
        openingText = 'Vänsterhängd';
      } else if (openingText === 'side-right') {
        openingText = 'Högerhängd';
      } else if (openingText === 'sliding' || openingText === 'sliding-right') {
        openingText = 'Skjutparti från höger';
      } else if (openingText === 'sliding-left') {
        openingText = 'Skjutparti från vänster';
      } else if (openingText === 'fixed') {
        openingText = 'Fast';
      }

      tr.innerHTML = `<td>Luft ${i+1}</td><td>${openingText} ${w} cm</td>`;
      doorSpecBody.append(tr);
    });

    // Lägg till glastyp
    if (window.doorSelectedGlassType) {
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'glassType');
      tr.innerHTML = `<td>Glastyp</td><td>${window.DOOR_GLASS_TYPE_LABELS[window.doorSelectedGlassType]}</td>`;
      doorSpecBody.append(tr);
    }

    // Lägg till material
    if (window.doorSelectedMaterial) {
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'material');
      tr.innerHTML = `<td>Material</td><td>${DOOR_MATERIAL_LABELS[window.doorSelectedMaterial]}</td>`;
      doorSpecBody.append(tr);
    }

    // Lägg till färg insida
    if (window.doorSelectedInsideColor) {
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'insideColor');
      tr.innerHTML = `<td>Färg insida</td><td>${window.DOOR_COLOR_LABELS[window.doorSelectedInsideColor]}</td>`;
      doorSpecBody.append(tr);
    }

    // Lägg till färg utsida om materialet är trä/aluminium eller pvc/aluminium
    if ((window.doorSelectedMaterial === 'tra-aluminium' || window.doorSelectedMaterial === 'pvc-aluminium') && window.doorSelectedOutsideColor) {
      const tr = document.createElement('tr');
      tr.setAttribute('data-key', 'outsideColor');
      tr.innerHTML = `<td>Färg utsida</td><td>${window.DOOR_COLOR_LABELS[window.doorSelectedOutsideColor]}</td>`;
      doorSpecBody.append(tr);
    }

    // Trigga en händelse för att meddela att specifikationstabellen har uppdaterats
    document.dispatchEvent(new CustomEvent('doorSpecTableUpdated'));
  }

  // Exponera funktionen globalt så att den kan anropas från andra skript
  window.updateDoorSpecTable = updateDoorSpecTable;

  // ─── UPPDATERA ALLT ─────────────────────────────────
  function updateDoorAll() {
    updateDoorDimensions();
    renderDoorPreview();
    updateDoorSpecTable();
  }

  // Exponera funktioner globalt
  window.doorGoTo = doorGoTo;
  window.updateDoorAll = updateDoorAll;
  window.buildDoorOpeningControls = buildDoorOpeningControls;

  // ─── INIT ───────────────────────────────────────────
  // Sätt standardvärden
  doorCount = 1;
  doorOpenings = ['fixed']; // Fast som standard
  doorOpenSizes = [doorWidthCm];

  // Aktivera första glastypen
  doorGlassOptions[0].classList.add('active');
  window.doorSelectedGlassType = doorGlassOptions[0].dataset.value;

  // Aktivera första färgalternativet för insidan
  document.querySelector('.door-color-option[data-side="inside"][data-value="white"]').classList.add('active');

  // Sätt standardvärden för panel (0 cm = ingen panel)
  panelHeightCm = 0;
  panelHeightSlider.value = panelHeightCm;
  panelHeightInput.value = panelHeightCm;
  panelHeightLabel.textContent = `${panelHeightCm} cm`;

  // Initialisera preview och öppningskontroller
  setTimeout(() => {
    // Bygg öppningskontroller i förväg så att de finns när användaren går till steg 3
    buildDoorOpeningControls();

    doorGoTo(0);
    updateDoorAll();
  }, 100);
});
