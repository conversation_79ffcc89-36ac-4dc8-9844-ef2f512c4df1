// steg8.js - Spröjs-steget i fönsterkonfiguratorn

(() => {
  // Vänta på att DOM är laddad
  document.addEventListener('DOMContentLoaded', function() {
    // Globala variabler för spröjsval
    window.selectedSprojsType = 'none'; // Standard: inga spröjs
    window.selectedSprojsPerLuft = []; // Specifika spröjs per luft
    window.horizontalSprojsCount = 2; // Standardvärde för antal horisontella spröjs
    window.verticalSprojsCount = 2; // Standardvärde för antal vertikala spröjs
    window.customSprojsPerLuft = []; // Anpassade spröjs per luft

    // Konstanter för spröjstyper
    window.SPROJS_LABELS = {
      'none': 'Inga spröjs',
      'one-horizontal': 'En horisontell',
      'one-vertical': 'En vertikal',
      'cross': 'Kors',
      'grid': 'Rutnät',
      'custom': 'Anpassad'
    };

    // Referens till spröjsalternativen
    const sprojsOptions = document.querySelectorAll('.sprojs-option');
    const luftSprojsContainer = document.getElementById('luftSprojsContainer');
    const customSprojsSettings = document.getElementById('customSprojsSettings');
    const horizontalSprojsCountInput = document.getElementById('horizontalSprojsCount');
    const verticalSprojsCountInput = document.getElementById('verticalSprojsCount');
    const customSprojsPreview = document.getElementById('customSprojsPreview');

    // Lyssna på klickhändelser för spröjsalternativen
    sprojsOptions.forEach(option => {
      option.addEventListener('click', function() {
        const sprojsValue = this.dataset.value;

        // Uppdatera aktiv klass
        document.querySelectorAll('.sprojs-option').forEach(opt => {
          opt.classList.remove('active');
        });
        this.classList.add('active');

        // Spara vald spröjstyp
        window.selectedSprojsType = sprojsValue;

        // Visa/dölj anpassade spröjs-inställningar
        if (sprojsValue === 'custom') {
          customSprojsSettings.style.display = 'block';
          window.updateCustomSprojsPreview();
        } else {
          customSprojsSettings.style.display = 'none';
        }

        // Uppdatera alla lufter med samma spröjstyp
        const count = window.count || 1;
        window.selectedSprojsPerLuft = Array(count).fill(sprojsValue);

        // Initiera anpassade spröjs per luft om det behövs
        if (sprojsValue === 'custom' && (!window.customSprojsPerLuft || window.customSprojsPerLuft.length !== count)) {
          window.customSprojsPerLuft = Array(count).fill({
            horizontal: window.horizontalSprojsCount,
            vertical: window.verticalSprojsCount
          });
        }

        // Uppdatera luftspecifika spröjsalternativ
        if (typeof updateLuftSprojsOptions === 'function') {
          updateLuftSprojsOptions();
        }

        // Uppdatera 3D-modellen
        window.updateSprojsInModel();

        // Uppdatera specifikationstabellen
        window.updateSprojsSpec();

        // Uppdatera 2D-modellen
        window.updateSprojsIn2DModel();

        // Trigga en händelse för att meddela att konfigurationen har ändrats
        document.dispatchEvent(new CustomEvent('configChanged'));
      });
    });

    // Lyssna på ändringar i antal luft
    document.addEventListener('luftCountChanged', function() {
      console.log("Antal luft ändrat, uppdaterar spröjsalternativ");
      updateLuftSprojsOptions();
    });

    // Lyssna på ändringar i antal horisontella spröjs
    horizontalSprojsCountInput.addEventListener('input', function() {
      let value = parseInt(this.value);
      if (isNaN(value)) value = 0;
      if (value < 0) value = 0;
      if (value > 4) value = 4;

      window.horizontalSprojsCount = value;
      this.value = value;

      // Uppdatera anpassade spröjs för alla lufter
      if (window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0) {
        window.customSprojsPerLuft = window.customSprojsPerLuft.map(item => ({
          ...item,
          horizontal: value
        }));
      }

      window.updateCustomSprojsPreview();
      window.updateSprojsInModel();
      window.updateSprojsIn2DModel();
      window.updateSprojsSpec();
    });

    // Lyssna på ändringar i antal vertikala spröjs
    verticalSprojsCountInput.addEventListener('input', function() {
      let value = parseInt(this.value);
      if (isNaN(value)) value = 0;
      if (value < 0) value = 0;
      if (value > 4) value = 4;

      window.verticalSprojsCount = value;
      this.value = value;

      // Uppdatera anpassade spröjs för alla lufter
      if (window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0) {
        window.customSprojsPerLuft = window.customSprojsPerLuft.map(item => ({
          ...item,
          vertical: value
        }));
      }

      window.updateCustomSprojsPreview();
      window.updateSprojsInModel();
      window.updateSprojsIn2DModel();
      window.updateSprojsSpec();
    });

    // Lyssna på klick på öka/minska-knappar för spröjs
    document.querySelectorAll('.increase-btn, .decrease-btn').forEach(button => {
      button.addEventListener('click', function() {
        const targetId = this.dataset.target;
        const input = document.getElementById(targetId);
        if (!input) return;

        let value = parseInt(input.value);
        if (isNaN(value)) value = 0;

        if (this.classList.contains('increase-btn')) {
          value = Math.min(4, value + 1);
        } else {
          value = Math.max(0, value - 1);
        }

        input.value = value;

        // Trigga input-händelsen för att uppdatera modellen
        const event = new Event('input', { bubbles: true });
        input.dispatchEvent(event);
      });
    });

    // Uppdatera förhandsvisning av anpassade spröjs
    window.updateCustomSprojsPreview = function() {
      if (!customSprojsPreview) return;

      // Rensa förhandsvisningen
      customSprojsPreview.innerHTML = '';

      // Skapa horisontella spröjs
      const hCount = window.horizontalSprojsCount || 0;
      for (let i = 0; i < hCount; i++) {
        const hLine = document.createElement('div');
        hLine.className = 'sprojs-h-line';
        // Fördela jämnt över höjden
        const position = (i + 1) * (100 / (hCount + 1));
        hLine.style.top = `${position}%`;
        customSprojsPreview.appendChild(hLine);
      }

      // Skapa vertikala spröjs
      const vCount = window.verticalSprojsCount || 0;
      for (let i = 0; i < vCount; i++) {
        const vLine = document.createElement('div');
        vLine.className = 'sprojs-v-line';
        // Fördela jämnt över bredden
        const position = (i + 1) * (100 / (vCount + 1));
        vLine.style.left = `${position}%`;
        customSprojsPreview.appendChild(vLine);
      }
    }

    // Uppdatera luftspecifika spröjsalternativ
    function updateLuftSprojsOptions() {
      // Rensa container
      luftSprojsContainer.innerHTML = '';

      // Hämta antal lufter
      const count = window.count || 1;
      console.log(`Uppdaterar spröjsalternativ för ${count} lufter`);

      // Säkerställ att selectedSprojsPerLuft har rätt längd
      if (!window.selectedSprojsPerLuft || window.selectedSprojsPerLuft.length !== count) {
        window.selectedSprojsPerLuft = Array(count).fill(window.selectedSprojsType || 'none');
      }

      // Säkerställ att customSprojsPerLuft har rätt längd
      if (!window.customSprojsPerLuft || window.customSprojsPerLuft.length !== count) {
        window.customSprojsPerLuft = Array(count).fill({
          horizontal: window.horizontalSprojsCount || 2,
          vertical: window.verticalSprojsCount || 2
        });
      }

      // Uppdatera alla lufter med samma spröjstyp
      window.selectedSprojsPerLuft = Array(count).fill(window.selectedSprojsType);
    }

    // Uppdatera förhandsvisningen av anpassade spröjs för en specifik luft
    function updateLuftCustomSprojsPreview(luftIndex) {
      const preview = document.getElementById(`customPreview-${luftIndex}`);
      if (!preview) return;

      // Rensa förhandsvisningen
      preview.innerHTML = '';

      // Hämta inställningar för denna luft
      const settings = window.customSprojsPerLuft[luftIndex] || { horizontal: 2, vertical: 2 };

      // Skapa horisontella spröjs
      const hCount = settings.horizontal || 0;
      for (let i = 0; i < hCount; i++) {
        const hLine = document.createElement('div');
        hLine.className = 'sprojs-h-line';
        // Fördela jämnt över höjden
        const position = (i + 1) * (100 / (hCount + 1));
        hLine.style.top = `${position}%`;
        preview.appendChild(hLine);
      }

      // Skapa vertikala spröjs
      const vCount = settings.vertical || 0;
      for (let i = 0; i < vCount; i++) {
        const vLine = document.createElement('div');
        vLine.className = 'sprojs-v-line';
        // Fördela jämnt över bredden
        const position = (i + 1) * (100 / (vCount + 1));
        vLine.style.left = `${position}%`;
        preview.appendChild(vLine);
      }
    }

    // Uppdatera 2D-modellen med valda spröjs
    window.updateSprojsIn2DModel = function() {
      // Hämta alla fönsterramar i 2D-preview
      const preview2D = document.getElementById('preview');
      if (!preview2D) return;

      const frames = preview2D.querySelectorAll('.window-frame');
      if (!frames || frames.length === 0) return;

      // Ta bort befintliga spröjs-linjer
      preview2D.querySelectorAll('.sprojs-line').forEach(line => line.remove());

      // Säkerställ att alla lufter har samma spröjstyp
      const count = window.count || 1;
      window.selectedSprojsPerLuft = Array(count).fill(window.selectedSprojsType);

      // Lägg till spröjs för varje luft
      frames.forEach((frame) => {
        const sprojsType = window.selectedSprojsType;
        if (sprojsType === 'none') return;

        // Skapa SVG för spröjs
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('class', 'frame-svg');
        svg.setAttribute('viewBox', '0 0 100 100');
        svg.setAttribute('preserveAspectRatio', 'none');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');

        // Lägg till spröjs baserat på typ
        if (sprojsType === 'one-horizontal') {
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('class', 'sprojs-line');
          line.setAttribute('x1', '0');
          line.setAttribute('y1', '50');
          line.setAttribute('x2', '100');
          line.setAttribute('y2', '50');
          svg.appendChild(line);
        } else if (sprojsType === 'one-vertical') {
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('class', 'sprojs-line');
          line.setAttribute('x1', '50');
          line.setAttribute('y1', '0');
          line.setAttribute('x2', '50');
          line.setAttribute('y2', '100');
          svg.appendChild(line);
        } else if (sprojsType === 'cross') {
          const hLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          hLine.setAttribute('class', 'sprojs-line');
          hLine.setAttribute('x1', '0');
          hLine.setAttribute('y1', '50');
          hLine.setAttribute('x2', '100');
          hLine.setAttribute('y2', '50');
          svg.appendChild(hLine);

          const vLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          vLine.setAttribute('class', 'sprojs-line');
          vLine.setAttribute('x1', '50');
          vLine.setAttribute('y1', '0');
          vLine.setAttribute('x2', '50');
          vLine.setAttribute('y2', '100');
          svg.appendChild(vLine);
        } else if (sprojsType === 'grid') {
          // Horisontella linjer
          for (let i = 1; i < 3; i++) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('class', 'sprojs-line');
            line.setAttribute('x1', '0');
            line.setAttribute('y1', `${i * 33.33}`);
            line.setAttribute('x2', '100');
            line.setAttribute('y2', `${i * 33.33}`);
            svg.appendChild(line);
          }

          // Vertikala linjer
          for (let i = 1; i < 3; i++) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('class', 'sprojs-line');
            line.setAttribute('x1', `${i * 33.33}`);
            line.setAttribute('y1', '0');
            line.setAttribute('x2', `${i * 33.33}`);
            line.setAttribute('y2', '100');
            svg.appendChild(line);
          }
        } else if (sprojsType === 'custom') {
          const settings = window.customSprojsPerLuft[0] || { horizontal: window.horizontalSprojsCount || 2, vertical: window.verticalSprojsCount || 2 };

          // Horisontella linjer
          const hCount = settings.horizontal || 0;
          for (let i = 0; i < hCount; i++) {
            const position = (i + 1) * (100 / (hCount + 1));
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('class', 'sprojs-line');
            line.setAttribute('x1', '0');
            line.setAttribute('y1', `${position}`);
            line.setAttribute('x2', '100');
            line.setAttribute('y2', `${position}`);
            svg.appendChild(line);
          }

          // Vertikala linjer
          const vCount = settings.vertical || 0;
          for (let i = 0; i < vCount; i++) {
            const position = (i + 1) * (100 / (vCount + 1));
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('class', 'sprojs-line');
            line.setAttribute('x1', `${position}`);
            line.setAttribute('y1', '0');
            line.setAttribute('x2', `${position}`);
            line.setAttribute('y2', '100');
            svg.appendChild(line);
          }
        }

        // Lägg till SVG i fönsterramen
        frame.appendChild(svg);
      });
    }

    // Uppdatera 3D-modellen med valda spröjs
    window.updateSprojsInModel = function() {
      // Kontrollera om 3D-modellen är initialiserad
      if (!window.Preview3D) {
        console.log("3D-preview är inte tillgänglig (Preview3D)");
        return;
      }

      // Säkerställ att alla lufter har samma spröjstyp
      const count = window.count || 1;
      window.selectedSprojsPerLuft = Array(count).fill(window.selectedSprojsType);

      // Skapa konfigurationsobjekt för spröjsuppdatering
      const sprojsConfig = {
        sprojsType: window.selectedSprojsType,
        sprojsPerLuft: window.selectedSprojsPerLuft,
        customSprojsPerLuft: window.customSprojsPerLuft
      };

      console.log("Uppdaterar 3D-modell med spröjs:", sprojsConfig);

      // Uppdatera 3D-modellen
      if (window.Preview3D.updateConfig) {
        window.Preview3D.updateConfig(sprojsConfig);
      }

      // Tvinga en återskapning av modellen
      if (window.Preview3D.recreateModel) {
        setTimeout(() => window.Preview3D.recreateModel(), 50);
      } else if (window.Preview3D.createWindow) {
        setTimeout(() => window.Preview3D.createWindow(), 50);
      }
    }

    // Uppdatera specifikationstabellen med spröjsinformation
    window.updateSprojsSpec = function() {
      // Kontrollera att specBody är tillgänglig
      const specBody = document.querySelector('#specTable tbody');
      if (!specBody) return;

      // Ta bort befintliga spröjsrader
      const sprojsRow = specBody.querySelector('tr[data-key="sprojs"]');
      if (sprojsRow) sprojsRow.remove();

      // Lägg till spröjsrad
      const row = document.createElement('tr');
      row.setAttribute('data-key', 'sprojs');

      // Visa en rad för spröjstypen
      if (window.selectedSprojsType === 'custom') {
        const settings = window.customSprojsPerLuft[0] || { horizontal: window.horizontalSprojsCount || 2, vertical: window.verticalSprojsCount || 2 };
        row.innerHTML = `<td>Spröjs</td><td>Anpassad (${settings.horizontal} horisontella, ${settings.vertical} vertikala)</td>`;
      } else {
        row.innerHTML = `<td>Spröjs</td><td>${window.SPROJS_LABELS[window.selectedSprojsType]}</td>`;
      }

      specBody.appendChild(row);
    }

    // Spara spröjsval tillsammans med resten av designen
    const saveBtn = document.querySelector('#section-8 .save-btn'); // Steg 9 (section-8)
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        setTimeout(() => {
          const arr = JSON.parse(localStorage.getItem('myWindows') || '[]');
          const idx = window.editingIndex !== null ? window.editingIndex : arr.length - 1;
          if (idx >= 0) {
            arr[idx].sprojsType = window.selectedSprojsType;

            // Spara anpassade spröjs-inställningar om de finns och spröjstypen är custom
            if (window.selectedSprojsType === 'custom' && window.customSprojsPerLuft && window.customSprojsPerLuft.length > 0) {
              // Spara bara första luftens inställningar eftersom alla lufter har samma
              arr[idx].customSprojsPerLuft = [window.customSprojsPerLuft[0]];
            }

            localStorage.setItem('myWindows', JSON.stringify(arr));
          }
        }, 0);
      });
    }

    // Patcha loadConfiguration för att ladda spröjsval
    function patchLoadConfiguration() {
      // Vi använder nu den uppdaterade loadConfiguration i script.js
      // som hanterar spröjs korrekt, så vi behöver inte patcha den här
    }

    // Patcha showSavedDetail för att visa spröjsinformation
    function patchShowSavedDetail() {
      if (typeof window.showSavedDetail !== 'function') {
        // Om inte definierad än, testa igen om 50ms
        return setTimeout(patchShowSavedDetail, 50);
      }

      const originalShowSavedDetail = window.showSavedDetail;
      window.showSavedDetail = function(cfg, idx) {
        // Anropa original-funktionen
        originalShowSavedDetail(cfg, idx);

        // Lägg till spröjsinformation i specifikationstabellen
        const tb = document.querySelector('#savedDetail .saved-spec tbody');
        if (!tb) return;

        // Ta bort befintliga spröjsrader
        const sprojsRow = tb.querySelector('tr[data-key="sprojs"]');
        if (sprojsRow) sprojsRow.remove();

        // Lägg till spröjsrad om det finns
        if (cfg.sprojsType) {
          const row = document.createElement('tr');
          row.setAttribute('data-key', 'sprojs');

          if (cfg.sprojsType === 'custom') {
            const settings = cfg.customSprojsPerLuft && cfg.customSprojsPerLuft[0]
              ? cfg.customSprojsPerLuft[0]
              : { horizontal: 2, vertical: 2 };
            row.innerHTML = `<td>Spröjs</td><td>Anpassad (${settings.horizontal} horisontella, ${settings.vertical} vertikala)</td>`;
          } else {
            row.innerHTML = `<td>Spröjs</td><td>${window.SPROJS_LABELS[cfg.sprojsType] || cfg.sprojsType}</td>`;
          }

          tb.appendChild(row);
        }
      };
    }

    // Lyssna på ändringar i antal luft
    document.addEventListener('configChanged', function() {
      const count = window.count || 1;
      if (!window.selectedSprojsPerLuft || window.selectedSprojsPerLuft.length !== count) {
        updateLuftSprojsOptions();
      }
    });

    // Kör patcharna efter att hela sidan laddat
    patchLoadConfiguration();
    patchShowSavedDetail();

    // Initialisera luftspecifika spröjsalternativ
    setTimeout(updateLuftSprojsOptions, 100);
  });
})();
