/* Steg 5 - Glastyp (Förbättrad version) */

.glass-group {
  display: flex;
  flex-direction: row;
  gap: 1.5rem;
  margin: 1.5rem 0;
  width: 100%;
}

@media (max-width: 768px) {
  .glass-group {
    flex-direction: column;
  }
}

.glass-option-btn {
  background: var(--color-panel);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  padding: 0;
  cursor: pointer;
  color: var(--color-secondary);
  transition: all var(--transition);
  text-align: left;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.glass-option-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: var(--color-border-hover, #ccc);
}

.glass-option-btn.active {
  border: 2px solid var(--color-primary);
  box-shadow: 0 0 0 2px rgba(245, 199, 0, 0.3);
}

.glass-option-btn.active::after {
  content: '✓';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: var(--color-primary);
  color: #121212;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.glass-option-header {
  background-color: var(--color-primary);
  color: #121212;
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.glass-option-content {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.glass-option-image {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.glass-option-image img {
  max-height: 100%;
  max-width: 100%;
}

.glass-option-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.glass-option-description {
  font-size: 0.9rem;
  color: var(--color-secondary);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.glass-benefits {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: auto;
}

.glass-benefit {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.glass-benefit-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* SVG icons for benefits */
.icon-energy {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M13 2L3 14h9l-1 8 10-12h-9l1-8z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.icon-sound {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 5L6 9H2v6h4l5 4V5z'/%3E%3Cpath d='M15.54 8.46a5 5 0 0 1 0 7.07'/%3E%3Cpath d='M19.07 4.93a10 10 0 0 1 0 14.14'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.icon-temp {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

/* Glass illustrations */
.double-glass-img {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='none'%3E%3Crect x='20' y='10' width='60' height='80' stroke='%23333' stroke-width='2' fill='%23E6F7FF' fill-opacity='0.3'/%3E%3Cline x1='50' y1='10' x2='50' y2='90' stroke='%23333' stroke-width='2'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
}

.triple-glass-img {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='none'%3E%3Crect x='20' y='10' width='60' height='80' stroke='%23333' stroke-width='2' fill='%23E6F7FF' fill-opacity='0.3'/%3E%3Cline x1='40' y1='10' x2='40' y2='90' stroke='%23333' stroke-width='2'/%3E%3Cline x1='60' y1='10' x2='60' y2='90' stroke='%23333' stroke-width='2'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
}
