/* CSS for the configurator selection screen */

.configurator-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 2rem;
  background: var(--color-panel);
  border-radius: var(--radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.configurator-selection h2 {
  font-size: 1.8rem;
  color: var(--color-primary);
  margin-bottom: 2rem;
  text-align: center;
}

.configurator-options {
  display: flex;
  justify-content: center;
  gap: 2rem;
  width: 100%;
  max-width: 800px;
}

.configurator-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--color-bg);
  border: 2px solid var(--color-border);
  border-radius: var(--radius);
  padding: 2rem;
  cursor: pointer;
  transition: transform var(--transition), border-color var(--transition), box-shadow var(--transition), background var(--transition);
  text-align: center;
  min-height: 300px;
  position: relative;
  overflow: hidden;
}

.configurator-option::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(245, 199, 0, 0.05);
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.configurator-option:hover {
  transform: translateY(-5px);
  border-color: var(--color-primary);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.configurator-option:hover::after {
  opacity: 1;
}

.configurator-option:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.configurator-option h3 {
  font-size: 1.5rem;
  color: var(--color-secondary);
  margin-bottom: 1rem;
}

.configurator-option p {
  color: #aaa;
  margin-bottom: 1.5rem;
}

.configurator-option .icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  color: var(--color-primary);
}

.configurator-option .btn {
  background: var(--color-primary);
  color: #121212;
  border: none;
  border-radius: var(--radius);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background var(--transition), transform var(--transition), box-shadow var(--transition);
  position: relative;
  z-index: 2;
}

.configurator-option .btn:hover {
  background: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.configurator-option .btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Door option should be slightly different to indicate it's coming soon */
.configurator-option.coming-soon {
  opacity: 0.8;
  position: relative;
}

.configurator-option.coming-soon::after {
  content: "Kommer snart";
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--color-primary);
  color: #121212;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .configurator-options {
    flex-direction: column;
  }

  .configurator-option {
    width: 100%;
  }
}
