!function(e){"use strict";window.IsofonsterConfigurator={config:{container:".isofonster-configurator-container",type:"both",enableSave:!0,enableExport:!0,ajaxUrl:"",nonce:""},state:{currentType:"window",currentStep:0,windowData:{width:180,height:150,count:1,openings:["fixed"],hinges:[null],openSizes:[100],material:"wood",glassType:"double",glassTypeValue:"standard",insideColor:"white",outsideColor:"white",sprojsType:"none",handle:"stormkrok",mosquitoNet:!1,customSprojsPerLuft:[]},doorData:{width:90,height:210,opening:"fixed",hinge:null,material:"wood",glassType:"double",insideColor:"white",outsideColor:"white",handle:"standard",panelHeight:0}},init:function(t){this.config=e.extend(this.config,t),this.bindEvents(),this.initializeState(),"undefined"!=typeof isofonster_ajax&&(this.config.ajaxUrl=isofonster_ajax.ajax_url,this.config.nonce=isofonster_ajax.nonce),console.log("Isofönster Konfigurator initialiserad")},bindEvents:function(){var t=this,n=e(this.config.container);n.on("click",".isofonster-top-nav button",(function(e){e.preventDefault(),t.handleNavigation(e(this))})),n.on("click","#isofonster-startWindowConfigurator",(function(e){e.preventDefault(),t.showWindowConfigurator()})),n.on("click","#isofonster-startDoorConfigurator",(function(e){e.preventDefault(),t.showDoorConfigurator()})),n.on("click",".isofonster-step",(function(n){n.preventDefault();var o=parseInt(e(this).data("step"));t.goToStep(o)})),n.on("click",".isofonster-builder-section h2",(function(n){n.preventDefault(),t.toggleSection(e(this).closest(".isofonster-builder-section"))})),n.on("input","#isofonster-widthInput",(function(){t.updateDimension("width",e(this).val())})),n.on("input","#isofonster-heightInput",(function(){t.updateDimension("height",e(this).val())})),n.on("click","#isofonster-decBtn",(function(e){e.preventDefault(),t.updateCount(t.state.windowData.count-1)})),n.on("click","#isofonster-incBtn",(function(e){e.preventDefault(),t.updateCount(t.state.windowData.count+1)})),n.on("input","#isofonster-customIn",(function(){t.updateCount(parseInt(e(this).val()))})),this.config.enableSave&&n.on("click",".isofonster-save-btn",(function(e){e.preventDefault(),t.saveDesign()})),this.config.enableExport&&n.on("click","#isofonster-exportBtn",(function(e){e.preventDefault(),t.exportPDF()}))},initializeState:function(){this.updateDimensionLabels(),this.updatePreview(),this.updateSpecTable()},handleNavigation:function(t){var n=t.data("target"),o=e(this.config.container);o.find(".isofonster-top-nav button").removeClass("active"),t.addClass("active"),o.find(".isofonster-section").addClass("hidden"),o.find("#isofonster-"+n).removeClass("hidden"),"sectionNewDesign"===n?this.showConfiguratorSelection():"sectionSavedDesigns"===n&&this.loadSavedDesigns()},showConfiguratorSelection:function(){var t=e(this.config.container);"both"===this.config.type?(t.find("#isofonster-configuratorSelection").removeClass("hidden"),t.find("#isofonster-windowConfigurator, #isofonster-doorConfigurator").addClass("hidden")):"window"===this.config.type?this.showWindowConfigurator():"door"===this.config.type&&this.showDoorConfigurator()},showWindowConfigurator:function(){var t=e(this.config.container);this.state.currentType="window",t.find("#isofonster-configuratorSelection").addClass("hidden"),t.find("#isofonster-doorConfigurator").addClass("hidden"),t.find("#isofonster-windowConfigurator").removeClass("hidden"),this.goToStep(0),this.updateAll()},showDoorConfigurator:function(){var t=e(this.config.container);this.state.currentType="door",t.find("#isofonster-configuratorSelection").addClass("hidden"),t.find("#isofonster-windowConfigurator").addClass("hidden"),t.find("#isofonster-doorConfigurator").removeClass("hidden"),console.log("Dörrkonfigurator kommer snart...")},goToStep:function(t){var n=e(this.config.container),o=n.find(".isofonster-builder-section"),i=n.find(".isofonster-step");i.removeClass("active"),i.eq(t).addClass("active"),o.addClass("collapsed"),o.eq(t).length&&(o.eq(t).removeClass("collapsed"),o.eq(t)[0].scrollIntoView({behavior:"smooth",block:"start"})),this.state.currentStep=t,2===t&&this.buildOpeningControls()},toggleSection:function(e){e.toggleClass("collapsed")},updateDimension:function(e,t){t=parseFloat(t)||0,"window"===this.state.currentType?"width"===e?this.state.windowData.width=Math.max(50,Math.min(180,t)):"height"===e&&(this.state.windowData.height=Math.max(60,Math.min(180,t))):"door"===this.state.currentType&&("width"===e?this.state.doorData.width=Math.max(60,Math.min(450,t)):"height"===e&&(this.state.doorData.height=Math.max(180,Math.min(240,t)))),this.updateDimensionLabels(),this.updatePreview(),this.updateSpecTable()},updateDimensionLabels:function(){var t=e(this.config.container);"window"===this.state.currentType&&(t.find("#isofonster-widthLabel").text(this.state.windowData.width+" cm"),t.find("#isofonster-heightLabel").text(this.state.windowData.height+" cm"),t.find("#isofonster-widthInput").val(this.state.windowData.width),t.find("#isofonster-heightInput").val(this.state.windowData.height))},updateCount:function(t){t=Math.max(1,Math.min(10,t||1)),this.state.windowData.count=t,this.state.windowData.openings=Array(t).fill("fixed"),this.state.windowData.hinges=Array(t).fill(null),this.redistributeSizes();var n=e(this.config.container);n.find("#isofonster-customIn").val(t),n.find("#isofonster-section-2").hasClass("collapsed")||this.buildOpeningControls(),this.updateAll()},redistributeSizes:function(){var e=this.state.windowData.count,t=100/e;this.state.windowData.openSizes=Array(e).fill(t)},buildOpeningControls:function(){var t=e(this.config.container),n=t.find("#isofonster-openingControls"),o=this.state.windowData.count;n.empty();for(var i=0;i<o;i++){var s=e('<div class="isofonster-opening-wrap">');s.append("<h4>Luft "+(i+1)+"</h4>");var r=e('<div class="isofonster-opening-type-controls">');[{value:"fixed",label:"Fast"},{value:"side-hung",label:"Sidohängt"},{value:"top-hung",label:"Toppvippt"}].forEach(function(t){var n=e('<button class="isofonster-opening-type-btn" data-index="'+i+'" data-type="'+t.value+'">'+t.label+"</button>");this.state.windowData.openings[i]===t.value&&n.addClass("active"),r.append(n)}.bind(this)),s.append(r),n.append(s)}n.on("click",".isofonster-opening-type-btn",function(t){t.preventDefault();var n=parseInt(e(this).data("index")),o=e(this).data("type");this.state.windowData.openings[n]=o,e(this).siblings().removeClass("active"),e(this).addClass("active"),this.updateAll()}.bind(this))},updateAll:function(){this.updateDimensionLabels(),this.updatePreview(),this.updateSpecTable()},updatePreview:function(){var t=e(this.config.container).find("#isofonster-preview");t.html('<div style="text-align: center; color: #999;">Förhandsvisning kommer här</div>')},updateSpecTable:function(){var t=e(this.config.container),n=t.find("#isofonster-specTable tbody");if(n.empty(),"window"===this.state.currentType){var o=this.state.windowData;[["Bredd",o.width+" cm"],["Höjd",o.height+" cm"],["Antal lufter",o.count],["Material",o.material],["Glastyp",o.glassType]].forEach((function(e){n.append("<tr><td>"+e[0]+"</td><td>"+e[1]+"</td></tr>")}))}},saveDesign:function(){if(this.config.enableSave){var t=prompt("Ange namn för din design:");if(t){var n=prompt("Ange antal av denna design:");if((n=parseInt(n)||1)<1)alert("Ogiltigt antal. Ange ett heltal ≥ 1.");else{var o={action:"save_design",nonce:this.config.nonce,name:t,type:this.state.currentType,quantity:n};"window"===this.state.currentType?e.extend(o,this.state.windowData):"door"===this.state.currentType&&e.extend(o,this.state.doorData),e.ajax({url:this.config.ajaxUrl,type:"POST",data:o,success:function(e){e.success?alert(e.data.message):alert("Fel vid sparande: "+e.data)},error:function(){alert("Fel vid sparande")}})}}}}},loadSavedDesigns:function(){this.config.enableSave&&e.ajax({url:this.config.ajaxUrl,type:"POST",data:{action:"load_user_designs",nonce:this.config.nonce},success:function(e){e.success&&this.renderSavedDesigns(e.data)}.bind(this),error:function(){console.error("Fel vid laddning av sparade designs")}})},renderSavedDesigns:function(t){var n=e(this.config.container),o=n.find("#isofonster-savedList");if(o.empty(),0===t.length)o.append('<p class="isofonster-placeholder-text">Inga sparade designs hittades</p>');else{t.forEach((function(t){var n=e('<div class="isofonster-saved-card">');n.append("<h4>"+t.name+"</h4>"),n.append("<p>"+t.type+" - "+t.width+"x"+t.height+" cm</p>"),n.append("<small>Skapad: "+new Date(t.created_at).toLocaleDateString()+"</small>"),o.append(n)}))}},exportPDF:function(){if(this.config.enableExport){var t=e(this.config.container),n=t.find("#isofonster-pdfProgressContainer");n.removeClass("hidden");var o=0,i=setInterval((function(){(o+=10)>=100&&(clearInterval(i),n.addClass("hidden"),alert("PDF export slutförd!")),t.find("#isofonster-pdfProgressBar").css("width",o+"%")}),200)}}},e(document).ready((function(){e(".isofonster-configurator-container").length>0&&setTimeout((function(){"undefined"!=typeof window.IsofonsterConfigurator&&console.log("Isofönster Konfigurator redo för initialisering")}),100)}))}(jQuery);
