/**
 * door-save.js
 * Hanterar sparande av dörrdesigner
 */

(() => {
  // Initialisera när DOM är laddad
  document.addEventListener('DOMContentLoaded', () => {
    // Hitta sparknappen i dörrkonfiguratorn
    const doorSaveBtn = document.querySelector('#door-section-6 .save-btn');

    if (doorSaveBtn) {
      doorSaveBtn.addEventListener('click', saveDoorDesign);
    }

    // Lyssna på ändringar i sparade designs-listan
    document.addEventListener('savedDesignsUpdated', updateSavedDesignsList);

    // Exponera funktioner globalt
    window.saveDoorDesign = saveDoorDesign;
    window.loadDoorDesign = loadDoorDesign;
  });

  /**
   * Sparar aktuell dörrdesign
   */
  function saveDoorDesign() {
    // Hämta sparade designer från localStorage
    const savedDesigns = JSON.parse(localStorage.getItem('myDesigns') || '[]');

    // Kontrollera om vi redigerar en befintlig design
    const isEditing = window.doorEditingIndex !== undefined && window.doorEditingIndex !== null;

    // Om vi redigerar, använd befintligt namn som standard
    let defaultName = '';
    let defaultQuantity = 1;

    if (isEditing) {
      const existingDesign = savedDesigns[window.doorEditingIndex];
      if (existingDesign) {
        defaultName = existingDesign.name;
        defaultQuantity = existingDesign.quantity || 1;
      }
    }

    // Fråga användaren om namn för designen
    const name = prompt('Ange namn för din dörrdesign:', defaultName);
    if (!name) return;

    // Fråga om antal dörrar
    const quantityStr = prompt('Ange antal dörrar av denna design:', defaultQuantity);
    const quantity = parseInt(quantityStr, 10);
    if (isNaN(quantity) || quantity < 1) {
      return alert('Ogiltigt antal. Ange ett heltal ≥ 1.');
    }

    // Kontrollera att alla nödvändiga värden finns
    if (!window.doorWidthCm) {
      console.warn('Door width not found, using default value');
    }
    if (!window.doorHeightCm) {
      console.warn('Door height not found, using default value');
    }

    // Skapa konfigurationsobjekt för dörren
    const doorConfig = {
      type: 'door', // Markera att detta är en dörrdesign
      name: name,
      quantity: quantity,
      width: window.doorWidthCm || 100, // Använd standardvärde om bredd saknas
      height: window.doorHeightCm || 200, // Använd standardvärde om höjd saknas
      count: window.doorCount || 1,
      openings: window.doorOpenings || ['side-left'],
      openSizes: window.doorOpenSizes || [window.doorWidthCm || 100],
      panelHeight: window.panelHeightCm || 0,
      glassType: window.doorSelectedGlassType || 'normal',
      material: window.doorSelectedMaterial || 'wood',
      insideColor: window.doorSelectedInsideColor || 'white',
      outsideColor: window.doorSelectedOutsideColor || 'white',
      savedAt: new Date().toISOString()
    };

    // Logga konfigurationen för felsökning
    console.log('Saving door configuration:', doorConfig);

    if (isEditing) {
      // Uppdatera befintlig design
      savedDesigns[window.doorEditingIndex] = doorConfig;
      console.log(`Uppdaterar dörrdesign med index ${window.doorEditingIndex}`);

      // Återställ redigeringsindex
      window.doorEditingIndex = null;

      // Visa bekräftelse
      alert(`Dörrdesign uppdaterad som "${name}" (Antal: ${quantity})`);
    } else {
      // Lägg till ny design
      savedDesigns.push(doorConfig);

      // Visa bekräftelse
      alert(`Dörrdesign sparad som "${name}" (Antal: ${quantity})`);
    }

    // Spara till localStorage
    localStorage.setItem('myDesigns', JSON.stringify(savedDesigns));

    // Trigga händelse för att uppdatera listan med sparade designer
    document.dispatchEvent(new CustomEvent('savedDesignsUpdated'));
  }

  /**
   * Laddar en sparad dörrdesign
   * @param {Object} config - Konfigurationsobjekt för dörren
   * @param {Number} idx - Index för designen i sparade designer (om det finns)
   */
  function loadDoorDesign(config, idx) {
    if (!config || config.type !== 'door') return;

    // Visa dörrkonfiguratorn
    document.getElementById('configuratorSelection').classList.add('hidden');
    document.getElementById('windowConfigurator').classList.add('hidden');
    document.getElementById('doorConfigurator').classList.remove('hidden');

    // Sätt redigeringsindex om det finns
    if (idx !== undefined) {
      window.doorEditingIndex = idx;
      console.log(`Redigerar dörrdesign med index: ${idx}`);
    }

    // Sätt värden från konfigurationen
    window.doorWidthCm = config.width;
    window.doorHeightCm = config.height;
    window.doorCount = config.count;
    window.doorOpenings = config.openings;
    window.doorOpenSizes = config.openSizes;
    window.panelHeightCm = config.panelHeight;
    window.doorSelectedGlassType = config.glassType;
    window.doorSelectedMaterial = config.material;
    window.doorSelectedInsideColor = config.insideColor;
    window.doorSelectedOutsideColor = config.outsideColor;

    // Uppdatera UI-element
    document.getElementById('doorWidthInput').value = config.width;
    document.getElementById('doorHeightInput').value = config.height;
    document.getElementById('doorWidthLabel').textContent = `${config.width} cm`;
    document.getElementById('doorHeightLabel').textContent = `${config.height} cm`;

    document.getElementById('panelHeightSlider').value = config.panelHeight;
    document.getElementById('panelHeightInput').value = config.panelHeight;
    document.getElementById('panelHeightLabel').textContent = `${config.panelHeight} cm`;

    // Aktivera rätt antal luft
    const doorQtyBtns = document.querySelectorAll('.door-qty-option-btn');
    doorQtyBtns.forEach(btn => {
      const isCustom = btn.dataset.value === 'custom';
      const matches = +btn.dataset.value === config.count;
      btn.classList.toggle('active', matches || (isCustom && !['1','2'].includes(config.count.toString())));
    });

    // Visa/dölj anpassad inmatning för antal luft
    const doorCustomCtrl = document.querySelector('.door-configurator .custom-control');
    const doorCustomIn = document.getElementById('doorCustomCount');
    if (!['1','2'].includes(config.count.toString())) {
      doorCustomCtrl.classList.remove('hidden');
      doorCustomIn.value = config.count;
    } else {
      doorCustomCtrl.classList.add('hidden');
    }

    // Aktivera rätt material
    document.querySelectorAll('.door-material-option-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.value === config.material);
    });

    // Aktivera rätt glastyp
    document.querySelectorAll('.door-glass-option').forEach(opt => {
      opt.classList.toggle('active', opt.dataset.value === config.glassType);
    });

    // Aktivera rätt färger
    document.querySelectorAll('.door-color-option[data-side="inside"]').forEach(opt => {
      opt.classList.toggle('active', opt.dataset.value === config.insideColor);
    });

    document.querySelectorAll('.door-color-option[data-side="outside"]').forEach(opt => {
      opt.classList.toggle('active', opt.dataset.value === config.outsideColor);
    });

    // Visa/dölj utsidans färgval baserat på material
    const outsideColorSection = document.getElementById('doorOutsideColorSection');
    if (config.material === 'tra-aluminium' || config.material === 'pvc-aluminium') {
      outsideColorSection.classList.remove('hidden');
    } else {
      outsideColorSection.classList.add('hidden');
    }

    // Uppdatera öppningskontroller
    window.buildDoorOpeningControls();

    // Uppdatera allt
    window.updateDoorAll();

    // Navigera till första steget i dörrkonfiguratorn
    if (typeof window.doorGoTo === 'function') {
      setTimeout(() => {
        window.doorGoTo(0);
      }, 100);
    }
  }

  /**
   * Uppdaterar listan med sparade designer
   */
  function updateSavedDesignsList() {
    // Denna funktion anropas när listan med sparade designer behöver uppdateras
    // Den faktiska implementationen finns i script.js
  }
})();
