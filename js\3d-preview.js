/**
 * 3D Fönsterpreview
 * Integrerad direkt i huvudsidan för att visa en interaktiv 3D-modell av fönstret
 */

// Omedelbart anropad funktionsuttryck för att undvika globala variabler
(function() {
  // Globala variabler inom detta scope
  let scene, camera, renderer, controls;
  let windowModel, frameGroup;
  let isOutside = false; // Börja med insidan
  let autoRotate = false;
  let isInitialized = false;
  let container;

  // Konfiguration
  const config = {
    // Dimensioner
    width: 100,  // cm
    height: 120, // cm
    depth: 10,   // cm
    frameWidth: 8, // cm

    // Färger
    glassColor: 0xffffff, // Helt vit för maximal genomskinlighet
    frameColor: 0xffffff, // Vit (standard) för yttre karm
    insideColor: 0xffffff, // Vit (standard) för inre ram
    outsideColor: 0xffffff, // Vit (standard) för utsidan
    lineColor: 0xffffff, // Vit för linjer - maximal synlighet
    handleColor: 0xeeeeee, // Ljus silver för handtag - mer synligt
    glassLayerColor: 0x888888, // Färg för linjer som visar glasskikt

    // Glastyp
    glassType: 'clear', // Standard glastyp för hela fönstret
    glassTypes: ['clear'], // Glastyper per luft
    glassLayers: 2, // Antal glasskikt (2 eller 3)

    // Spröjs
    sprojsType: 'none', // Standard spröjstyp för hela fönstret
    sprojsPerLuft: ['none'], // Spröjstyper per luft
    customSprojsPerLuft: [{ horizontal: 2, vertical: 2 }], // Anpassade spröjs per luft

    // Fönsterdata
    count: 1,
    openings: ['fixed'],
    hinges: [null],
    openSizes: [100],

    // Materialegenskaper
    glassMaterialProps: {
      transparent: true,
      opacity: 0.01, // Nästan helt osynligt
      roughness: 0.0,
      metalness: 0.0,
      clearcoat: 0.0, // Ingen beläggning
      reflectivity: 0.0, // Ingen reflektivitet
      transmission: 1.0, // Fullständig transmission
      side: THREE.DoubleSide,
      depthWrite: false,
      depthTest: true,
      premultipliedAlpha: true,
      blending: THREE.CustomBlending,
      blendSrc: THREE.OneFactor,
      blendDst: THREE.OneMinusSrcAlphaFactor
    },

    frameMaterialProps: {
      roughness: 0.5, // Lite mindre grov yta för bättre ljusreflektion
      metalness: 0.1, // Lätt metallisk effekt för bättre synlighet
      flatShading: false,
      emissive: 0x111111, // Lätt självlysande effekt för bättre synlighet i mörka områden
      emissiveIntensity: 0.1
    },

    // Rendering
    renderQuality: 'high', // 'low', 'medium', 'high'
    shadows: false, // Aktivera/inaktivera skuggor

    // Debug
    debug: true // Aktivera/inaktivera debug-loggning
  };

  // Material för olika glastyper
  const glassMaterials = {
    // Klart glas - mer synligt med lätt blå ton och reflektivitet
    clear: function() {
      return new THREE.MeshPhysicalMaterial({
        color: 0xc0e8ff, // Lätt blå ton för att göra glaset mer synligt
        metalness: 0.2,   // Lite metallisk för reflektioner
        roughness: 0.05,  // Nästan helt slätt
        transmission: 0.9, // Hög transmission men inte helt genomskinligt
        thickness: 0.5,    // Tjockare glas
        clearcoat: 0.5,    // Lägg till ytbeläggning för mer realism
        clearcoatRoughness: 0.05,
        transparent: true,
        opacity: 0.3,      // Högre opacitet för att göra glaset mer synligt
        reflectivity: 0.3, // Mer reflektivitet
        side: THREE.DoubleSide,
        depthWrite: true   // Aktivera djupskrivning för bättre rendering
      });
    },

    // Frostat glas - diffus transparens
    frosted: function() {
      return new THREE.MeshPhysicalMaterial({
        color: 0xf0f0ff,   // Lätt blåvit ton
        metalness: 0.1,
        roughness: 0.8,    // Mycket hög roughness för frostat utseende
        transmission: 0.7,
        thickness: 0.8,    // Tjockare glas
        clearcoat: 0.2,
        clearcoatRoughness: 0.8,
        transparent: true,
        opacity: 0.6,      // Högre opacitet
        reflectivity: 0.2,
        side: THREE.DoubleSide,
        depthWrite: true
      });
    },

    // Tonat glas - mörkare och mindre transparent
    tinted: function() {
      return new THREE.MeshPhysicalMaterial({
        color: 0x505060,   // Mörkare blågrå ton
        metalness: 0.2,
        roughness: 0.05,
        transmission: 0.6,
        thickness: 0.8,    // Tjockare glas
        clearcoat: 1.0,
        clearcoatRoughness: 0.1,
        transparent: true,
        opacity: 0.7,      // Högre opacitet
        reflectivity: 0.4, // Mer reflektivitet
        side: THREE.DoubleSide,
        depthWrite: true
      });
    },

    // Reflekterande glas - mer metalliskt och reflekterande
    reflective: function() {
      return new THREE.MeshPhysicalMaterial({
        color: 0xb0c0d0,   // Silverblå ton
        metalness: 0.6,    // Mycket metallisk
        roughness: 0.05,
        transmission: 0.5,
        thickness: 0.8,    // Tjockare glas
        clearcoat: 1.0,
        clearcoatRoughness: 0.05,
        transparent: true,
        opacity: 0.8,      // Högre opacitet
        reflectivity: 0.7, // Mycket hög reflektivitet
        side: THREE.DoubleSide,
        depthWrite: true
      });
    }
  };

  /**
   * Debug-loggning med stöd för olika nivåer
   * @param {string} message - Meddelande att logga
   * @param {string} level - Loggnivå ('info', 'warn', 'error')
   * @param {Object} data - Extra data att logga
   */
  function debug(message, level = 'info', data = null) {
    // Disabled all console logging
    return;
  }

  // Exponera funktioner globalt
  window.Preview3D = {
    initialize,
    updateConfig,
    show,
    hide,
    recreateModel,
    createWindow,
    isVisible: false,
    debug // Exponera debug-funktionen för extern användning
  };

  // Lyssna på DOM-laddning
  document.addEventListener('DOMContentLoaded', setupEventListeners);

  /**
   * Sätter upp event-lyssnare för 3D-preview
   */
  function setupEventListeners() {
    // Knapp för att växla 3D-vy
    const toggleBtn = document.getElementById('toggle3dPreview');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', togglePreview);
    }

    // Knapp för att rotera
    const rotateBtn = document.getElementById('rotateBtn');
    if (rotateBtn) {
      rotateBtn.addEventListener('click', toggleRotation);
    }

    // Knapp för att vända fönstret
    const flipBtn = document.getElementById('flipBtn');
    if (flipBtn) {
      flipBtn.addEventListener('click', flipWindow);
    }

    // Lyssna på storleksändringar
    window.addEventListener('resize', onWindowResize);

    // Lyssna på ändringar i glastyp från konfiguratorn
    setupGlassTypeListeners();

    // Lyssna på ändringar i dimensioner
    setupDimensionListeners();

    // Lyssna på ändringar i antal luft
    setupLuftCountListeners();

    // Lyssna på alla ändringar i konfigurationen
    document.addEventListener('configChanged', function() {
      debug("Konfigurationsändring detekterad, uppdaterar 3D-modellen");
      const newConfig = getCurrentConfig();
      updateConfig(newConfig);
    });

    // Lyssna på ändringar i specifikationstabellen
    const specTable = document.getElementById('specTable');
    if (specTable) {
      const observer = new MutationObserver(function(mutations) {
        debug("Specifikationstabellen har ändrats, uppdaterar 3D-modellen");
        const newConfig = getCurrentConfig();
        updateConfig(newConfig);
      });

      observer.observe(specTable, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true
      });
    }
  }

  /**
   * Sätter upp lyssnare för ändringar i dimensioner
   */
  function setupDimensionListeners() {
    debug("Sätter upp lyssnare för dimensionsändringar");

    // Lyssna på ändringar i bredd och höjd
    const widthInput = document.getElementById('widthInput');
    const heightInput = document.getElementById('heightInput');

    if (widthInput) {
      widthInput.addEventListener('input', function() {
        debug(`Bredd ändrad till: ${this.value}`);
        const newConfig = getCurrentConfig();
        updateConfig(newConfig);
      });
    }

    if (heightInput) {
      heightInput.addEventListener('input', function() {
        debug(`Höjd ändrad till: ${this.value}`);
        const newConfig = getCurrentConfig();
        updateConfig(newConfig);
      });
    }

    debug("Dimensionslyssnare konfigurerade");
  }

  /**
   * Sätter upp lyssnare för ändringar i antal luft
   */
  function setupLuftCountListeners() {
    debug("Sätter upp lyssnare för ändringar i antal luft");

    // Lyssna på klick på antal luft-knappar
    const luftButtons = document.querySelectorAll('.qty-option-btn');
    luftButtons.forEach(button => {
      button.addEventListener('click', function() {
        debug(`Antal luft-knapp klickad: ${this.textContent}`);

        // Kort fördröjning för att låta DOM uppdateras
        setTimeout(() => {
          const newConfig = getCurrentConfig();
          updateConfig(newConfig);
        }, 50);
      });
    });

    // Lyssna på ändringar i anpassat antal luft
    const customCountInput = document.getElementById('customCount');
    if (customCountInput) {
      customCountInput.addEventListener('input', function() {
        debug(`Anpassat antal luft ändrat till: ${this.value}`);

        // Kort fördröjning för att låta DOM uppdateras
        setTimeout(() => {
          const newConfig = getCurrentConfig();
          updateConfig(newConfig);
        }, 50);
      });
    }

    // Lyssna på klick på lägg till/ta bort luft-knappar
    document.addEventListener('click', function(event) {
      if (event.target.classList.contains('add-opening-btn') ||
          event.target.classList.contains('remove-opening-btn')) {
        debug(`Lägg till/ta bort luft-knapp klickad`);

        // Kort fördröjning för att låta DOM uppdateras
        setTimeout(() => {
          const newConfig = getCurrentConfig();
          updateConfig(newConfig);
        }, 100);
      }
    });

    debug("Antal luft-lyssnare konfigurerade");
  }

  /**
   * Sätter upp lyssnare för ändringar i glastyp och antal glasskikt från konfiguratorn
   * Stödjer både global glastyp och glastyp per luft
   */
  function setupGlassTypeListeners() {
    debug("Sätter upp lyssnare för glastyp-val och antal glasskikt");

    // Lyssna på ändringar i glastyp-val (frostat, tonat, etc.)
    document.addEventListener('click', function(event) {
      // Kontrollera om klicket var på ett glastyp-val
      const glassTypeOption = findGlassTypeOption(event.target);

      if (glassTypeOption) {
        const glassType = glassTypeOption.dataset.glassType ||
                         glassTypeOption.getAttribute('data-glass-type') ||
                         'clear';

        // Kontrollera om detta är för en specifik luft
        const luftIndex = glassTypeOption.dataset.luftIndex ||
                         glassTypeOption.getAttribute('data-luft-index');

        if (luftIndex !== null && luftIndex !== undefined) {
          // Detta är för en specifik luft
          const index = parseInt(luftIndex, 10);
          debug(`Glastyp vald för luft ${index+1}: ${glassType}`);

          // Uppdatera konfigurationen och 3D-modellen
          {
            // Säkerställ att glassTypes-arrayen finns och har rätt längd
            if (!Array.isArray(config.glassTypes)) {
              config.glassTypes = Array(config.count).fill(config.glassType || 'clear');
            }

            // Uppdatera endast om det är en ändring
            if (config.glassTypes[index] !== glassType) {
              config.glassTypes[index] = glassType;
              updateGlassMaterial();
              debug(`3D-modell uppdaterad med ny glastyp för luft ${index+1}: ${glassType}`);

              // Spara glastyper i en global variabel för att kunna återställa dem senare
              window.glassTypes = [...config.glassTypes];
            }
          }
        } else {
          // Detta är för alla lufter (global glastyp)
          debug(`Global glastyp vald: ${glassType}`);

          // Uppdatera konfigurationen och 3D-modellen
          if (config.glassType !== glassType) {
            config.glassType = glassType;

            // Uppdatera alla lufter med samma glastyp
            config.glassTypes = Array(config.count).fill(glassType);

            updateGlassMaterial();
            debug(`3D-modell uppdaterad med ny global glastyp: ${glassType}`);

            // Spara glastyper i en global variabel för att kunna återställa dem senare
            window.glassTypes = [...config.glassTypes];
          }
        }
      }
    });

    // Lyssna på ändringar i glastyp från radioknappar eller select-element
    const glassTypeInputs = document.querySelectorAll('input[name="glastyp"], select[name="glastyp"]');
    glassTypeInputs.forEach(input => {
      input.addEventListener('change', function(event) {
        const glassType = event.target.value || 'clear';

        // Kontrollera om detta är för en specifik luft
        const luftIndex = input.dataset.luftIndex ||
                         input.getAttribute('data-luft-index');

        if (luftIndex !== null && luftIndex !== undefined) {
          // Detta är för en specifik luft
          const index = parseInt(luftIndex, 10);
          debug(`Glastyp ändrad via input för luft ${index+1}: ${glassType}`);

          // Uppdatera konfigurationen och 3D-modellen
          {
            // Säkerställ att glassTypes-arrayen finns och har rätt längd
            if (!Array.isArray(config.glassTypes)) {
              config.glassTypes = Array(config.count).fill(config.glassType || 'clear');
            }

            // Uppdatera endast om det är en ändring
            if (config.glassTypes[index] !== glassType) {
              config.glassTypes[index] = glassType;
              updateGlassMaterial();
              debug(`3D-modell uppdaterad med ny glastyp för luft ${index+1}: ${glassType}`);

              // Spara glastyper i en global variabel för att kunna återställa dem senare
              window.glassTypes = [...config.glassTypes];
            }
          }
        } else {
          // Detta är för alla lufter (global glastyp)
          debug(`Global glastyp ändrad via input: ${glassType}`);

          // Uppdatera konfigurationen och 3D-modellen
          if (config.glassType !== glassType) {
            config.glassType = glassType;

            // Uppdatera alla lufter med samma glastyp
            config.glassTypes = Array(config.count).fill(glassType);

            updateGlassMaterial();
            debug(`3D-modell uppdaterad med ny global glastyp: ${glassType}`);

            // Spara glastyper i en global variabel för att kunna återställa dem senare
            window.glassTypes = [...config.glassTypes];
          }
        }
      });
    });

    // Lyssna på ändringar i antal glasskikt
    document.addEventListener('click', function(event) {
      // Kontrollera om klicket var på ett val för antal glasskikt
      const glassLayersOption = findGlassLayersOption(event.target);

      if (glassLayersOption) {
        const layers = parseInt(glassLayersOption.dataset.glassLayers ||
                               glassLayersOption.getAttribute('data-glass-layers'), 10);

        if (!isNaN(layers) && (layers === 2 || layers === 3)) {
          debug(`Antal glasskikt valt: ${layers}`);

          // Uppdatera konfigurationen och 3D-modellen
          if (config.glassLayers !== layers) {
            config.glassLayers = layers;
            createWindow(); // Uppdatera hela modellen eftersom antal glasskikt påverkar utseendet
            debug(`3D-modell uppdaterad med nytt antal glasskikt: ${layers}`);

            // Spara antal glasskikt i en global variabel för att kunna återställa det senare
            window.glassLayers = layers;
          }
        }
      }
    });

    // Lyssna på ändringar i antal glasskikt från radioknappar eller select-element
    const glassLayersInputs = document.querySelectorAll('input[name="antal-glas"], select[name="antal-glas"]');
    glassLayersInputs.forEach(input => {
      input.addEventListener('change', function(event) {
        const layers = parseInt(event.target.value, 10);

        if (!isNaN(layers) && (layers === 2 || layers === 3)) {
          debug(`Antal glasskikt ändrat via input: ${layers}`);

          // Uppdatera konfigurationen och 3D-modellen
          if (config.glassLayers !== layers) {
            config.glassLayers = layers;
            createWindow(); // Uppdatera hela modellen eftersom antal glasskikt påverkar utseendet
            debug(`3D-modell uppdaterad med nytt antal glasskikt: ${layers}`);

            // Spara antal glasskikt i en global variabel för att kunna återställa det senare
            window.glassLayers = layers;
          }
        }
      });
    });

    debug("Glastyp- och glasskikt-lyssnare konfigurerade");
  }

  /**
   * Hittar val för antal glasskikt från ett klickat element eller dess föräldrar
   * @param {Element} element - Element som klickades
   * @returns {Element|null} Glasskikt-alternativet eller null om inget hittades
   */
  function findGlassLayersOption(element) {
    // Kontrollera om elementet självt är ett glasskikt-alternativ
    if (element.classList &&
        (element.classList.contains('glass-layers-option') ||
         element.hasAttribute('data-glass-layers'))) {
      return element;
    }

    // Kontrollera föräldrar upp till 3 nivåer
    let parent = element.parentElement;
    for (let i = 0; i < 3 && parent; i++) {
      if (parent.classList &&
          (parent.classList.contains('glass-layers-option') ||
           parent.hasAttribute('data-glass-layers'))) {
        return parent;
      }
      parent = parent.parentElement;
    }

    return null;
  }

  /**
   * Hittar glastyp-alternativ från ett klickat element eller dess föräldrar
   * @param {Element} element - Element som klickades
   * @returns {Element|null} Glastyp-alternativet eller null om inget hittades
   */
  function findGlassTypeOption(element) {
    // Kontrollera om elementet självt är ett glastyp-alternativ
    if (element.classList &&
        (element.classList.contains('glass-type-option') ||
         element.hasAttribute('data-glass-type'))) {
      return element;
    }

    // Kontrollera föräldrar upp till 3 nivåer
    let parent = element.parentElement;
    for (let i = 0; i < 3 && parent; i++) {
      if (parent.classList &&
          (parent.classList.contains('glass-type-option') ||
           parent.hasAttribute('data-glass-type'))) {
        return parent;
      }
      parent = parent.parentElement;
    }

    return null;
  }

  /**
   * Växlar mellan 2D och 3D preview
   */
  function togglePreview() {
    try {
      if (window.Preview3D.isVisible) {
        window.Preview3D.hide();
      } else {
        // Uppdatera konfigurationen från aktuella värden
        const config = getCurrentConfig();
        window.Preview3D.updateConfig(config);

        // Visa 3D-preview med en kort fördröjning för att säkerställa att DOM är uppdaterad
        setTimeout(() => {
          window.Preview3D.show();
        }, 50);
      }
    } catch (error) {
      // Error handling without logging
    }
  }

  /**
   * Hämtar aktuell konfiguration direkt från 2D-preview
   * @returns {Object} Konfigurationsobjekt för 3D-preview
   */
  function getCurrentConfig() {
    debug("Hämtar aktuell konfiguration för 3D-preview");

    // Skapa ett nytt konfigurationsobjekt baserat på det befintliga
    const newConfig = Object.assign({}, config);

    try {
      // Hämta värden från DOM
      const widthInput = document.getElementById('widthInput');
      const heightInput = document.getElementById('heightInput');

      if (widthInput && heightInput) {
        newConfig.width = parseInt(widthInput.value) || 100;
        newConfig.height = parseInt(heightInput.value) || 120;
        debug(`Hämtade dimensioner från input: ${newConfig.width}x${newConfig.height}`);
      } else {
        debug("Kunde inte hitta bredd/höjd-inputs, använder standardvärden", 'warn');
      }

      // Hämta global glastyp från DOM (används som fallback)
      const glassTypeInputs = document.querySelectorAll('input[name="glastyp"]:checked, select[name="glastyp"]');
      if (glassTypeInputs.length > 0) {
        const selectedGlassType = glassTypeInputs[0].value;
        if (selectedGlassType) {
          newConfig.glassType = selectedGlassType;
          debug(`Hämtade global glastyp från input: ${newConfig.glassType}`);
        }
      } else {
        // Försök hitta element med data-glass-type attribut som har active-klass
        const activeGlassTypeOption = document.querySelector('.glass-type-option.active, [data-glass-type].active');
        if (activeGlassTypeOption) {
          const glassType = activeGlassTypeOption.dataset.glassType ||
                           activeGlassTypeOption.getAttribute('data-glass-type');
          if (glassType) {
            newConfig.glassType = glassType;
            debug(`Hämtade global glastyp från aktivt element: ${newConfig.glassType}`);
          }
        } else {
          debug("Kunde inte hitta vald global glastyp, använder standardvärde: " + newConfig.glassType, 'warn');
        }
      }

      // Initiera glastyper per luft med standardvärden
      let glassTypes = Array(newConfig.count || 1).fill(newConfig.glassType);

      // Försök hämta glastyper per luft från DOM
      try {
        const frames = document.querySelectorAll('.window-frame');
        if (frames.length > 0) {
          debug(`Försöker hämta glastyper för ${frames.length} lufter`);

          frames.forEach((frame, index) => {
            // Försök hitta glastyp för denna luft
            const luftGlassType = frame.dataset.glassType ||
                                 frame.getAttribute('data-glass-type');

            if (luftGlassType) {
              glassTypes[index] = luftGlassType;
              debug(`Hittade glastyp för luft ${index+1}: ${luftGlassType}`);
            } else {
              // Försök hitta glastyp-element inom denna luft
              const luftGlassTypeElement = frame.querySelector('[data-glass-type]');
              if (luftGlassTypeElement) {
                const type = luftGlassTypeElement.dataset.glassType ||
                            luftGlassTypeElement.getAttribute('data-glass-type');
                if (type) {
                  glassTypes[index] = type;
                  debug(`Hittade glastyp för luft ${index+1} från barn-element: ${type}`);
                }
              }
            }
          });
        }
      } catch (error) {
        debug(`Fel vid hämtning av glastyper per luft: ${error.message}`, 'error');
      }

      // Fallback till globala variabler om de finns
      if (Array.isArray(window.glassTypes) && window.glassTypes.length > 0) {
        debug(`Använder globala glassTypes: ${window.glassTypes.join(', ')}`);

        // Kopiera så många värden som möjligt från globala glassTypes
        for (let i = 0; i < Math.min(glassTypes.length, window.glassTypes.length); i++) {
          if (window.glassTypes[i]) {
            glassTypes[i] = window.glassTypes[i];
          }
        }
      }

      // Säkerställ att glassTypes har rätt längd
      while (glassTypes.length < newConfig.count) {
        glassTypes.push(newConfig.glassType);
      }
      if (glassTypes.length > newConfig.count) {
        glassTypes = glassTypes.slice(0, newConfig.count);
      }

      newConfig.glassTypes = glassTypes;
      debug(`Glastyper per luft: ${glassTypes.join(', ')}`);

      // Hämta antal glasskikt från DOM
      try {
        // Metod 1: Försök hitta input med name="antal-glas"
        const glassLayersInputs = document.querySelectorAll('input[name="antal-glas"]:checked, select[name="antal-glas"]');
        if (glassLayersInputs.length > 0) {
          const selectedLayers = parseInt(glassLayersInputs[0].value, 10);
          if (!isNaN(selectedLayers) && (selectedLayers === 2 || selectedLayers === 3)) {
            newConfig.glassLayers = selectedLayers;
            debug(`Hämtade antal glasskikt från input: ${newConfig.glassLayers}`);
          }
        } else {
          // Metod 2: Försök hitta element med data-glass-layers attribut som har active-klass
          const activeLayersOption = document.querySelector('.glass-layers-option.active, [data-glass-layers].active');
          if (activeLayersOption) {
            const layers = parseInt(activeLayersOption.dataset.glassLayers ||
                                   activeLayersOption.getAttribute('data-glass-layers'), 10);
            if (!isNaN(layers) && (layers === 2 || layers === 3)) {
              newConfig.glassLayers = layers;
              debug(`Hämtade antal glasskikt från aktivt element: ${newConfig.glassLayers}`);
            }
          } else {
            // Metod 3: Fallback till global variabel
            if (typeof window.glassLayers !== 'undefined') {
              const layers = parseInt(window.glassLayers, 10);
              if (!isNaN(layers) && (layers === 2 || layers === 3)) {
                newConfig.glassLayers = layers;
                debug(`Hämtade antal glasskikt från global variabel: ${newConfig.glassLayers}`);
              }
            } else {
              debug(`Kunde inte hitta antal glasskikt, använder standardvärde: ${newConfig.glassLayers || 2}`, 'warn');
            }
          }
        }
      } catch (error) {
        debug(`Fel vid hämtning av antal glasskikt: ${error.message}`, 'error');
      }


      // Standardvärden
      let count = 1;
      let openings = ['fixed'];
      let hinges = [null];
      let openSizes = [newConfig.width];

      // Hämta direkt från 2D-preview
      const preview2D = document.getElementById('preview');
      if (preview2D) {
        debug("2D-preview hittad, extraherar information...");

        // Räkna antal fönsterramar i 2D-preview
        const frames = preview2D.querySelectorAll('.window-frame');
        debug(`Hittade ${frames.length} fönsterramar i 2D-preview`);

        if (frames.length > 0) {
          count = frames.length;
          openings = [];
          hinges = [];
          openSizes = [];

          // Extrahera information från varje fönsterram
          frames.forEach((frame, index) => {
            debug(`Analyserar fönsterram ${index + 1}/${frames.length}`);

            // Bestäm öppningstyp baserat på SVG-linjer och handtag
            let opening = 'fixed';
            let hinge = null;

            // Kontrollera om det finns ett handtag
            const handle = frame.querySelector('.handle');
            if (handle) {
              debug(`Handtag hittat i fönsterram ${index + 1}, klasser: ${handle.className}`);

              // Analysera handtagets position
              if (handle.classList.contains('side-left')) {
                opening = 'side-hung';
                hinge = 'side-right';
                debug(`Fönsterram ${index + 1} har sidohängt handtag på vänster sida`);
              } else if (handle.classList.contains('side-right')) {
                opening = 'side-hung';
                hinge = 'side-left';
                debug(`Fönsterram ${index + 1} har sidohängt handtag på höger sida`);
              } else if (handle.classList.contains('bottom-center')) {
                opening = 'top-hung';
                debug(`Fönsterram ${index + 1} har topphängt handtag`);
              } else if (handle.classList.contains('top-center')) {
                opening = 'bottom-hung';
                debug(`Fönsterram ${index + 1} har bottenhängt handtag`);
              } else {
                debug(`Fönsterram ${index + 1} har handtag med okänd placering`, 'warn');
              }

              // Kontrollera om det finns öppningslinjer som bekräftar öppningstypen
              const openingLines = frame.querySelectorAll('.opening-line');
              if (openingLines.length > 0) {
                debug(`Fönsterram ${index + 1} har ${openingLines.length} öppningslinjer`);
              }
            } else {
              debug(`Inget handtag hittat i fönsterram ${index + 1}, antar fast fönster`);
            }

            // Bestäm storlek baserat på flera metoder
            let size = newConfig.width / count; // Standardvärde

            // Metod 1: Försök hämta flex-värde
            const flexValue = frame.style.flex;
            if (flexValue) {
              const match = flexValue.match(/^(\d+)/);
              if (match && match[1]) {
                size = parseInt(match[1]);
                debug(`Metod 1: Fönsterram ${index + 1} har flex-värde ${flexValue}, storlek: ${size}`);
              }
            }

            // Metod 2: Försök hämta bredd direkt
            if (size === newConfig.width / count && frame.offsetWidth) {
              // Endast använd offsetWidth om vi inte redan har ett värde från flex
              const frameWidth = frame.offsetWidth;
              if (frameWidth > 0) {
                // Konvertera från pixlar till enheter
                const containerWidth = preview2D.offsetWidth;
                const scaleFactor = newConfig.width / containerWidth;
                size = Math.round(frameWidth * scaleFactor);
                debug(`Metod 2: Fönsterram ${index + 1} har bredd ${frameWidth}px, skalad till ${size} enheter`);
              }
            }

            // Metod 3: Försök hämta data-attribut
            if (size === newConfig.width / count && frame.dataset.width) {
              size = parseInt(frame.dataset.width);
              debug(`Metod 3: Fönsterram ${index + 1} har data-width=${size}`);
            }

            // Lägg till information för denna fönsterram
            openings.push(opening);
            hinges.push(hinge);
            openSizes.push(size);
          });
        }
      } else {
        debug("2D-preview kunde inte hittas, använder fallback-värden", 'warn');
      }

      debug("Extraherad från 2D-preview:", 'info', {
        count,
        openings,
        hinges,
        openSizes
      });

      // Fallback till globala variabler om vi inte kunde extrahera från 2D-preview
      if (count === 1 && openings[0] === 'fixed' && typeof window.count !== 'undefined') {
        debug("Använder globala variabler som fallback");

        if (typeof window.count !== 'undefined') {
          count = window.count;
          debug(`Använder global count: ${count}`);
        }

        if (Array.isArray(window.openings) && window.openings.length > 0) {
          openings = window.openings;
          debug(`Använder globala openings: ${openings.join(', ')}`);
        }

        if (Array.isArray(window.hinges)) {
          hinges = window.hinges;
          debug(`Använder globala hinges: ${hinges.join(', ')}`);
        }

        if (Array.isArray(window.openSizes) && window.openSizes.length > 0) {
          openSizes = window.openSizes;
          debug(`Använder globala openSizes: ${openSizes.join(', ')}`);
        }
      }

      // Säkerställ att arrays har rätt längd
      while (openings.length < count) {
        openings.push('fixed');
        debug(`Lade till 'fixed' i openings för att matcha count (${count})`);
      }

      while (hinges.length < count) {
        hinges.push(null);
        debug(`Lade till null i hinges för att matcha count (${count})`);
      }

      while (openSizes.length < count) {
        const defaultSize = Math.floor(newConfig.width / count);
        openSizes.push(defaultSize);
        debug(`Lade till standardstorlek ${defaultSize} i openSizes för att matcha count (${count})`);
      }

      // Normalisera openSizes så att summan blir width
      const totalSize = openSizes.reduce((sum, size) => sum + size, 0);
      if (totalSize !== newConfig.width) {
        const scaleFactor = newConfig.width / totalSize;
        openSizes = openSizes.map(size => Math.round(size * scaleFactor));
        debug(`Normaliserade openSizes för att matcha total bredd (${newConfig.width}): ${openSizes.join(', ')}`);
      }

      // Uppdatera konfigurationen
      newConfig.count = count;
      newConfig.openings = openings;
      newConfig.hinges = hinges;
      newConfig.openSizes = openSizes;

    } catch (error) {
      debug(`Fel vid hämtning av konfiguration: ${error.message}`, 'error', error);
    }

    debug("3D-preview konfiguration:", 'info', newConfig);
    return newConfig;
  }

  /**
   * Initialiserar 3D-preview
   */
  function initialize() {
    if (isInitialized) {
      createWindow(); // Uppdatera fönstermodellen med aktuell konfiguration
      return;
    }

    container = document.getElementById('preview3d');
    if (!container) {
      return;
    }

    // Kontrollera att Three.js är laddat
    if (typeof THREE === 'undefined') {
      return;
    }

    try {
      // Sätt standardvy till insida
      isOutside = false;

      createScene();
      createLights();
      createWindow();
      createControls();

      // Uppdatera etiketten för att visa insida
      const viewLabel = document.getElementById('viewLabel');
      if (viewLabel) {
        viewLabel.textContent = 'Insida';
      }

      animate();

      // Starta kontinuerlig uppdatering av 3D-modellen
      startContinuousUpdate();

      isInitialized = true;
    } catch (error) {
      // Error handling without logging
    }
  }

  /**
   * Startar kontinuerlig uppdatering av 3D-modellen
   * Uppdaterar modellen var 2:a sekund för att säkerställa att den alltid är synkroniserad med konfigurationen
   */
  function startContinuousUpdate() {
    debug("Startar kontinuerlig uppdatering av 3D-modellen");

    // Avbryt eventuellt befintligt intervall
    if (window._3dUpdateInterval) {
      clearInterval(window._3dUpdateInterval);
      debug("Avbröt befintligt uppdateringsintervall");
    }

    // Uppdatera modellen var 2:a sekund
    window._3dUpdateInterval = setInterval(() => {
      if (isInitialized) {
        const newConfig = getCurrentConfig();
        updateConfig(newConfig);
        debug("Kontinuerlig uppdatering av 3D-modellen utförd");
      }
    }, 2000);

    debug(`Kontinuerlig uppdatering startad med intervall-ID: ${window._3dUpdateInterval}`);
  }

  /**
   * Skapar Three.js scen
   */
  function createScene() {
    // Skapa scen
    scene = new THREE.Scene();

    // Skapa en vit bakgrund för maximal kontrast
    scene.background = new THREE.Color(0xffffff); // Helt vit bakgrund

    // Skapa kamera
    camera = new THREE.PerspectiveCamera(
      35, // Bättre FOV för att visa hela fönstret
      container.clientWidth / container.clientHeight,
      0.1,
      2000
    );

    // Sätt kamerans position till maximal utzoomning
    const initialDistance = Math.max(200, Math.max(config.width, config.height) * 3);
    camera.position.set(0, 0, initialDistance);

    // Skapa renderer med förbättrad kvalitet
    renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: true
    });

    // Sätt storlek och pixelratio
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Begränsa för bättre prestanda

    // Aktivera skuggor med högre kvalitet
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Aktivera fysiskt korrekt belysning
    renderer.physicallyCorrectLights = true;

    // Aktivera tonemapping för bättre färgåtergivning
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;

    // Lägg till canvas i DOM
    container.appendChild(renderer.domElement);

    // Lyssna på storleksändringar
    window.addEventListener('resize', onWindowResize);
  }

  /**
   * Skapar ljus i scenen med förbättrad belysning från alla håll
   */
  function createLights() {
    // Ambient ljus - mycket starkare bakgrundsljus för att undvika mörka områden
    const ambientLight = new THREE.AmbientLight(0xffffff, 1.0);
    scene.add(ambientLight);

    // Hemisfäriskt ljus - ger mjuk belysning från alla riktningar
    const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0xffffff, 0.6);
    hemisphereLight.position.set(0, 100, 0);
    scene.add(hemisphereLight);

    // Huvudljus från framsidan
    const frontLight = new THREE.DirectionalLight(0xffffff, 0.8);
    frontLight.position.set(0, 0, 100);
    frontLight.castShadow = false;
    scene.add(frontLight);

    // Huvudljus från baksidan - lika starkt som framsidan
    const backLight = new THREE.DirectionalLight(0xffffff, 0.8);
    backLight.position.set(0, 0, -100);
    backLight.castShadow = false;
    scene.add(backLight);

    // Fyllnadsljus från vänster - lika starkt på båda sidor
    const leftFrontLight = new THREE.DirectionalLight(0xffffff, 0.6);
    leftFrontLight.position.set(-100, 0, 50);
    scene.add(leftFrontLight);

    const leftBackLight = new THREE.DirectionalLight(0xffffff, 0.6);
    leftBackLight.position.set(-100, 0, -50);
    scene.add(leftBackLight);

    // Fyllnadsljus från höger - lika starkt på båda sidor
    const rightFrontLight = new THREE.DirectionalLight(0xffffff, 0.6);
    rightFrontLight.position.set(100, 0, 50);
    scene.add(rightFrontLight);

    const rightBackLight = new THREE.DirectionalLight(0xffffff, 0.6);
    rightBackLight.position.set(100, 0, -50);
    scene.add(rightBackLight);

    // Fyllnadsljus från ovan - lika starkt på båda sidor
    const topFrontLight = new THREE.DirectionalLight(0xffffff, 0.5);
    topFrontLight.position.set(0, 100, 50);
    scene.add(topFrontLight);

    const topBackLight = new THREE.DirectionalLight(0xffffff, 0.5);
    topBackLight.position.set(0, 100, -50);
    scene.add(topBackLight);

    // Fyllnadsljus från nedan - lika starkt på båda sidor
    const bottomFrontLight = new THREE.DirectionalLight(0xffffff, 0.4);
    bottomFrontLight.position.set(0, -100, 50);
    scene.add(bottomFrontLight);

    const bottomBackLight = new THREE.DirectionalLight(0xffffff, 0.4);
    bottomBackLight.position.set(0, -100, -50);
    scene.add(bottomBackLight);

    // Punktljus för att framhäva detaljer - lika starkt på båda sidor
    const frontPointLight = new THREE.PointLight(0xffffff, 0.6, 300);
    frontPointLight.position.set(0, 0, 100);
    scene.add(frontPointLight);

    const backPointLight = new THREE.PointLight(0xffffff, 0.6, 300);
    backPointLight.position.set(0, 0, -100);
    scene.add(backPointLight);

    // Lägg till punktljus i varje hörn för att framhäva karmarna - på båda sidor
    const cornerLightIntensity = 0.3;
    const cornerLightDistance = 200;

    // Framsidans hörn
    const frontCornerLight1 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    frontCornerLight1.position.set(-50, 50, 50);
    scene.add(frontCornerLight1);

    const frontCornerLight2 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    frontCornerLight2.position.set(50, 50, 50);
    scene.add(frontCornerLight2);

    const frontCornerLight3 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    frontCornerLight3.position.set(-50, -50, 50);
    scene.add(frontCornerLight3);

    const frontCornerLight4 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    frontCornerLight4.position.set(50, -50, 50);
    scene.add(frontCornerLight4);

    // Baksidans hörn
    const backCornerLight1 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    backCornerLight1.position.set(-50, 50, -50);
    scene.add(backCornerLight1);

    const backCornerLight2 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    backCornerLight2.position.set(50, 50, -50);
    scene.add(backCornerLight2);

    const backCornerLight3 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    backCornerLight3.position.set(-50, -50, -50);
    scene.add(backCornerLight3);

    const backCornerLight4 = new THREE.PointLight(0xffffff, cornerLightIntensity, cornerLightDistance);
    backCornerLight4.position.set(50, -50, -50);
    scene.add(backCornerLight4);
  }

  /**
   * Skapar fönstermodellen
   */
  function createWindow() {
    debug("Skapar 3D-fönstermodell", 'info', config);

    // Ta bort gamla modellen om den finns
    if (frameGroup) {
      scene.remove(frameGroup);
      debug("Tog bort tidigare fönstermodell");
    }

    // Skapa en grupp för hela fönstermodellen
    windowModel = new THREE.Group();
    windowModel.name = "windowModel";

    // Skala om från cm till Three.js enheter (1:1)
    const scale = 1;
    const width = config.width * scale;
    const height = config.height * scale;
    const depth = config.depth * scale;
    const frameWidth = config.frameWidth * scale;

    debug(`Skapar fönster med dimensioner: ${width}x${height}x${depth}, antal luft: ${config.count}`);

    // Skapa insidan av fönstret
    const insideGroup = new THREE.Group();
    insideGroup.name = "insideGroup";
    windowModel.add(insideGroup);

    // Skapa en helt ny fönstermodell för insidan baserad på antal lufter
    createWindowFrame(insideGroup, width, height, depth, frameWidth, config.count, config.openSizes);

    // Skapa utsidan av fönstret (spegelvänd version)
    const outsideGroup = new THREE.Group();
    outsideGroup.name = "outsideGroup";
    outsideGroup.position.z = -depth; // Placera på utsidan
    outsideGroup.rotation.y = Math.PI; // Rotera 180 grader för att spegelvända
    windowModel.add(outsideGroup);

    // Skapa en spegelvänd version av fönstermodellen för utsidan
    createOutsideWindowFrame(outsideGroup, width, height, depth, frameWidth, config.count, config.openSizes);

    // Centrera fönstermodellen
    windowModel.position.set(0, 0, 0);

    // Skapa en grupp för ramen som kan roteras separat
    frameGroup = new THREE.Group();
    frameGroup.name = "frameGroup";
    frameGroup.add(windowModel);
    scene.add(frameGroup);

    // Skala modellen för att passa i vyn
    scaleModelToFit();

    // Logga att modellen har skapats
    debug("3D-fönstermodell skapad med både insida och utsida");
  }

  /**
   * Skapar en fönsterram med hål för varje luft - med inre ramar och handtag
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Total bredd
   * @param {number} height - Total höjd
   * @param {number} depth - Total djup
   * @param {number} frameWidth - Karmens bredd
   * @param {number} luftCount - Antal lufter
   * @param {Array} luftSizes - Storlekar för varje luft
   */
  function createWindowFrame(parent, width, height, depth, frameWidth, luftCount, luftSizes) {
    debug(`Skapar fönsterram med ${luftCount} lufter - med inre ramar och handtag`);

    // Skapa material för ramen - använd insidans färg
    const frameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: config.insideColor || config.frameColor })
    );

    // Skapa material för inre ramar - alltid grå färg för handtagskarmen
    const innerFrameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: 0x808080 }) // Fast grå färg
    );

    // Om det inte finns några lufter, skapa bara en yttre karm
    if (luftCount <= 0) {
      debug("Inga lufter att skapa, skapar endast yttre karm");
      createSolidFrame(parent, width, height, depth, frameWidth, frameMaterial);
      return;
    }

    // Beräkna total summa av alla luftstorlekar
    const totalSize = luftSizes.reduce((sum, size) => sum + size, 0);

    // Beräkna tillgänglig bredd efter att ha tagit hänsyn till yttre ram
    const availableWidth = width - frameWidth * 2;
    const postWidth = frameWidth;
    const totalPostWidth = (luftCount - 1) * postWidth;
    const luftAreaWidth = availableWidth - totalPostWidth;

    // Skapa yttre karm (topp, botten, vänster, höger)
    createFrameBorder(parent, width, height, depth, frameWidth, frameMaterial);

    // Beräkna position för första luften
    let currentX = -width / 2 + frameWidth;

    // Skapa varje luft med inre ram
    for (let i = 0; i < luftCount; i++) {
      // Beräkna bredd för denna luft
      const luftSize = luftSizes[i] || Math.floor(width / luftCount);
      const scaledWidth = (luftSize / totalSize) * luftAreaWidth;

      // Skapa inre ram för denna luft
      createInnerFrame(
        parent,
        currentX,
        0, // Centrerad vertikalt
        scaledWidth,
        height - frameWidth * 2,
        depth,
        frameWidth,
        innerFrameMaterial,
        i
      );

      // Uppdatera position för nästa luft
      currentX += scaledWidth;

      // Lägg till mittpost om det inte är den sista luften
      if (i < luftCount - 1) {
        const post = createBoxMesh(
          postWidth,
          height - frameWidth * 2,
          depth,
          frameMaterial,
          `post-${i}`
        );
        post.position.set(currentX + postWidth / 2, 0, 0);
        parent.add(post);

        currentX += postWidth;
      }
    }

    debug("Fönsterram med lufter och inre ramar skapad");
  }

  /**
   * Skapar en inre ram för en luft som sitter helt fast i den yttre ramen utan mellanrum
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} startX - Startposition X
   * @param {number} centerY - Centrumposition Y
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {number} frameWidth - Yttre rambredd
   * @param {Material} material - Material för inre ramen
   * @param {number} index - Index för denna luft
   */
  function createInnerFrame(parent, startX, centerY, width, height, depth, frameWidth, material, index) {
    debug(`Skapar inre ram för luft ${index+1}, position: ${startX}, bredd: ${width}`);

    // Beräkna centrum för denna luft
    const centerX = startX + width / 2;

    // Inre ramens bredd (70% mindre än yttre ramen)
    const innerFrameWidth = frameWidth * 0.3; // 30% av yttre ramens bredd

    // Inre ramens djup (lite mindre för att skapa en inåtgående kant)
    const innerDepth = depth - 2;

    // Skapa en grupp för den inre ramen
    const innerFrameGroup = new THREE.Group();
    innerFrameGroup.name = `innerFrameGroup-${index}`;
    innerFrameGroup.position.set(centerX, centerY, 0);
    innerFrameGroup.userData = {
      index: index,
      width: width,
      height: height,
      opening: config.openings[index],
      hinge: config.hinges[index],
      glassType: config.glassTypes[index] || config.glassType || 'clear'
    };
    parent.add(innerFrameGroup);

    // Beräkna den inre öppningens dimensioner
    // Använd innerFrameWidth istället för frameWidth för att göra den inre karmen tunnare
    const innerOpeningWidth = width - innerFrameWidth * 2;
    const innerOpeningHeight = height - innerFrameWidth * 2;

    // Skapa en inre ram som en enda extruderad form

    // Skapa en yttre kontur (samma storlek som luften)
    const outerShape = new THREE.Shape();
    outerShape.moveTo(-width/2, -height/2);
    outerShape.lineTo(width/2, -height/2);
    outerShape.lineTo(width/2, height/2);
    outerShape.lineTo(-width/2, height/2);
    outerShape.lineTo(-width/2, -height/2);

    // Skapa en inre kontur (hålet)
    const innerHole = new THREE.Path();
    innerHole.moveTo(-innerOpeningWidth/2, -innerOpeningHeight/2);
    innerHole.lineTo(innerOpeningWidth/2, -innerOpeningHeight/2);
    innerHole.lineTo(innerOpeningWidth/2, innerOpeningHeight/2);
    innerHole.lineTo(-innerOpeningWidth/2, innerOpeningHeight/2);
    innerHole.lineTo(-innerOpeningWidth/2, -innerOpeningHeight/2);

    // Lägg till hålet i formen
    outerShape.holes.push(innerHole);

    // Skapa extruderingsparametrar
    const extrudeSettings = {
      steps: 1,
      depth: innerDepth,
      bevelEnabled: false
    };

    // Skapa geometri genom extrudering
    const frameGeometry = new THREE.ExtrudeGeometry(outerShape, extrudeSettings);

    // Skapa mesh
    const frameMesh = new THREE.Mesh(frameGeometry, material);
    frameMesh.name = `innerFrameMesh-${index}`;

    // Centrera geometrin
    frameMesh.position.z = -innerDepth/2;

    // Lägg till i gruppen
    innerFrameGroup.add(frameMesh);

    // Skapa glas för denna luft med specifik glastyp
    // Gör glaset nästan lika stort som öppningen för att minimera mellanrum
    const glassWidth = innerOpeningWidth - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassHeight = innerOpeningHeight - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassGeometry = new THREE.BoxGeometry(glassWidth, glassHeight, 0.5); // Behåll tjockleken för synlighet

    // Använd specifik glastyp för denna luft om tillgänglig
    const glassType = config.glassTypes[index] || config.glassType || 'clear';
    let glassMaterial;

    if (glassType && glassMaterials[glassType]) {
      glassMaterial = glassMaterials[glassType]();
      debug(`Använder glasmaterial för typ: ${glassType} i luft ${index+1}`);
    } else {
      // Fallback till standardmaterial - helt transparent utan markeringar
      glassMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.1, // Mycket låg opacitet
        roughness: 0.0,
        metalness: 0.0,
        clearcoat: 0.0,
        clearcoatRoughness: 0.0,
        transmission: 0.99,
        side: THREE.DoubleSide,
        depthWrite: false,
        premultipliedAlpha: true
      });
      debug(`Använder standardglasmaterial i luft ${index+1} (ingen specifik typ hittades för: ${glassType})`);
    }

    // Skapa glaset
    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    glass.name = `glass-${index}`; // Namnge glaset med luftindex för att kunna uppdatera det senare
    glass.position.z = 0.1; // Placera exakt mot den inre karmen
    innerFrameGroup.add(glass);

    // Lägg till spröjs på insidan
    addSprojsToGlass(innerFrameGroup, glass, index, glassWidth, glassHeight, innerDepth, false);

    // Skapa namngivna delar för handtagsplacering (osynliga)
    // Använd innerFrameWidth istället för frameWidth för att placera handtagen på den inre karmen

    // Topp
    const topFrame = new THREE.Object3D();
    topFrame.name = `innerTopFrame-${index}`;
    topFrame.position.set(0, height/2 - innerFrameWidth/2, 0);
    innerFrameGroup.add(topFrame);

    // Botten
    const bottomFrame = new THREE.Object3D();
    bottomFrame.name = `innerBottomFrame-${index}`;
    bottomFrame.position.set(0, -height/2 + innerFrameWidth/2, 0);
    innerFrameGroup.add(bottomFrame);

    // Vänster
    const leftFrame = new THREE.Object3D();
    leftFrame.name = `innerLeftFrame-${index}`;
    leftFrame.position.set(-width/2 + innerFrameWidth/2, 0, 0);
    innerFrameGroup.add(leftFrame);

    // Höger
    const rightFrame = new THREE.Object3D();
    rightFrame.name = `innerRightFrame-${index}`;
    rightFrame.position.set(width/2 - innerFrameWidth/2, 0, 0);
    innerFrameGroup.add(rightFrame);

    // Kontrollera om detta är utsidan baserat på parent-objektets namn eller föräldrar
    const isOutside = parent.name && parent.name.toLowerCase().includes('outside') ||
                     (parent.parent && parent.parent.name && parent.parent.name.toLowerCase().includes('outside'));

    // Lägg till handtag om det är ett öppningsbart fönster och INTE är utsidan
    if (!isOutside && config.openings[index] && config.openings[index] !== 'fixed') {
      debug(`Lägger till handtag på insidan för luft ${index+1}`);
      addHandleToInnerFrame(
        innerFrameGroup,
        0, // Centrerad i innerFrameGroup
        0, // Centrerad i innerFrameGroup
        width, // Full bredd för korrekt placering
        height, // Full höjd för korrekt placering
        innerDepth,
        innerFrameWidth,
        config.openings[index],
        config.hinges[index],
        index
      );
    } else if (isOutside) {
      debug(`Inga handtag läggs till på utsidan för luft ${index+1} enligt önskemål`);
    }

    // Ta bort anrop till addGlassLayerLines för att undvika linjer på glaset
    // Vi behöver inte lägga till några linjer för glasskikt

    debug(`Inre ram för luft ${index+1} skapad utan mellanrum till yttre ramen (extruderad metod)`);
  }

  /**
   * Skapar en solid ram utan hål
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Total bredd
   * @param {number} height - Total höjd
   * @param {number} depth - Total djup
   * @param {number} frameWidth - Karmens bredd
   * @param {Material} frameMaterial - Material för ramen
   */
  function createSolidFrame(parent, width, height, depth, frameWidth, frameMaterial) {
    debug("Skapar solid ram utan hål");

    // Skapa yttre karm (hela rektangeln)
    const outerFrame = createBoxMesh(
      width,
      height,
      depth,
      frameMaterial,
      "outerFrame"
    );
    parent.add(outerFrame);

    // Skapa en "hålighet" i mitten
    const innerWidth = width - frameWidth * 2;
    const innerHeight = height - frameWidth * 2;

    // Skapa en inre box som representerar hålet
    const innerBoxGeometry = new THREE.BoxGeometry(
      innerWidth,
      innerHeight,
      depth * 2 // Dubbel djup för att säkerställa att hålet går igenom
    );

    // Skapa ett transparent material för hålet
    const holeMaterial = new THREE.MeshBasicMaterial({
      color: 0x000000,
      transparent: true,
      opacity: 0.0,
      side: THREE.DoubleSide,
      depthWrite: false
    });

    // Skapa hålet
    const innerBox = new THREE.Mesh(innerBoxGeometry, holeMaterial);
    innerBox.name = "innerHole";
    parent.add(innerBox);

    debug("Solid ram utan hål skapad");
  }

  /**
   * Skapar en ram runt kanten (topp, botten, vänster, höger)
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Total bredd
   * @param {number} height - Total höjd
   * @param {number} depth - Total djup
   * @param {number} frameWidth - Karmens bredd
   * @param {Material} frameMaterial - Material för ramen
   */
  function createFrameBorder(parent, width, height, depth, frameWidth, frameMaterial) {
    debug("Skapar ram runt kanten (topp, botten, vänster, höger)");

    // Kontrollera om detta är utsidan baserat på parent-objektets namn
    const isOutside = parent.name && parent.name.toLowerCase().includes('outside');

    // Använd rätt färg beroende på om det är insida eller utsida
    let material = frameMaterial;
    if (!material) {
      const color = isOutside ? config.outsideColor : config.insideColor || config.frameColor;
      material = new THREE.MeshStandardMaterial(
        Object.assign({}, config.frameMaterialProps, { color: color })
      );
      debug(`Skapar nytt material för ram med färg: ${color.toString(16)}`);
    }

    // Skapa topp-ram
    const topFrame = createBoxMesh(
      width,
      frameWidth,
      depth,
      material,
      isOutside ? "outsideTopFrame" : "topFrame"
    );
    topFrame.position.set(0, height / 2 - frameWidth / 2, 0);
    parent.add(topFrame);

    // Skapa botten-ram
    const bottomFrame = createBoxMesh(
      width,
      frameWidth,
      depth,
      material,
      isOutside ? "outsideBottomFrame" : "bottomFrame"
    );
    bottomFrame.position.set(0, -height / 2 + frameWidth / 2, 0);
    parent.add(bottomFrame);

    // Skapa vänster ram
    const leftFrame = createBoxMesh(
      frameWidth,
      height - frameWidth * 2, // Justera för att inte överlappa med topp och botten
      depth,
      material,
      isOutside ? "outsideLeftFrame" : "leftFrame"
    );
    leftFrame.position.set(-width / 2 + frameWidth / 2, 0, 0);
    parent.add(leftFrame);

    // Skapa höger ram
    const rightFrame = createBoxMesh(
      frameWidth,
      height - frameWidth * 2, // Justera för att inte överlappa med topp och botten
      depth,
      material,
      isOutside ? "outsideRightFrame" : "rightFrame"
    );
    rightFrame.position.set(width / 2 - frameWidth / 2, 0, 0);
    parent.add(rightFrame);

    debug(`Ram runt kanten skapad för ${isOutside ? "utsidan" : "insidan"}`);
  }



  /**
   * Skapar ett box-mesh med givna dimensioner
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {Material} material - Material
   * @param {string} name - Namn
   * @returns {Mesh} Mesh-objekt
   */
  function createBoxMesh(width, height, depth, material, name) {
    const geometry = new THREE.BoxGeometry(width, height, depth);
    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = name;
    return mesh;
  }

  /**
   * Lägger till ett realistiskt handtag direkt på den inre ramen
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} centerX - Centrumposition X
   * @param {number} centerY - Centrumposition Y
   * @param {number} width - Bredd på inre ramen
   * @param {number} height - Höjd på inre ramen
   * @param {number} depth - Djup på inre ramen
   * @param {number} frameWidth - Bredd på inre ramens karm
   * @param {string} openingType - Öppningstyp
   * @param {string} hingeType - Gångjärnstyp
   * @param {number} index - Index för denna luft
   */
  function addHandleToInnerFrame(parent, centerX, centerY, width, height, depth, frameWidth, openingType, hingeType, index) {
    // Kontrollera om detta är utsidan baserat på parent-objektets namn eller föräldrar
    const isOutside = parent.name && parent.name.toLowerCase().includes('outside') ||
                     (parent.parent && parent.parent.name && parent.parent.name.toLowerCase().includes('outside')) ||
                     (parent.parent && parent.parent.parent && parent.parent.parent.name && parent.parent.parent.name.toLowerCase().includes('outside'));

    // Inga handtag på utsidan enligt önskemål
    if (isOutside) {
      debug(`Inga handtag läggs till på utsidan för inre ram ${index+1} enligt önskemål`);
      return;
    }

    // Inre ramens bredd (70% mindre än yttre ramen)
    const innerFrameWidth = frameWidth * 0.3; // 30% av yttre ramens bredd
    debug(`Lägger till realistiskt handtag för inre ram ${index+1}, öppningstyp: ${openingType}, gångjärn: ${hingeType}`);

    // Skapa handtagets material - metalliskt utseende
    const handleMaterial = new THREE.MeshStandardMaterial({
      color: 0xCCCCCC, // Ljusare silverfärg
      roughness: 0.1,   // Mycket slät yta
      metalness: 0.9,   // Mycket metallisk
      emissive: 0x222222,
      emissiveIntensity: 0.05
    });

    // Material för handtagets bas - mörkare metall
    const baseMaterial = new THREE.MeshStandardMaterial({
      color: 0xAAAAAA, // Mörkare silverfärg
      roughness: 0.2,
      metalness: 0.8
    });

    // Bestäm handtagets position baserat på öppningstyp
    let handlePosition = { x: 0, y: 0, z: 0 };
    let handleRotation = { x: 0, y: 0, z: 0 };

    // Hitta rätt ram att placera handtaget på
    let targetFrame = null;
    let targetFrameName = "";

    switch (openingType) {
      case 'side-hung':
        if (hingeType === 'side-left') {
          // Handtag på höger sida (motsatt sida från gångjärnet)
          targetFrameName = `innerRightFrame-${index}`;
          handlePosition.x = 0; // Centrerat på ramen
          handlePosition.y = 0; // Centrerat på ramen
          handlePosition.z = innerFrameWidth / 2; // Placera på utsidan av den inre ramen
        } else {
          // Handtag på vänster sida (motsatt sida från gångjärnet)
          targetFrameName = `innerLeftFrame-${index}`;
          handlePosition.x = 0; // Centrerat på ramen
          handlePosition.y = 0; // Centrerat på ramen
          handlePosition.z = innerFrameWidth / 2; // Placera på utsidan av den inre ramen
        }
        break;
      case 'top-hung':
        // Handtag på nedre sidan (motsatt sida från gångjärnet)
        targetFrameName = `innerBottomFrame-${index}`;
        handlePosition.x = 0; // Centrerat på ramen
        handlePosition.y = 0; // Centrerat på ramen
        handlePosition.z = innerFrameWidth / 2; // Placera på utsidan av den inre ramen
        handleRotation.z = Math.PI / 2; // Rotera handtaget 90 grader
        break;
      case 'bottom-hung':
        // Handtag på övre sidan (motsatt sida från gångjärnet)
        targetFrameName = `innerTopFrame-${index}`;
        handlePosition.x = 0; // Centrerat på ramen
        handlePosition.y = 0; // Centrerat på ramen
        handlePosition.z = innerFrameWidth / 2; // Placera på utsidan av den inre ramen
        handleRotation.z = Math.PI / 2; // Rotera handtaget 90 grader
        break;
      case 'tilt-turn':
        // Handtag på höger sida
        targetFrameName = `innerRightFrame-${index}`;
        handlePosition.x = 0; // Centrerat på ramen
        handlePosition.y = 0; // Centrerat på ramen
        handlePosition.z = innerFrameWidth / 2; // Placera på utsidan av den inre ramen
        break;
    }

    // Hitta målramen i parent
    targetFrame = parent.getObjectByName(targetFrameName);

    if (!targetFrame) {
      debug(`Kunde inte hitta ramen ${targetFrameName} för handtaget`, 'warn');
      // Fallback: Lägg till handtaget direkt på parent
      targetFrame = parent;
    } else {
      debug(`Hittade ramen ${targetFrameName} för handtaget`);
    }

    // Skapa en grupp för handtaget och dess delar
    const handleGroup = new THREE.Group();
    handleGroup.name = `handleGroup-${index}`;

    // Dimensioner för handtagets bas (rektangulär platta)
    const basePlateWidth = 6;
    const basePlateHeight = 14; // Återställd till ursprunglig höjd
    const basePlateDepth = 1;

    // Skapa handtagets basplatta
    const basePlateGeometry = new THREE.BoxGeometry(basePlateWidth, basePlateHeight, basePlateDepth);
    const basePlate = new THREE.Mesh(basePlateGeometry, baseMaterial);
    basePlate.name = `handleBasePlate-${index}`;
    basePlate.position.set(handlePosition.x, handlePosition.y, handlePosition.z + basePlateDepth/2);
    basePlate.rotation.set(handleRotation.x, handleRotation.y, handleRotation.z);
    handleGroup.add(basePlate);

    // Dimensioner för handtagets grepp
    const gripWidth = 2;
    const gripHeight = 10; // Återställd till ursprunglig höjd
    const gripDepth = 2;
    const gripOffset = 3; // Avstånd från basplattan

    // Skapa handtagets grepp (horisontell del)
    const gripGeometry = new THREE.BoxGeometry(gripWidth, gripHeight, gripDepth);
    const grip = new THREE.Mesh(gripGeometry, handleMaterial);
    grip.name = `handleGrip-${index}`;
    grip.position.set(
      handlePosition.x,
      handlePosition.y, // Centrerat på basplattan
      handlePosition.z + basePlateDepth + gripOffset + gripDepth/2
    );
    grip.rotation.set(handleRotation.x, handleRotation.y, handleRotation.z);
    handleGroup.add(grip);

    // Dimensioner för handtagets anslutning till basplattan
    const connectorWidth = 2;
    const connectorHeight = 2;
    const connectorDepth = gripOffset;

    // Skapa endast den övre anslutningen (ingen nedre anslutning enligt bilden)
    const topConnectorGeometry = new THREE.BoxGeometry(connectorWidth, connectorHeight, connectorDepth);
    const topConnector = new THREE.Mesh(topConnectorGeometry, handleMaterial);
    topConnector.name = `handleConnector-${index}`;
    topConnector.position.set(
      handlePosition.x,
      handlePosition.y + gripHeight/2 - connectorHeight/2, // Anslutning i övre delen av greppet
      handlePosition.z + basePlateDepth + connectorDepth/2
    );
    topConnector.rotation.set(handleRotation.x, handleRotation.y, handleRotation.z);
    handleGroup.add(topConnector);

    // Lägg till handtagsgruppen på målramen
    targetFrame.add(handleGroup);

    // Ta bort anrop till addOpeningLinesToInnerFrame för att undvika linjer på glaset
    // Vi behöver inte lägga till några öppningslinjer på den inre ramen

    debug(`Realistiskt handtag för inre ram ${index+1} tillagt direkt på ramen ${targetFrameName}`);
  }

  /**
   * Lägger till öppningslinjer till den inre ramen
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} centerX - Centrumposition X
   * @param {number} centerY - Centrumposition Y
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {string} openingType - Öppningstyp
   * @param {string} hingeType - Gångjärnstyp
   * @param {number} index - Index för denna luft
   */
  function addOpeningLinesToInnerFrame(parent, centerX, centerY, width, height, depth, openingType, hingeType, index) {
    debug(`Inga öppningslinjer läggs till för inre ram ${index+1} enligt önskemål`);

    // Inga linjer läggs till enligt önskemål

    // Tomma case för att behålla strukturen
    switch (openingType) {
      case 'side-hung': {
        // Inga linjer för sidohängda fönster
        break;
      }

      case 'top-hung': {
        // Inga linjer för topphängda fönster
        break;
      }

      case 'bottom-hung': {
        // Inga linjer för bottenhängda fönster
        break;
      }
    }

    debug(`Inga öppningslinjer tillagda för inre ram ${index+1}`);
  }



  /**
   * Skapar insidan av fönstret (baserat på 2D-designen)
   */
  function createInsideView(parent, width, height, depth, frameWidth) {
    debug("Skapar insidan av fönstret");

    // Skapa en grupp för insidan
    const insideGroup = new THREE.Group();
    insideGroup.position.z = -depth / 2 - 0.1; // Placera på insidan
    insideGroup.name = "insideGroup";
    parent.add(insideGroup);

    // Inget behöver läggas till här eftersom createWindowFrame redan skapar hela modellen
    debug("Insidan av fönstret skapad");
  }

  /**
   * Skapar utsidan av fönstret (spegelvänd version av 2D-designen)
   */
  function createOutsideView(parent, width, height, depth, frameWidth) {
    debug("Skapar utsidan av fönstret");

    // Skapa en grupp för utsidan
    const outsideGroup = new THREE.Group();
    outsideGroup.position.z = depth / 2 + 0.1; // Placera på utsidan
    outsideGroup.rotation.y = Math.PI; // Rotera 180 grader för att spegelvända
    outsideGroup.name = "outsideGroup";
    parent.add(outsideGroup);

    // Skapa en spegelvänd version av fönstermodellen
    createMirroredWindowFrame(outsideGroup, width, height, depth, frameWidth, config.count, config.openSizes);

    debug("Utsidan av fönstret skapad");
  }

  /**
   * Lägger till linjer som visar antal glasskikt i ramen
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Bredd på ramen
   * @param {number} height - Höjd på ramen
   * @param {number} depth - Djup på ramen
   * @param {number} frameWidth - Bredd på karmen
   * @param {number} layers - Antal glasskikt (2 eller 3)
   * @param {boolean} isMirrored - Om detta är en spegelvänd ram
   */
  function addGlassLayerLines(parent, width, height, depth, frameWidth, layers, isMirrored) {
    debug(`Lägger till linjer för ${layers} glasskikt, spegelvänd: ${isMirrored}`);

    // Validera antal lager
    if (layers < 2) layers = 2;
    if (layers > 3) layers = 3;

    // Skapa material för linjerna
    const lineMaterial = new THREE.LineBasicMaterial({
      color: config.glassLayerColor,
      linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
    });

    // Beräkna positioner för linjerna i ramen
    const positions = [];

    if (layers === 2) {
      // För 2 glas: en linje i mitten
      positions.push(0);
    } else if (layers === 3) {
      // För 3 glas: två linjer med jämna mellanrum
      positions.push(-depth/4);
      positions.push(depth/4);
    }

    // Skapa linjer i alla fyra sidor av ramen
    const sides = [
      { name: 'top', start: [-width/2 + frameWidth, height/2 - frameWidth/2], end: [width/2 - frameWidth, height/2 - frameWidth/2], direction: 'horizontal' },
      { name: 'bottom', start: [-width/2 + frameWidth, -height/2 + frameWidth/2], end: [width/2 - frameWidth, -height/2 + frameWidth/2], direction: 'horizontal' },
      { name: 'left', start: [-width/2 + frameWidth/2, -height/2 + frameWidth], end: [-width/2 + frameWidth/2, height/2 - frameWidth], direction: 'vertical' },
      { name: 'right', start: [width/2 - frameWidth/2, -height/2 + frameWidth], end: [width/2 - frameWidth/2, height/2 - frameWidth], direction: 'vertical' }
    ];

    // Skapa linjer för varje position och sida
    positions.forEach((zPos, posIndex) => {
      sides.forEach((side, sideIndex) => {
        // Skapa geometri för linjen
        const points = [];

        if (side.direction === 'horizontal') {
          points.push(new THREE.Vector3(side.start[0], side.start[1], zPos));
          points.push(new THREE.Vector3(side.end[0], side.end[1], zPos));
        } else {
          points.push(new THREE.Vector3(side.start[0], side.start[1], zPos));
          points.push(new THREE.Vector3(side.end[0], side.end[1], zPos));
        }

        const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
        const line = new THREE.Line(lineGeometry, lineMaterial);

        // Namnge linjen för att kunna hitta den senare
        line.name = `glassLayer-${side.name}-${posIndex}`;

        // Lägg till linjen i föräldraobjektet
        parent.add(line);

        debug(`Lade till linje för glasskikt ${posIndex+1} på ${side.name}-sidan`);
      });
    });

    debug(`Lade till linjer för ${layers} glasskikt`);
  }

  /**
   * Skapar en spegelvänd version av fönstermodellen - med inre ramar och handtag
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Total bredd
   * @param {number} height - Total höjd
   * @param {number} depth - Total djup
   * @param {number} frameWidth - Karmens bredd
   * @param {number} luftCount - Antal lufter
   * @param {Array} luftSizes - Storlekar för varje luft
   */
  function createMirroredWindowFrame(parent, width, height, depth, frameWidth, luftCount, luftSizes) {
    debug(`Skapar spegelvänd fönsterram med ${luftCount} lufter - med inre ramar och handtag`);
    debug(`Använder utsidans färg: ${config.outsideColor.toString(16)}`);

    // Skapa material för ramen (använd outsideColor för utsidan)
    const frameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: config.outsideColor })
    );

    // Skapa material för inre ramar - alltid grå färg för handtagskarmen
    const innerFrameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: 0x808080 }) // Fast grå färg
    );

    // Om det inte finns några lufter, skapa bara en yttre karm
    if (luftCount <= 0) {
      debug("Inga lufter att skapa för spegelvänd modell, skapar endast yttre karm");
      createSolidFrame(parent, width, height, depth, frameWidth, frameMaterial);
      return;
    }

    // Beräkna total summa av alla luftstorlekar
    const totalSize = luftSizes.reduce((sum, size) => sum + size, 0);

    // Beräkna tillgänglig bredd efter att ha tagit hänsyn till yttre ram
    const availableWidth = width - frameWidth * 2;
    const postWidth = frameWidth;
    const totalPostWidth = (luftCount - 1) * postWidth;
    const luftAreaWidth = availableWidth - totalPostWidth;

    // Skapa yttre karm (topp, botten, vänster, höger)
    createFrameBorder(parent, width, height, depth, frameWidth, frameMaterial);

    // Beräkna position för första luften (spegelvänd ordning)
    let currentX = -width / 2 + frameWidth;

    // Skapa varje luft i omvänd ordning
    const reversedLuftSizes = [...luftSizes].reverse();
    const reversedOpenings = [...config.openings].reverse();
    const reversedHinges = [...config.hinges].reverse();

    // Skapa varje luft med inre ram
    for (let i = 0; i < luftCount; i++) {
      // Beräkna bredd för denna luft
      const luftSize = reversedLuftSizes[i] || Math.floor(width / luftCount);
      const scaledWidth = (luftSize / totalSize) * luftAreaWidth;

      // Skapa inre ram för denna luft
      createMirroredInnerFrame(
        parent,
        currentX,
        0, // Centrerad vertikalt
        scaledWidth,
        height - frameWidth * 2,
        depth,
        frameWidth,
        innerFrameMaterial,
        i,
        reversedOpenings[i],
        reversedHinges[i]
      );

      // Uppdatera position för nästa luft
      currentX += scaledWidth;

      // Lägg till mittpost om det inte är den sista luften
      if (i < luftCount - 1) {
        const post = createBoxMesh(
          postWidth,
          height - frameWidth * 2,
          depth,
          frameMaterial,
          `mirroredPost-${i}`
        );
        post.position.set(currentX + postWidth / 2, 0, 0);
        parent.add(post);

        currentX += postWidth;
      }
    }

    debug("Spegelvänd fönsterram med lufter och inre ramar skapad");
  }

  /**
   * Skapar en spegelvänd inre ram för en luft som sitter helt fast i den yttre ramen utan mellanrum
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} startX - Startposition X
   * @param {number} centerY - Centrumposition Y
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {number} frameWidth - Yttre rambredd
   * @param {Material} material - Material för inre ramen
   * @param {number} index - Index för denna luft
   * @param {string} openingType - Öppningstyp
   * @param {string} hingeType - Gångjärnstyp
   */
  function createMirroredInnerFrame(parent, startX, centerY, width, height, depth, frameWidth, material, index, openingType, hingeType) {
    debug(`Skapar spegelvänd inre ram för luft ${index+1}, position: ${startX}, bredd: ${width}`);

    // Beräkna centrum för denna luft
    const centerX = startX + width / 2;

    // Inre ramens bredd (70% mindre än yttre ramen)
    const innerFrameWidth = frameWidth * 0.3; // 30% av yttre ramens bredd

    // Inre ramens djup (lite mindre för att skapa en inåtgående kant)
    const innerDepth = depth - 2;

    // Skapa en grupp för den inre ramen
    const innerFrameGroup = new THREE.Group();
    innerFrameGroup.name = `mirroredInnerFrameGroup-${index}`;
    innerFrameGroup.position.set(centerX, centerY, 0);

    // Beräkna originalindex (eftersom vi skapar i omvänd ordning)
    const originalIndex = config.count - index - 1;

    innerFrameGroup.userData = {
      index: originalIndex, // Använd originalindex för att matcha insidan
      width: width,
      height: height,
      opening: openingType,
      hinge: hingeType,
      glassType: config.glassTypes[originalIndex] || config.glassType || 'clear'
    };
    parent.add(innerFrameGroup);

    // Beräkna den inre öppningens dimensioner
    // Använd innerFrameWidth istället för frameWidth för att göra den inre karmen tunnare
    const innerOpeningWidth = width - innerFrameWidth * 2;
    const innerOpeningHeight = height - innerFrameWidth * 2;

    // Skapa en inre ram som en enda extruderad form

    // Skapa en yttre kontur (samma storlek som luften)
    const outerShape = new THREE.Shape();
    outerShape.moveTo(-width/2, -height/2);
    outerShape.lineTo(width/2, -height/2);
    outerShape.lineTo(width/2, height/2);
    outerShape.lineTo(-width/2, height/2);
    outerShape.lineTo(-width/2, -height/2);

    // Skapa en inre kontur (hålet)
    const innerHole = new THREE.Path();
    innerHole.moveTo(-innerOpeningWidth/2, -innerOpeningHeight/2);
    innerHole.lineTo(innerOpeningWidth/2, -innerOpeningHeight/2);
    innerHole.lineTo(innerOpeningWidth/2, innerOpeningHeight/2);
    innerHole.lineTo(-innerOpeningWidth/2, innerOpeningHeight/2);
    innerHole.lineTo(-innerOpeningWidth/2, -innerOpeningHeight/2);

    // Lägg till hålet i formen
    outerShape.holes.push(innerHole);

    // Skapa extruderingsparametrar
    const extrudeSettings = {
      steps: 1,
      depth: innerDepth,
      bevelEnabled: false
    };

    // Skapa geometri genom extrudering
    const frameGeometry = new THREE.ExtrudeGeometry(outerShape, extrudeSettings);

    // Skapa mesh
    const frameMesh = new THREE.Mesh(frameGeometry, material);
    frameMesh.name = `mirroredInnerFrameMesh-${index}`;

    // Centrera geometrin
    frameMesh.position.z = -innerDepth/2;

    // Lägg till i gruppen
    innerFrameGroup.add(frameMesh);

    // Skapa glas för denna luft med specifik glastyp
    // Gör glaset nästan lika stort som öppningen för att minimera mellanrum
    const glassWidth = innerOpeningWidth - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassHeight = innerOpeningHeight - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassGeometry = new THREE.BoxGeometry(glassWidth, glassHeight, 0.5); // Behåll tjockleken för synlighet

    // Använd specifik glastyp för denna luft om tillgänglig
    // Notera att vi använder den omvända ordningen för glastyper eftersom lufterna är spegelvända
    const reversedIndex = config.count - index - 1;
    const glassType = config.glassTypes[reversedIndex] || config.glassType || 'clear';
    let glassMaterial;

    if (glassType && glassMaterials[glassType]) {
      glassMaterial = glassMaterials[glassType]();
      debug(`Använder glasmaterial för typ: ${glassType} i spegelvänd luft ${index+1} (original luft ${reversedIndex+1})`);
    } else {
      // Fallback till standardmaterial - helt transparent utan markeringar
      glassMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0.1, // Mycket låg opacitet
        roughness: 0.0,
        metalness: 0.0,
        clearcoat: 0.0,
        clearcoatRoughness: 0.0,
        transmission: 0.99,
        side: THREE.DoubleSide,
        depthWrite: false,
        premultipliedAlpha: true
      });
      debug(`Använder standardglasmaterial i spegelvänd luft ${index+1} (ingen specifik typ hittades för: ${glassType})`);
    }

    // Skapa glaset
    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    glass.name = `outsideGlass-${reversedIndex}`; // Namnge glaset med originalluftens index för att kunna uppdatera det senare
    glass.position.z = 0.1; // Placera exakt mot den inre karmen
    innerFrameGroup.add(glass);

    // Lägg till spröjs på utsidan
    addSprojsToGlass(innerFrameGroup, glass, reversedIndex, glassWidth, glassHeight, innerDepth, true);

    // Skapa namngivna delar för handtagsplacering (osynliga)
    // Använd innerFrameWidth istället för frameWidth för att placera handtagen på den inre karmen

    // Topp
    const topFrame = new THREE.Object3D();
    topFrame.name = `mirroredInnerTopFrame-${index}`;
    topFrame.position.set(0, height/2 - innerFrameWidth/2, 0);
    innerFrameGroup.add(topFrame);

    // Botten
    const bottomFrame = new THREE.Object3D();
    bottomFrame.name = `mirroredInnerBottomFrame-${index}`;
    bottomFrame.position.set(0, -height/2 + innerFrameWidth/2, 0);
    innerFrameGroup.add(bottomFrame);

    // Vänster
    const leftFrame = new THREE.Object3D();
    leftFrame.name = `mirroredInnerLeftFrame-${index}`;
    leftFrame.position.set(-width/2 + innerFrameWidth/2, 0, 0);
    innerFrameGroup.add(leftFrame);

    // Höger
    const rightFrame = new THREE.Object3D();
    rightFrame.name = `mirroredInnerRightFrame-${index}`;
    rightFrame.position.set(width/2 - innerFrameWidth/2, 0, 0);
    innerFrameGroup.add(rightFrame);

    // Inga handtag på utsidan enligt önskemål
    debug(`Inga handtag läggs till på utsidan för luft ${index+1} enligt önskemål`);

    // Spegelvända gångjärnstypen för utsidan (för eventuell framtida användning)
    let mirroredHingeType = hingeType;
    if (hingeType) {
      if (hingeType === 'side-left') {
        mirroredHingeType = 'side-right';
      } else if (hingeType === 'side-right') {
        mirroredHingeType = 'side-left';
      }
    }

    // Ta bort anrop till addGlassLayerLines för att undvika linjer på glaset
    // Vi behöver inte lägga till några linjer för glasskikt

    debug(`Spegelvänd inre ram för luft ${index+1} skapad utan mellanrum till yttre ramen (extruderad metod)`);
  }



  /**
   * Skapar en enkel luft
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {number} frameWidth - Rambredd
   * @param {boolean} isOutside - Om detta är utsidan (spegelvänd)
   */
  function createSingleFrame(parent, width, height, depth, frameWidth, isOutside) {
    // Skapa inre karm (hål för glaset)
    const innerWidth = width - frameWidth * 2;
    const innerHeight = height - frameWidth * 2;

    // Skapa ram
    const frameGeometry = new THREE.BoxGeometry(
      innerWidth,
      innerHeight,
      depth
    );
    const frameMaterial = new THREE.MeshStandardMaterial({
      color: config.insideColor,
      roughness: 0.7, // Mer realistisk yta
      metalness: 0.0, // Ingen metallisk effekt
      flatShading: false
    });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    parent.add(frame);

    // Skapa glas
    const glassWidth = innerWidth - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassHeight = innerHeight - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassGeometry = new THREE.BoxGeometry(
      glassWidth,
      glassHeight,
      0.5 // Behåll tjockleken för synlighet
    );

    // Skapa glasmaterial baserat på vald glastyp för första luften
    let glassMaterial;

    // Använd specifik glastyp för första luften om tillgänglig
    const glassType = config.glassTypes[0] || config.glassType || 'clear';

    // Använd specifikt material för vald glastyp om det finns
    if (glassType && glassMaterials[glassType]) {
      glassMaterial = glassMaterials[glassType]();
      debug(`Använder glasmaterial för typ: ${glassType} i createSingleFrame`);
    } else {
      // Fallback till standardmaterial
      glassMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xffffff, // Helt vitt för maximal genomskinlighet
        transparent: true,
        opacity: 0.05, // Nästan helt genomskinligt
        roughness: 0.0, // Helt slätt
        metalness: 0.0, // Ingen metallisk effekt
        clearcoat: 0.5, // Lätt beläggning
        clearcoatRoughness: 0.0, // Helt slät beläggning
        reflectivity: 0.2, // Låg reflektivitet
        transmission: 0.99, // Maximal transmission (genomskinlighet)
        side: THREE.DoubleSide, // Rendera båda sidor
        depthWrite: false, // Förhindra z-fighting
        premultipliedAlpha: true // Förbättrad transparens
      });
      debug(`Använder standardglasmaterial i createSingleFrame (ingen specifik typ hittades för: ${glassType})`);
    }

    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    glass.position.z = depth / 2 + 0.01; // Placera exakt mot den inre karmen
    glass.name = "glass-0"; // Namnge glaset med index 0 för att kunna uppdatera det senare
    parent.add(glass);

    // Om det är ett öppningsbart fönster, lägg till öppningslinjer och handtag
    if (config.openings[0] && config.openings[0] !== 'fixed') {
      // Lägg till öppningslinjer
      addOpeningLines(parent, config.openings[0], config.hinges[0], glassWidth, glassHeight, depth, isOutside);

      // För utsidan, spegelvända handtagets position
      const hingeType = config.hinges[0];
      let effectiveHingeType = hingeType;

      if (isOutside && hingeType) {
        // Spegelvända gångjärnstypen för utsidan
        if (hingeType === 'side-left') {
          effectiveHingeType = 'side-right';
        } else if (hingeType === 'side-right') {
          effectiveHingeType = 'side-left';
        }
      }

      addHandle(parent, config.openings[0], effectiveHingeType, glassWidth, glassHeight, isOutside);
    }
  }

  /**
   * Skapar flera lufter
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} totalWidth - Total bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {number} frameWidth - Rambredd
   * @param {boolean} isOutside - Om detta är utsidan (spegelvänd)
   */
  function createMultipleFrames(parent, totalWidth, height, depth, frameWidth, isOutside) {
    debug(`Skapar ${config.count} lufter, isOutside: ${isOutside}`);
    debug("Luftstorlekar:", 'info', config.openSizes);
    debug("Öppningstyper:", 'info', config.openings);
    debug("Gångjärnstyper:", 'info', config.hinges);

    // Skapa en container för alla lufter
    const framesContainer = new THREE.Group();
    framesContainer.name = isOutside ? "outsideFrames" : "insideFrames";
    parent.add(framesContainer);

    // Skapa mittpost(er)
    const postWidth = frameWidth;
    const postDepth = depth + 1; // Lite djupare för att undvika z-fighting

    // Beräkna total bredd för alla lufter
    let currentX = -totalWidth / 2 + frameWidth; // Börja från vänster kant

    // Om det är utsidan, börja från höger för att spegelvända
    const luftIndices = isOutside ? [...Array(config.count).keys()].reverse() : [...Array(config.count).keys()];

    // Säkerställ att vi har rätt antal storlekar
    if (config.openSizes.length !== config.count) {
      debug(`Varning: Antal storlekar (${config.openSizes.length}) matchar inte antal luft (${config.count})`, 'warn');
      // Justera storlekarna
      while (config.openSizes.length < config.count) {
        config.openSizes.push(Math.floor(totalWidth / config.count));
      }
      if (config.openSizes.length > config.count) {
        config.openSizes = config.openSizes.slice(0, config.count);
      }
    }

    // Beräkna total summa av alla luftstorlekar
    const totalSize = config.openSizes.reduce((sum, size) => sum + size, 0);

    // Beräkna tillgänglig bredd efter att ha tagit hänsyn till mittpostar
    const availableWidth = totalWidth - (config.count - 1) * postWidth;

    // Skapa en array för att lagra alla luftgrupper
    const frameGroups = [];

    for (let idx = 0; idx < config.count; idx++) {
      // Använd rätt index baserat på om det är insida eller utsida
      const i = luftIndices[idx];

      // Beräkna bredd för denna luft
      const luftWidth = config.openSizes[i] || Math.floor(totalWidth / config.count);
      // Beräkna skalad bredd baserat på tillgänglig bredd
      const scaledWidth = (luftWidth / totalSize) * availableWidth;

      debug(`Skapar luft ${i+1}/${config.count}, bredd: ${luftWidth}, skalad bredd: ${scaledWidth}, öppningstyp: ${config.openings[i]}`);

      // Skapa en grupp för denna luft
      const frameGroup = new THREE.Group();
      frameGroup.position.x = currentX + scaledWidth / 2;
      frameGroup.name = isOutside ? `outsideFrame-${i}` : `insideFrame-${i}`;
      frameGroup.userData = {
        index: i,
        width: scaledWidth,
        height: height - frameWidth * 2,
        opening: config.openings[i],
        hinge: config.hinges[i],
        isOutside: isOutside
      };
      framesContainer.add(frameGroup);
      frameGroups.push(frameGroup);

      // Skapa ram för denna luft
      createFrame(
        frameGroup,
        scaledWidth,
        height - frameWidth * 2,
        depth,
        config.openings[i],
        config.hinges[i],
        isOutside
      );

      // Uppdatera X-position för nästa luft
      currentX += scaledWidth;

      // Lägg till mittpost om det inte är den sista luften
      if (idx < config.count - 1) {
        createPost(
          framesContainer,
          postWidth,
          height - frameWidth * 2,
          postDepth,
          currentX,
          idx,
          isOutside
        );

        currentX += postWidth;
      }
    }

    // Spara referens till alla luftgrupper för senare användning
    framesContainer.userData = { frameGroups };

    debug(`Skapade ${config.count} lufter för ${isOutside ? "utsidan" : "insidan"}`);
    return framesContainer;
  }

  /**
   * Skapar en enskild fönsterram med glas och handtag
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {string} openingType - Öppningstyp
   * @param {string} hingeType - Gångjärnstyp
   * @param {boolean} isOutside - Om detta är utsidan
   */
  function createFrame(parent, width, height, depth, openingType, hingeType, isOutside) {
    debug(`Skapar fönsterram: ${width}x${height}, öppningstyp: ${openingType}, gångjärn: ${hingeType}, isOutside: ${isOutside}`);

    // Skapa ram - använd rätt färg beroende på om det är insida eller utsida
    const frameGeometry = new THREE.BoxGeometry(width, height, depth);
    const color = isOutside ? config.outsideColor : config.insideColor || config.frameColor;
    const frameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: color })
    );
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.name = isOutside ? "outsideFrameMesh" : "insideFrameMesh";
    parent.add(frame);

    debug(`Fönsterram skapad med färg: ${color.toString(16)}`);

    // Skapa glas
    const glassWidth = width - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassHeight = height - 0.5; // Minska mellanrummet till 0.25 mm på varje sida
    const glassGeometry = new THREE.BoxGeometry(glassWidth, glassHeight, 0.5); // Behåll tjockleken för synlighet

    // Skapa glasmaterial baserat på vald glastyp för denna luft
    let glassMaterial;

    // Hämta luftindex från parent
    let luftIndex = 0;
    if (parent.userData && typeof parent.userData.index !== 'undefined') {
      luftIndex = parent.userData.index;
    }

    // Använd specifik glastyp för denna luft om tillgänglig
    const glassType = config.glassTypes[luftIndex] || config.glassType || 'clear';

    // Använd specifikt material för vald glastyp om det finns
    if (glassType && glassMaterials[glassType]) {
      glassMaterial = glassMaterials[glassType]();
      debug(`Använder glasmaterial för typ: ${glassType} i luft ${luftIndex+1} (createFrame)`);
    } else {
      // Fallback till standardmaterial
      glassMaterial = new THREE.MeshPhysicalMaterial(
        Object.assign({}, config.glassMaterialProps, { color: config.glassColor })
      );
      debug(`Använder standardglasmaterial i luft ${luftIndex+1} (ingen specifik typ hittades för: ${glassType})`);
    }

    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    glass.position.z = depth / 2 + 0.01; // Placera exakt mot den inre karmen
    // Namnge glaset med luftindex för att kunna uppdatera det senare
    glass.name = isOutside ? `outsideGlass-${luftIndex}` : `insideGlass-${luftIndex}`;
    parent.add(glass);

    // Lägg till spröjs om det finns för denna luft
    addSprojsToGlass(parent, glass, luftIndex, glassWidth, glassHeight, depth, isOutside);

    // Lägg till öppningslinjer och handtag om det inte är ett fast fönster
    if (openingType && openingType !== 'fixed') {
      // Lägg till öppningslinjer
      addOpeningLines(parent, openingType, hingeType, glassWidth, glassHeight, depth, isOutside);

      // För utsidan, spegelvända handtagets position
      let effectiveHingeType = hingeType;
      if (isOutside && hingeType) {
        if (hingeType === 'side-left') {
          effectiveHingeType = 'side-right';
        } else if (hingeType === 'side-right') {
          effectiveHingeType = 'side-left';
        }
      }

      // Lägg till handtag
      addHandle(parent, openingType, effectiveHingeType, glassWidth, glassHeight, isOutside);
    }

    debug(`Fönsterram skapad: ${openingType}`);
  }

  /**
   * Lägger till spröjs på glaset
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {Object} glass - Glasobjektet
   * @param {number} luftIndex - Index för denna luft
   * @param {number} glassWidth - Glasets bredd
   * @param {number} glassHeight - Glasets höjd
   * @param {number} depth - Djup
   * @param {boolean} isOutside - Om detta är utsidan
   */
  function addSprojsToGlass(parent, glass, luftIndex, glassWidth, glassHeight, depth, isOutside) {
    // Hämta spröjstyp för denna luft
    const sprojsType = config.sprojsPerLuft && config.sprojsPerLuft[luftIndex]
      ? config.sprojsPerLuft[luftIndex]
      : config.sprojsType || 'none';

    debug(`Lägger till spröjs av typ '${sprojsType}' för luft ${luftIndex+1}, isOutside: ${isOutside}`);

    // Om inga spröjs, avsluta
    if (sprojsType === 'none') return;

    // Skapa en grupp för spröjs
    const sprojsGroup = new THREE.Group();
    sprojsGroup.name = isOutside ? `outsideSprojsGroup-${luftIndex}` : `sprojsGroup-${luftIndex}`;

    // Skapa material för spröjs (samma färg som ramen)
    const color = isOutside ? config.outsideColor : config.insideColor || config.frameColor;
    const sprojsMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: color })
    );

    // Konstanter för spröjs
    const sprojsWidth = 0.8; // Bredd på spröjs
    const sprojsDepth = 0.5; // Djup på spröjs
    const zOffset = isOutside ? -depth / 2 - 0.1 : depth / 2 + 0.1; // Z-position för spröjs (framför glaset)

    // Skapa spröjs baserat på typ
    switch (sprojsType) {
      case 'one-horizontal':
        // En horisontell spröjs
        createHorizontalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 1);
        break;

      case 'one-vertical':
        // En vertikal spröjs
        createVerticalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 1);
        break;

      case 'cross':
        // Korsande spröjs (en horisontell och en vertikal)
        createHorizontalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 1);
        createVerticalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 1);
        break;

      case 'grid':
        // Rutnät (två horisontella och två vertikala)
        createHorizontalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 2);
        createVerticalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, 2);
        break;

      case 'custom':
        // Anpassade spröjs
        const customSettings = config.customSprojsPerLuft && config.customSprojsPerLuft[luftIndex]
          ? config.customSprojsPerLuft[luftIndex]
          : { horizontal: 2, vertical: 2 };

        createHorizontalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, customSettings.horizontal);
        createVerticalSprojs(sprojsGroup, glassWidth, glassHeight, sprojsWidth, sprojsDepth, sprojsMaterial, zOffset, customSettings.vertical);
        break;
    }

    // Lägg till spröjsgruppen i föräldern
    parent.add(sprojsGroup);

    debug(`Spröjs skapade för luft ${luftIndex+1}`);
  }

  /**
   * Skapar horisontella spröjs
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} glassWidth - Glasets bredd
   * @param {number} glassHeight - Glasets höjd
   * @param {number} sprojsWidth - Spröjsens bredd
   * @param {number} sprojsDepth - Spröjsens djup
   * @param {Object} material - Material för spröjs
   * @param {number} zOffset - Z-position för spröjs
   * @param {number} count - Antal spröjs
   */
  function createHorizontalSprojs(parent, glassWidth, glassHeight, sprojsWidth, sprojsDepth, material, zOffset, count) {
    if (!count || count <= 0) return;

    // Skapa horisontella spröjs
    for (let i = 0; i < count; i++) {
      // Beräkna position för denna spröjs
      const position = (i + 1) * (glassHeight / (count + 1)) - glassHeight / 2;

      // Skapa geometri för spröjs
      const geometry = new THREE.BoxGeometry(glassWidth, sprojsWidth, sprojsDepth);

      // Skapa mesh för spröjs
      const sprojs = new THREE.Mesh(geometry, material);
      sprojs.position.set(0, position, zOffset);
      sprojs.name = `horizontalSprojs-${i}`;

      // Lägg till i föräldern
      parent.add(sprojs);
    }
  }

  /**
   * Skapar vertikala spröjs
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} glassWidth - Glasets bredd
   * @param {number} glassHeight - Glasets höjd
   * @param {number} sprojsWidth - Spröjsens bredd
   * @param {number} sprojsDepth - Spröjsens djup
   * @param {Object} material - Material för spröjs
   * @param {number} zOffset - Z-position för spröjs
   * @param {number} count - Antal spröjs
   */
  function createVerticalSprojs(parent, glassWidth, glassHeight, sprojsWidth, sprojsDepth, material, zOffset, count) {
    if (!count || count <= 0) return;

    // Skapa vertikala spröjs
    for (let i = 0; i < count; i++) {
      // Beräkna position för denna spröjs
      const position = (i + 1) * (glassWidth / (count + 1)) - glassWidth / 2;

      // Skapa geometri för spröjs
      const geometry = new THREE.BoxGeometry(sprojsWidth, glassHeight, sprojsDepth);

      // Skapa mesh för spröjs
      const sprojs = new THREE.Mesh(geometry, material);
      sprojs.position.set(position, 0, zOffset);
      sprojs.name = `verticalSprojs-${i}`;

      // Lägg till i föräldern
      parent.add(sprojs);
    }
  }

  /**
   * Skapar en mittpost mellan två lufter
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {number} xPosition - X-position
   * @param {number} index - Index för mittposten
   * @param {boolean} isOutside - Om detta är utsidan
   */
  function createPost(parent, width, height, depth, xPosition, index, isOutside) {
    debug(`Skapar mittpost ${index} på position ${xPosition}, isOutside: ${isOutside}`);

    const postGeometry = new THREE.BoxGeometry(width, height, depth);
    // Använd rätt färg beroende på om det är insida eller utsida
    const color = isOutside ? config.outsideColor : config.insideColor || config.frameColor;
    const postMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: color })
    );
    const post = new THREE.Mesh(postGeometry, postMaterial);
    post.position.set(xPosition, 0, 0);
    post.name = isOutside ? `outsidePost-${index}` : `insidePost-${index}`;
    parent.add(post);

    debug(`Mittpost ${index} skapad med färg: ${color.toString(16)}`);
    return post;
  }

  /**
   * Skapar en fönsterram för utsidan med hål för varje luft - utan handtag
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {number} width - Total bredd
   * @param {number} height - Total höjd
   * @param {number} depth - Total djup
   * @param {number} frameWidth - Karmens bredd
   * @param {number} luftCount - Antal lufter
   * @param {Array} luftSizes - Storlekar för varje luft
   */
  function createOutsideWindowFrame(parent, width, height, depth, frameWidth, luftCount, luftSizes) {
    debug(`Skapar utsidans fönsterram med ${luftCount} lufter - utan handtag`);
    debug(`Använder utsidans färg: ${config.outsideColor.toString(16)}`);

    // Skapa material för ramen - använd utsidans färg
    const frameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: config.outsideColor })
    );

    // Skapa material för inre ramar - alltid grå färg för handtagskarmen
    const innerFrameMaterial = new THREE.MeshStandardMaterial(
      Object.assign({}, config.frameMaterialProps, { color: 0x808080 }) // Fast grå färg
    );

    // Om det inte finns några lufter, skapa bara en yttre karm
    if (luftCount <= 0) {
      debug("Inga lufter att skapa för utsidan, skapar endast yttre karm");
      createSolidFrame(parent, width, height, depth, frameWidth, frameMaterial);
      return;
    }

    // Beräkna total summa av alla luftstorlekar
    const totalSize = luftSizes.reduce((sum, size) => sum + size, 0);

    // Beräkna tillgänglig bredd efter att ha tagit hänsyn till yttre ram
    const availableWidth = width - frameWidth * 2;
    const postWidth = frameWidth;
    const totalPostWidth = (luftCount - 1) * postWidth;
    const luftAreaWidth = availableWidth - totalPostWidth;

    // Skapa yttre karm (topp, botten, vänster, höger)
    createFrameBorder(parent, width, height, depth, frameWidth, frameMaterial);

    // Beräkna position för första luften
    let currentX = -width / 2 + frameWidth;

    // Skapa varje luft med inre ram - i omvänd ordning för utsidan
    const reversedIndices = [...Array(luftCount).keys()].reverse();

    for (let i = 0; i < luftCount; i++) {
      // Använd omvänd ordning för utsidan
      const originalIndex = reversedIndices[i];

      // Beräkna bredd för denna luft
      const luftSize = luftSizes[originalIndex] || Math.floor(width / luftCount);
      const scaledWidth = (luftSize / totalSize) * luftAreaWidth;

      // Skapa inre ram för denna luft
      createInnerFrame(
        parent,
        currentX,
        0, // Centrerad vertikalt
        scaledWidth,
        height - frameWidth * 2,
        depth,
        frameWidth,
        innerFrameMaterial,
        originalIndex
      );

      // Uppdatera position för nästa luft
      currentX += scaledWidth;

      // Lägg till mittpost om det inte är den sista luften
      if (i < luftCount - 1) {
        const post = createBoxMesh(
          postWidth,
          height - frameWidth * 2,
          depth,
          frameMaterial,
          `outsidePost-${originalIndex}`
        );
        post.position.set(currentX + postWidth / 2, 0, 0);
        parent.add(post);

        currentX += postWidth;
      }
    }

    debug("Utsidans fönsterram med lufter och inre ramar skapad");
  }

  /**
   * Lägger till öppningslinjer för att visa hur fönstret öppnas
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {string} opening - Öppningstyp
   * @param {string} hinge - Gångjärnstyp
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {number} depth - Djup
   * @param {boolean} isOutside - Om detta är utsidan
   */
  function addOpeningLines(parent, opening, hinge, width, height, depth, isOutside) {
    console.log(`Lägger till öppningslinjer för ${opening}, gångjärn: ${hinge}, isOutside: ${isOutside}`);

    // Förbättrade färger för linjerna - använd konfigurerad färg
    const lineColor = config.lineColor; // Ljusare grå för bättre synlighet
    const dashColor = 0xaaaaaa; // Ljusare grå för streckade linjer
    const hingeColor = 0x999999; // Ljusare för gångjärn

    // Skapa linjer baserat på öppningstyp
    switch (opening) {
      case 'side-hung': {
        // Skapa material med bättre synlighet
        const material = new THREE.LineBasicMaterial({
          color: lineColor,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        const dashMaterial = new THREE.LineDashedMaterial({
          color: dashColor,
          dashSize: 2,
          gapSize: 1,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        // Bestäm vilken sida gångjärnet är på
        const isLeft = hinge === 'side-left';
        const hingeX = isLeft ? -width / 2 : width / 2;
        const farX = -hingeX;

        // Skapa geometri för linjer
        const lineGeometry1 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(hingeX, 0, 0),
          new THREE.Vector3(farX, height / 2, 0)
        ]);

        const lineGeometry2 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(hingeX, 0, 0),
          new THREE.Vector3(farX, -height / 2, 0)
        ]);

        // Skapa linjer
        const line1 = new THREE.Line(lineGeometry1, material);
        const line2 = new THREE.Line(lineGeometry2, material);

        // Placera linjerna
        const zOffset = isOutside ? -depth / 2 - 0.1 : depth / 2 + 0.1;
        line1.position.z = zOffset;
        line2.position.z = zOffset;

        // Lägg till linjerna
        parent.add(line1);
        parent.add(line2);

        // Lägg till gångjärnsindikator
        const hingeHeight = height * 0.8;
        const hingeRadius = 0.8;
        const hingeSegments = 8;

        // Skapa gångjärn
        const hingeGeometry = new THREE.CylinderGeometry(
          hingeRadius, hingeRadius, hingeHeight, hingeSegments
        );
        const hingeMaterial = new THREE.MeshStandardMaterial({
          color: hingeColor,
          roughness: 0.5,
          metalness: 0.7
        });

        const hinge1 = new THREE.Mesh(hingeGeometry, hingeMaterial);
        hinge1.rotation.x = Math.PI / 2; // Rotera för att stå vertikalt
        hinge1.position.set(hingeX, 0, zOffset);

        parent.add(hinge1);

        break;
      }

      case 'top-hung': {
        // Skapa material med bättre synlighet
        const material = new THREE.LineBasicMaterial({
          color: lineColor,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        // Skapa geometri för linjer
        const lineGeometry1 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(-width / 2, height / 2, 0),
          new THREE.Vector3(0, -height / 2, 0)
        ]);

        const lineGeometry2 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(width / 2, height / 2, 0),
          new THREE.Vector3(0, -height / 2, 0)
        ]);

        // Skapa linjer
        const line1 = new THREE.Line(lineGeometry1, material);
        const line2 = new THREE.Line(lineGeometry2, material);

        // Placera linjerna
        const zOffset = isOutside ? -depth / 2 - 0.1 : depth / 2 + 0.1;
        line1.position.z = zOffset;
        line2.position.z = zOffset;

        // Lägg till linjerna
        parent.add(line1);
        parent.add(line2);

        // Lägg till gångjärnsindikator
        const hingeWidth = width * 0.8;
        const hingeRadius = 0.8;
        const hingeSegments = 8;

        // Skapa gångjärn
        const hingeGeometry = new THREE.CylinderGeometry(
          hingeRadius, hingeRadius, hingeWidth, hingeSegments
        );
        const hingeMaterial = new THREE.MeshStandardMaterial({
          color: hingeColor,
          roughness: 0.5,
          metalness: 0.7
        });

        const hinge1 = new THREE.Mesh(hingeGeometry, hingeMaterial);
        hinge1.rotation.z = Math.PI / 2; // Rotera för att ligga horisontellt
        hinge1.position.set(0, height / 2, zOffset);

        parent.add(hinge1);
        break;
      }

      case 'bottom-hung': {
        // Skapa material med bättre synlighet
        const material = new THREE.LineBasicMaterial({
          color: lineColor,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        // Skapa geometri för linjer
        const lineGeometry1 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(-width / 2, -height / 2, 0),
          new THREE.Vector3(0, height / 2, 0)
        ]);

        const lineGeometry2 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(width / 2, -height / 2, 0),
          new THREE.Vector3(0, height / 2, 0)
        ]);

        // Skapa linjer
        const line1 = new THREE.Line(lineGeometry1, material);
        const line2 = new THREE.Line(lineGeometry2, material);

        // Placera linjerna
        const zOffset = isOutside ? -depth / 2 - 0.1 : depth / 2 + 0.1;
        line1.position.z = zOffset;
        line2.position.z = zOffset;

        // Lägg till linjerna
        parent.add(line1);
        parent.add(line2);

        // Lägg till gångjärnsindikator
        const hingeWidth = width * 0.8;
        const hingeRadius = 0.8;
        const hingeSegments = 8;

        // Skapa gångjärn
        const hingeGeometry = new THREE.CylinderGeometry(
          hingeRadius, hingeRadius, hingeWidth, hingeSegments
        );
        const hingeMaterial = new THREE.MeshStandardMaterial({
          color: hingeColor,
          roughness: 0.5,
          metalness: 0.7
        });

        const hinge1 = new THREE.Mesh(hingeGeometry, hingeMaterial);
        hinge1.rotation.z = Math.PI / 2; // Rotera för att ligga horisontellt
        hinge1.position.set(0, -height / 2, zOffset);

        parent.add(hinge1);
        break;
      }

      case 'tilt-turn': {
        // Skapa material med bättre synlighet
        const material = new THREE.LineBasicMaterial({
          color: lineColor,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        const dashMaterial = new THREE.LineDashedMaterial({
          color: dashColor,
          dashSize: 2,
          gapSize: 1,
          linewidth: 2 // Tjockare linjer (fungerar inte i alla webbläsare)
        });

        // Skapa geometri för linjer
        const lineGeometry1 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(0, height / 2, 0),
          new THREE.Vector3(-width / 2, 0, 0)
        ]);

        const lineGeometry2 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(0, height / 2, 0),
          new THREE.Vector3(width / 2, 0, 0)
        ]);

        const lineGeometry3 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(-width / 2, 0, 0),
          new THREE.Vector3(0, -height / 2, 0)
        ]);

        const lineGeometry4 = new THREE.BufferGeometry().setFromPoints([
          new THREE.Vector3(width / 2, 0, 0),
          new THREE.Vector3(0, -height / 2, 0)
        ]);

        // Skapa linjer
        const line1 = new THREE.Line(lineGeometry1, dashMaterial);
        const line2 = new THREE.Line(lineGeometry2, dashMaterial);
        const line3 = new THREE.Line(lineGeometry3, material);
        const line4 = new THREE.Line(lineGeometry4, material);

        // Placera linjerna
        const zOffset = isOutside ? -depth / 2 - 0.1 : depth / 2 + 0.1;
        line1.position.z = zOffset;
        line2.position.z = zOffset;
        line3.position.z = zOffset;
        line4.position.z = zOffset;

        // Lägg till linjerna
        parent.add(line1);
        parent.add(line2);
        parent.add(line3);
        parent.add(line4);

        // Lägg till gångjärnsindikator för sidohängning
        const hingeHeight = height * 0.6;
        const hingeRadius = 0.8;
        const hingeSegments = 8;

        // Skapa gångjärn
        const hingeGeometry = new THREE.CylinderGeometry(
          hingeRadius, hingeRadius, hingeHeight, hingeSegments
        );
        const hingeMaterial = new THREE.MeshStandardMaterial({
          color: hingeColor,
          roughness: 0.5,
          metalness: 0.7
        });

        const hinge1 = new THREE.Mesh(hingeGeometry, hingeMaterial);
        hinge1.rotation.x = Math.PI / 2; // Rotera för att stå vertikalt
        hinge1.position.set(width / 2, 0, zOffset);

        // Lägg till gångjärnsindikator för topphängning
        const hingeWidth = width * 0.6;

        const hingeGeometry2 = new THREE.CylinderGeometry(
          hingeRadius, hingeRadius, hingeWidth, hingeSegments
        );

        const hinge2 = new THREE.Mesh(hingeGeometry2, hingeMaterial);
        hinge2.rotation.z = Math.PI / 2; // Rotera för att ligga horisontellt
        hinge2.position.set(0, height / 2, zOffset);

        parent.add(hinge1);
        parent.add(hinge2);
        break;
      }
    }
  }

  /**
   * Lägger till handtag baserat på öppningstyp
   * @param {Object} parent - Föräldraobjekt att lägga till i
   * @param {string} openingType - Öppningstyp
   * @param {string} hingeType - Gångjärnstyp
   * @param {number} width - Bredd
   * @param {number} height - Höjd
   * @param {boolean} isOutside - Om detta är utsidan
   */
  function addHandle(parent, openingType, hingeType, width, height, isOutside) {
    // Inga handtag på utsidan enligt önskemål
    if (isOutside) {
      debug(`Inga handtag läggs till på utsidan enligt önskemål`);
      return;
    }



    // Skapa handtag (enkel version)
    const handleWidth = 4;
    const handleHeight = 15;
    const handleDepth = 3;

    const handleGeometry = new THREE.BoxGeometry(
      handleWidth,
      handleHeight,
      handleDepth
    );
    const handleMaterial = new THREE.MeshStandardMaterial({
      color: config.handleColor, // Använd konfigurerad färg
      roughness: 0.3, // Slätare yta för metalliskt utseende
      metalness: 0.8, // Mer metallisk effekt
      flatShading: false
    });
    const handle = new THREE.Mesh(handleGeometry, handleMaterial);

    // Z-position för handtaget (alltid på insidan)
    const zOffset = 2 + handleDepth / 2;

    // Placera handtaget baserat på öppningstyp
    switch (openingType) {
      case 'side-hung':
        if (hingeType === 'side-left') {
          handle.position.set(width / 2 - handleWidth / 2, 0, zOffset);
        } else {
          handle.position.set(-width / 2 + handleWidth / 2, 0, zOffset);
        }
        break;
      case 'top-hung':
        handle.position.set(0, -height / 2 + handleHeight / 2, zOffset);
        handle.rotation.z = Math.PI / 2;
        break;
      case 'bottom-hung':
        handle.position.set(0, height / 2 - handleHeight / 2, zOffset);
        handle.rotation.z = Math.PI / 2;
        break;
      case 'tilt-turn':
        handle.position.set(width / 2 - handleWidth / 2, 0, zOffset);
        break;
    }

    parent.add(handle);
  }

  /**
   * Skalar modellen för att passa i vyn
   */
  function scaleModelToFit() {
    // Beräkna aspektförhållande för fönstret
    const windowAspect = config.width / config.height;

    // Beräkna aspektförhållande för containern
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const containerAspect = containerWidth / containerHeight;

    // Beräkna skalningsfaktor med mer utrymme (0.75 istället för 0.6)
    let scale;
    if (windowAspect > containerAspect) {
      // Fönstret är bredare än containern
      scale = (containerWidth * 0.75) / config.width;
    } else {
      // Fönstret är högre än containern
      scale = (containerHeight * 0.75) / config.height;
    }

    // Skala modellen
    frameGroup.scale.set(scale, scale, scale);

    // Centrera modellen
    frameGroup.position.set(0, 0, 0);

    // Behåll kamerans position för att tillåta användaren att zooma in och utforska
    // Uppdatera bara kamerans position om det är första gången modellen skapas
    if (!isInitialized) {
      const maxDistance = Math.max(config.width, config.height) * 3; // Samma som maxDistance i createControls
      camera.position.z = maxDistance;
    }

    // Uppdatera kontrollerna
    if (controls) {
      controls.target.set(0, 0, 0);
      controls.update();
    }
  }

  /**
   * Skapar kontroller för interaktion
   */
  function createControls() {
    // Skapa OrbitControls för interaktiv rotation
    controls = new THREE.OrbitControls(camera, renderer.domElement);

    // Grundläggande inställningar
    controls.enableDamping = true;
    controls.dampingFactor = 0.1; // Ökad dämpning för mjukare rörelse
    controls.screenSpacePanning = true; // Tillåt panorering i skärmrymden

    // Avståndsbegränsningar
    const minDistance = Math.max(config.width, config.height) * 0.8;
    const maxDistance = Math.max(config.width, config.height) * 3;
    controls.minDistance = minDistance;
    controls.maxDistance = maxDistance;

    // Rotationsbegränsningar
    controls.maxPolarAngle = Math.PI * 0.8; // Tillåt mer rotation nedåt
    controls.minPolarAngle = Math.PI * 0.2; // Begränsa rotation uppåt

    // Automatisk rotation
    controls.autoRotate = false; // Ingen automatisk rotation
    controls.autoRotateSpeed = 0.5; // Långsammare rotation

    // Centrera kontrollen
    controls.target.set(0, 0, 0);

    // Sätt kameran till maximal utzoomning endast vid första initialiseringen
    if (!isInitialized) {
      camera.position.z = maxDistance;
    }

    controls.update();

    // Sätt standardvy till insida
    setView('inside');

    // Dölj kontrollknappar (borttagna från HTML)
    const viewLabel = document.getElementById('viewLabel');
    if (viewLabel) {
      viewLabel.textContent = 'Insida';
      viewLabel.classList.remove('hidden');
    }
  }

  /**
   * Sätter vyn till insida (alltid insida enligt önskemål)
   * @param {string} view - ignoreras, alltid 'inside'
   */
  function setView(view) {
    // Sätt alltid till insida
    isOutside = false;

    // Sätt rotation för insida
    if (frameGroup) {
      frameGroup.rotation.y = Math.PI; // Rotation för insida
    }

    // Uppdatera etiketten
    const viewLabel = document.getElementById('viewLabel');
    if (viewLabel) {
      viewLabel.textContent = 'Insida';
    }
  }

  /**
   * Växlar automatisk rotation
   */
  function toggleRotation() {
    autoRotate = !autoRotate;
    controls.autoRotate = autoRotate;
  }

  /**
   * Vänder fönstret för att visa insida/utsida (inaktiverad enligt önskemål)
   */
  function flipWindow() {
    // Gör ingenting - funktionen är inaktiverad enligt önskemål

    // Säkerställ att vi alltid visar insida
    setView('inside');
  }

  /**
   * Hanterar storleksändring av fönstret
   */
  function onWindowResize() {
    if (!isInitialized || !container) {
      return;
    }

    // Hämta nya dimensioner
    const width = container.clientWidth;
    const height = container.clientHeight;

    // Uppdatera kamera
    camera.aspect = width / height;
    camera.updateProjectionMatrix();

    // Uppdatera renderer
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // Skala om modellen
    scaleModelToFit();

    // Forcera en extra rendering
    if (renderer && scene && camera) {
      renderer.render(scene, camera);
    }
  }

  /**
   * Animationsloop
   */
  function animate() {
    requestAnimationFrame(animate);

    if (controls) {
      controls.update();

      // Detektera om användaren ser insidan eller utsidan baserat på rotation
      if (frameGroup) {
        // Beräkna vinkel mellan kamerans riktning och fönstrets normal
        const cameraDirection = new THREE.Vector3(0, 0, -1);
        cameraDirection.applyQuaternion(camera.quaternion);

        // Beräkna fönstrets normal (z-axel) efter rotation
        const windowNormal = new THREE.Vector3(0, 0, 1);
        windowNormal.applyQuaternion(frameGroup.quaternion);

        // Beräkna skalärprodukten för att avgöra om vi ser framsidan eller baksidan
        const dotProduct = cameraDirection.dot(windowNormal);

        // Uppdatera etiketten baserat på vilken sida som visas
        const viewLabel = document.getElementById('viewLabel');
        if (viewLabel) {
          const isViewingInside = dotProduct < 0;
          viewLabel.textContent = isViewingInside ? 'Insida' : 'Utsida';
        }
      }
    }

    if (renderer && scene && camera) renderer.render(scene, camera);
  }

  /**
   * Uppdaterar fönstermodellen baserat på konfiguration
   */
  function updateConfig(newConfig) {
    console.log("Uppdaterar 3D-modell med ny konfiguration:", newConfig);

    // Validera konfigurationen
    if (!newConfig) {
      console.error("Ingen konfiguration tillhandahållen!");
      return;
    }

    // Spara tidigare värden för att se om de har ändrats
    const previousGlassType = config.glassType;
    const previousGlassLayers = config.glassLayers;
    const previousFrameColor = config.frameColor;
    const previousInsideColor = config.insideColor;
    const previousOutsideColor = config.outsideColor;

    // Logga färgändringar
    if (newConfig.frameColor && newConfig.frameColor !== previousFrameColor) {
      debug(`Ramfärg ändrad från ${previousFrameColor.toString(16)} till ${newConfig.frameColor.toString(16)}`);
    }
    if (newConfig.insideColor && newConfig.insideColor !== previousInsideColor) {
      debug(`Insidefärg ändrad från ${previousInsideColor.toString(16)} till ${newConfig.insideColor.toString(16)}`);
    }
    if (newConfig.outsideColor && newConfig.outsideColor !== previousOutsideColor) {
      debug(`Utsidefärg ändrad från ${previousOutsideColor.toString(16)} till ${newConfig.outsideColor.toString(16)}`);
    }

    // Säkerställ att arrays har rätt längd
    if (newConfig.count) {
      if (newConfig.openings && newConfig.openings.length !== newConfig.count) {
        console.warn(`Justerar längden på openings array från ${newConfig.openings.length} till ${newConfig.count}`);
        while (newConfig.openings.length < newConfig.count) newConfig.openings.push('fixed');
        if (newConfig.openings.length > newConfig.count) newConfig.openings = newConfig.openings.slice(0, newConfig.count);
      }

      if (newConfig.hinges && newConfig.hinges.length !== newConfig.count) {
        console.warn(`Justerar längden på hinges array från ${newConfig.hinges.length} till ${newConfig.count}`);
        while (newConfig.hinges.length < newConfig.count) newConfig.hinges.push(null);
        if (newConfig.hinges.length > newConfig.count) newConfig.hinges = newConfig.hinges.slice(0, newConfig.count);
      }

      if (newConfig.openSizes && newConfig.openSizes.length !== newConfig.count) {
        console.warn(`Justerar längden på openSizes array från ${newConfig.openSizes.length} till ${newConfig.count}`);
        const avgSize = Math.floor(newConfig.width / newConfig.count);
        while (newConfig.openSizes.length < newConfig.count) newConfig.openSizes.push(avgSize);
        if (newConfig.openSizes.length > newConfig.count) newConfig.openSizes = newConfig.openSizes.slice(0, newConfig.count);
      }

      // Säkerställ att glassTypes har rätt längd
      if (newConfig.glassTypes && newConfig.glassTypes.length !== newConfig.count) {
        console.warn(`Justerar längden på glassTypes array från ${newConfig.glassTypes.length} till ${newConfig.count}`);
        // Använd global glastyp som fallback
        const defaultGlassType = newConfig.glassType || 'clear';
        while (newConfig.glassTypes.length < newConfig.count) newConfig.glassTypes.push(defaultGlassType);
        if (newConfig.glassTypes.length > newConfig.count) newConfig.glassTypes = newConfig.glassTypes.slice(0, newConfig.count);
      } else if (!newConfig.glassTypes) {
        // Om glassTypes saknas helt, skapa en ny array med global glastyp
        newConfig.glassTypes = Array(newConfig.count).fill(newConfig.glassType || 'clear');
        console.warn(`Skapade ny glassTypes array med ${newConfig.count} element`);
      }

      // Säkerställ att sprojsPerLuft har rätt längd
      if (newConfig.sprojsPerLuft && newConfig.sprojsPerLuft.length !== newConfig.count) {
        console.warn(`Justerar längden på sprojsPerLuft array från ${newConfig.sprojsPerLuft.length} till ${newConfig.count}`);
        // Använd global spröjstyp som fallback
        const defaultSprojsType = newConfig.sprojsType || 'none';
        while (newConfig.sprojsPerLuft.length < newConfig.count) newConfig.sprojsPerLuft.push(defaultSprojsType);
        if (newConfig.sprojsPerLuft.length > newConfig.count) newConfig.sprojsPerLuft = newConfig.sprojsPerLuft.slice(0, newConfig.count);
      } else if (!newConfig.sprojsPerLuft) {
        // Om sprojsPerLuft saknas helt, skapa en ny array med global spröjstyp
        newConfig.sprojsPerLuft = Array(newConfig.count).fill(newConfig.sprojsType || 'none');
        console.warn(`Skapade ny sprojsPerLuft array med ${newConfig.count} element`);
      }

      // Säkerställ att customSprojsPerLuft har rätt längd
      if (newConfig.customSprojsPerLuft && newConfig.customSprojsPerLuft.length !== newConfig.count) {
        console.warn(`Justerar längden på customSprojsPerLuft array från ${newConfig.customSprojsPerLuft.length} till ${newConfig.count}`);
        // Använd standardvärden som fallback
        const defaultCustomSprojs = { horizontal: 2, vertical: 2 };
        while (newConfig.customSprojsPerLuft.length < newConfig.count) newConfig.customSprojsPerLuft.push(defaultCustomSprojs);
        if (newConfig.customSprojsPerLuft.length > newConfig.count) newConfig.customSprojsPerLuft = newConfig.customSprojsPerLuft.slice(0, newConfig.count);
      } else if (!newConfig.customSprojsPerLuft) {
        // Om customSprojsPerLuft saknas helt, skapa en ny array med standardvärden
        newConfig.customSprojsPerLuft = Array(newConfig.count).fill({ horizontal: 2, vertical: 2 });
        console.warn(`Skapade ny customSprojsPerLuft array med ${newConfig.count} element`);
      }
    }

    // Uppdatera konfigurationen
    Object.assign(config, newConfig);

    // Om redan initialiserad, uppdatera modellen
    if (isInitialized) {
      console.log("Uppdaterar befintlig 3D-modell");
      try {
        // Kontrollera om färger, glastyp eller antal glasskikt har ändrats
        const colorChanged = (newConfig.frameColor && previousFrameColor !== newConfig.frameColor) ||
                            (newConfig.insideColor && previousInsideColor !== newConfig.insideColor) ||
                            (newConfig.outsideColor && previousOutsideColor !== newConfig.outsideColor);

        if (colorChanged) {
          debug(`Färger har ändrats, återskapar modellen`);
          // Om färger har ändrats behöver vi uppdatera hela modellen
          createWindow();
        } else if ((previousGlassType !== config.glassType && newConfig.glassType) ||
                  (previousGlassLayers !== config.glassLayers && newConfig.glassLayers)) {

          if (previousGlassType !== config.glassType) {
            debug(`Glastyp ändrad från ${previousGlassType} till ${config.glassType}, uppdaterar glasmaterial`);
          }

          if (previousGlassLayers !== config.glassLayers) {
            debug(`Antal glasskikt ändrat från ${previousGlassLayers} till ${config.glassLayers}, uppdaterar modellen`);
            // Om antal glasskikt har ändrats behöver vi uppdatera hela modellen
            createWindow();
          } else {
            // Om endast glastypen har ändrats räcker det med att uppdatera glasmaterialet
            updateGlassMaterial();
          }
        } else {
          // Annars uppdatera hela modellen
          createWindow();
        }
      } catch (error) {
        // Error handling without logging
      }
    }
  }

  /**
   * Uppdaterar glasmaterialet på alla glasrutor i modellen
   * Stödjer olika glastyper per luft
   */
  function updateGlassMaterial() {
    debug(`Uppdaterar glasmaterial för ${config.count} lufter med glastyper: ${config.glassTypes.join(', ')}`);

    if (!scene) {
      debug("Ingen scen finns, kan inte uppdatera glasmaterial", 'warn');
      return;
    }

    // Skapa en cache för glasmaterial för att undvika att skapa samma material flera gånger
    const materialCache = {};

    // Skapa eller hämta glasmaterial för en specifik typ
    function getGlassMaterial(type) {
      // Om materialet redan finns i cachen, återanvänd det
      if (materialCache[type]) {
        return materialCache[type];
      }

      // Annars skapa ett nytt material
      let material;
      if (type && glassMaterials[type]) {
        material = glassMaterials[type]();
        debug(`Skapar nytt glasmaterial för typ: ${type}`);
      } else {
        // Fallback till standardmaterial
        material = new THREE.MeshPhysicalMaterial(
          Object.assign({}, config.glassMaterialProps, { color: config.glassColor })
        );
        debug(`Skapar standardglasmaterial (ingen specifik typ hittades för: ${type})`);
      }

      // Spara i cachen för framtida användning
      materialCache[type] = material;
      return material;
    }

    // Hitta alla glasrutor i scenen och uppdatera deras material
    let updatedCount = 0;

    // Först uppdatera glasrutor som tillhör specifika lufter
    for (let i = 0; i < config.count; i++) {
      const glassType = config.glassTypes[i] || config.glassType || 'clear';
      const glassMaterial = getGlassMaterial(glassType);

      // Hitta glasrutor för denna luft
      scene.traverse(function(object) {
        if (object.isMesh && (
            object.name === `glass-${i}` ||
            object.name === `insideGlass-${i}` ||
            object.name === `outsideGlass-${i}` ||
            object.name.includes(`glass-${i}-`)
          )) {
          // Spara objektets position och rotation
          const position = object.position.clone();
          const rotation = object.rotation.clone();

          // Uppdatera materialet
          if (object.material) object.material.dispose(); // Frigör minne
          object.material = glassMaterial;

          // Återställ position och rotation
          object.position.copy(position);
          object.rotation.copy(rotation);

          updatedCount++;
          debug(`Uppdaterade material för glasruta ${object.name} till typ: ${glassType}`);
        }
      });
    }

    // Sedan uppdatera eventuella glasrutor som inte tillhör en specifik luft
    // med den globala glastypen
    const defaultGlassMaterial = getGlassMaterial(config.glassType || 'clear');

    scene.traverse(function(object) {
      if (object.isMesh && (
          (object.name === "insideGlass" ||
           object.name === "outsideGlass" ||
           object.name.includes("glass")) &&
          !object.name.match(/-\d+/) // Exkludera glasrutor som redan har uppdaterats
        )) {
        // Spara objektets position och rotation
        const position = object.position.clone();
        const rotation = object.rotation.clone();

        // Uppdatera materialet
        if (object.material) object.material.dispose(); // Frigör minne
        object.material = defaultGlassMaterial;

        // Återställ position och rotation
        object.position.copy(position);
        object.rotation.copy(rotation);

        updatedCount++;
        debug(`Uppdaterade material för generell glasruta: ${object.name} till global typ: ${config.glassType}`);
      }
    });

    debug(`Uppdaterade material för totalt ${updatedCount} glasrutor`);

    // Rendera scenen för att visa ändringarna
    if (renderer && scene && camera) {
      renderer.render(scene, camera);
    }
  }

  /**
   * Visar 3D-preview
   */
  function show() {
    debug("Visar 3D-preview");

    try {
      // Steg 1: Säkerställ att 2D-preview är uppdaterad
      updatePreview2D()
        .then(() => {
          // Steg 2: Vänta en kort stund för att säkerställa att DOM är uppdaterad
          return new Promise(resolve => setTimeout(resolve, 50));
        })
        .then(() => {
          // Steg 3: Uppdatera konfigurationen från aktuella värden
          const newConfig = getCurrentConfig();
          updateConfig(newConfig);

          // Steg 4: Initiera eller uppdatera 3D-modellen
          if (!isInitialized) {
            debug("Initialiserar 3D-preview");
            initialize();
          } else {
            debug("3D-preview redan initialiserad, uppdaterar modellen");
            createWindow();
          }

          // Steg 5: Visa 3D-preview och dölj 2D-preview
          togglePreviewVisibility(true);

          // Steg 6: Uppdatera storleken och rendera scenen
          onWindowResize();

          // Steg 7: Schemalägg flera renderingar för att säkerställa korrekt visning
          scheduleMultipleRenderings();

          // Steg 8: Säkerställ att kontinuerlig uppdatering är igång
          if (typeof window._3dUpdateInterval === 'undefined') {
            startContinuousUpdate();
          }
        })
        .catch(error => {
          debug(`Fel vid visning av 3D-preview: ${error.message}`, 'error', error);
        });
    } catch (error) {
      debug(`Oväntat fel vid visning av 3D-preview: ${error.message}`, 'error', error);
    }
  }

  /**
   * Uppdaterar 2D-preview om möjligt
   * @returns {Promise} Promise som löses när uppdateringen är klar
   */
  function updatePreview2D() {
    return new Promise((resolve, reject) => {
      debug("Försöker uppdatera 2D-preview");

      if (window.PreviewRenderer && typeof window.PreviewRenderer.renderPreview === 'function') {
        try {
          window.PreviewRenderer.renderPreview();
          debug("2D-preview uppdaterad");
          resolve();
        } catch (error) {
          debug(`Kunde inte uppdatera 2D-preview: ${error.message}`, 'warn', error);
          resolve(); // Fortsätt ändå
        }
      } else {
        debug("PreviewRenderer inte tillgänglig, fortsätter utan uppdatering", 'warn');
        resolve();
      }
    });
  }

  /**
   * Växlar synlighet mellan 2D och 3D preview
   * @param {boolean} show3D - Om 3D-preview ska visas (true) eller 2D-preview (false)
   */
  function togglePreviewVisibility(show3D) {
    debug(`Växlar preview-synlighet: ${show3D ? '3D' : '2D'}`);

    const preview2d = document.getElementById('preview');
    const preview3d = document.getElementById('preview3d');
    const viewLabel = document.getElementById('viewLabel');
    const toggleBtn = document.getElementById('toggle3dPreview');

    if (show3D) {
      // Visa 3D, dölj 2D
      if (preview2d) {
        preview2d.classList.add('hidden');
        debug("2D-preview dold");
      }

      if (preview3d) {
        preview3d.classList.remove('hidden');
        debug("3D-preview visad");
      } else {
        debug("3D-preview element hittades inte!", 'error');
      }

      // Visa etiketten men inte kontrollerna (borttagna från HTML)
      if (viewLabel) {
        viewLabel.textContent = 'Insida'; // Sätt alltid till insida
        viewLabel.classList.remove('hidden');
      }

      if (toggleBtn && toggleBtn.querySelector('span')) {
        toggleBtn.querySelector('span').textContent = '2D-vy';
      }

      window.Preview3D.isVisible = true;
    } else {
      // Visa 2D, dölj 3D
      if (preview2d) {
        preview2d.classList.remove('hidden');
        debug("2D-preview visad");
      }

      if (preview3d) {
        preview3d.classList.add('hidden');
        debug("3D-preview dold");
      }

      // Dölj etiketten
      if (viewLabel) viewLabel.classList.add('hidden');

      if (toggleBtn && toggleBtn.querySelector('span')) {
        toggleBtn.querySelector('span').textContent = '3D-vy';
      }

      window.Preview3D.isVisible = false;
    }
  }

  /**
   * Schemalägger flera renderingar för att säkerställa korrekt visning
   */
  function scheduleMultipleRenderings() {
    debug("Schemalägger flera renderingar");

    const renderTimes = [100, 300, 500, 1000]; // Millisekunder

    renderTimes.forEach((time, index) => {
      setTimeout(() => {
        if (!renderer || !scene || !camera) {
          debug("Renderer, scene eller camera saknas, kan inte rendera", 'warn');
          return;
        }

        onWindowResize();
        if (controls) controls.update();
        renderer.render(scene, camera);

        debug(`Rendering ${index + 1}/${renderTimes.length} utförd efter ${time}ms`);

        // Vid sista renderingen, uppdatera konfigurationen igen
        if (index === renderTimes.length - 1) {
          const finalConfig = getCurrentConfig();
          updateConfig(finalConfig);
          createWindow(); // Återskapa fönstret med den senaste konfigurationen

          onWindowResize();
          if (controls) controls.update();
          renderer.render(scene, camera);
          debug("Final rendering utförd med uppdaterad konfiguration");
        }
      }, time);
    });
  }

  /**
   * Döljer 3D-preview
   */
  function hide() {
    debug("Döljer 3D-preview");

    try {
      // Växla till 2D-preview
      togglePreviewVisibility(false);

      // Uppdatera 2D-preview efter en kort fördröjning
      setTimeout(() => {
        updatePreview2D()
          .then(() => {
            debug("2D-preview uppdaterad efter byte från 3D");
          })
          .catch(error => {
            debug(`Fel vid uppdatering av 2D-preview: ${error.message}`, 'warn', error);
          });
      }, 50);
    } catch (error) {
      debug(`Fel vid döljning av 3D-preview: ${error.message}`, 'error', error);
    }
  }

  /**
   * Återskapar fönstermodellen med aktuell konfiguration
   * Används för att uppdatera modellen när färger eller andra egenskaper ändras
   */
  function recreateModel() {
    debug("Återskapar fönstermodellen med aktuell konfiguration");

    try {
      // Anropa createWindow för att återskapa modellen
      createWindow();

      // Uppdatera storleken och rendera scenen
      onWindowResize();

      // Forcera en rendering
      if (renderer && scene && camera) {
        renderer.render(scene, camera);
      }

      debug("Fönstermodellen återskapad");
    } catch (error) {
      debug(`Fel vid återskapande av fönstermodellen: ${error.message}`, 'error', error);
    }
  }
})();
