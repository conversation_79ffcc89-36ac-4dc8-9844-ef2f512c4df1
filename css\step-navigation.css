/* CSS för steg-navigering */

/* G<PERSON>r sektionsrubriker klickbara */
.builder-section h2 {
  position: relative;
  cursor: pointer;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  transition: background-color 0.2s ease;
}

.builder-section h2:hover {
  background-color: #2a2a2a;
}

/* Lägg till en pil-ikon som indikerar expanderbart innehåll */
.builder-section h2::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0.8rem;
  height: 0.8rem;
  border-right: 2px solid var(--color-secondary);
  border-bottom: 2px solid var(--color-secondary);
  transition: transform 0.3s ease;
}

.builder-section.collapsed h2::after {
  transform: translateY(-50%) rotate(-45deg);
}

.builder-section:not(.collapsed) h2::after {
  transform: translateY(-50%) rotate(45deg);
}

/* Hover-effekt för sektionsrubriker */
.builder-section h2:hover::after {
  border-color: var(--color-primary);
}

/* <PERSON><PERSON>ra <PERSON>/stängning av sektioner */
.builder-section .section-body {
  transition: max-height 0.3s ease, opacity 0.3s ease;
  max-height: 2000px;
  opacity: 1;
  overflow: hidden;
}

.builder-section.collapsed .section-body {
  max-height: 0;
  opacity: 0;
  padding: 0;
}

/* Aktiv sektion */
.builder-section:not(.collapsed) h2 {
  background-color: #2a2a2a;
  color: var(--color-primary);
}

/* Förbättra steg-navigeringen */
.step-nav .step {
  position: relative;
  overflow: hidden;
}

.step-nav .step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  opacity: 0;
  transform: scale(0);
  border-radius: 50%;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.step-nav .step:hover::before {
  opacity: 0.2;
  transform: scale(1);
}

.step-nav .step.active::before {
  opacity: 0;
}
